<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Artikon</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>
<body>
    <div class="tbl" id="content">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                         <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">07/01/2025</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
					<div class="middle_section bone_cut InitialPage">
                        <div class="profile">
                            <ul>
                                <li>Distal Femur Cut</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center justify-content-center">
                                        <div class="col-6">
                                            <div class="Bone-box-style pb-80">

                                                <img src="../static/images/femur/distal-femur-cut.jpg" class="img-fluid distal-femur-cut-img_large" alt="">

                                            </div>
                                        </div>
                                    </div>
									<div class="note">
										<p class="note_txt">Notes:</p>
										<p>
											<ul>
												<li>Press pedal to activate robotic cutting </li>
											</ul>
										</p>
									</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="middle_section bone_cut GREEN d-none">
                        <div class="profile">
                            <ul>
                                <li>Distal Femur Cut</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center justify-content-center">
                                        <div class="col-6">
                                            <div class="Bone-box-style pb-80">

                                                <img src="../static/images/femur/distal-femur-cut_green.jpg" class="img-fluid distal-femur-cut-img_large" alt="">

                                            </div>
                                        </div>
                                    </div>
									<div class="note">
										<p class="note_txt">Notes:</p>
										<p>
											<ul>
												<li>Press pedal to activate robotic cutting </li>
											</ul>
										</p>
									</div>
                                </div>

                            </div>
                        </div>
                    </div>
					<div class="middle_section bone_cut RED d-none">
                        <div class="profile">
                            <ul>
                                <li>Distal Femur Cut</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center justify-content-center">
                                        <div class="col-6">
                                            <div class="Bone-box-style pb-80">

                                                <img src="../static/images/femur/distal-femur-cut_red.jpg" class="img-fluid distal-femur-cut-img_large" alt="">

                                            </div>
                                        </div>
                                    </div>
									<div class="note">
										<p class="note_txt">Notes:</p>
										<p>
											<ul>
												<li>Press pedal to activate robotic cutting</li>
											</ul>
										</p>
									</div>

                                </div>

                            </div>
                        </div>
                    </div>
				</div>
				
				<div class="bottom_btn">
					<div class="btn">
						Robot in position

					</div>
					<div class="blank"></div>
					<div class="btn">
						<a onclick="handleBackClick()">
							<span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back
						</a>
						<script>
							function handleNextClick() {
								const selectedBone = localStorage.getItem('selectedBone');

								if (selectedBone === 'femur') {
									window.location.href = "inner-page.html";
								} else {
									window.location.href = "tibia-inner-page3.html?bone=tibia";
								}
							}
						</script>
					</div>
					<div class="btn">
						<a href="femur-inner-page5.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
					</div>
				</div>
								
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#">
                                    <span><img src="../static/images/home.png" /></span>Main Menu
                                </a>
                            </li>
                            <li class="footer_btn_three">
                                <a href="#">
                                    <span><img src="../static/images/union.png" /></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            function redirectToPage(bone) {
                localStorage.setItem("selectedBone", bone); // Save selected bone

            }
        });
    </script>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>

<script type="text/javascript">

let socket;

function startServer() {
    // Check if the socket is already opened
        if (!socket || socket.readyState !== WebSocket.OPEN) {
        const currentUrl = window.location.href;
            const pageName = currentUrl.substring(currentUrl.lastIndexOf('/') + 1).split('?')[0];
        socket = new WebSocket('ws://127.0.0.1:8000/ws');

        socket.onopen = function(event) {
            console.log('Socket opened for ');

            socket.send(JSON.stringify({ file: pageName }));
        };

        socket.onmessage = function(event) {
            const values = event.data.split(',');
            const abbreviation = values[0];
            const data1 = values[1];
            const data2 = values[2];
            const data3 = values[3];
			console.log(abbreviation);
            // Select the GREEN and RED elements
            const greenElement = document.querySelector('.middle_section.bone_cut.GREEN');
            const redElement = document.querySelector('.middle_section.bone_cut.RED');
            const initElement = document.querySelector('.middle_section.bone_cut.InitialPage');

            // Toggle sections based on the abbreviation
            if (abbreviation === 'GREEN') {
                greenElement.classList.remove('d-none');
                redElement.classList.add('d-none');
                initElement.classList.add('d-none');
            } else if (abbreviation === 'RED') {
                redElement.classList.remove('d-none');
                greenElement.classList.add('d-none');
				initElement.classList.add('d-none');
            }

            // Redirect to a new page based on abbreviation
            if (abbreviation === 'exit') {
                window.location.href = projectUrl + 'femur-inner-page5.html';
            }
        };

        socket.onerror = function(error) {
            console.error('Socket error:', error);
        };

        socket.onclose = function(event) {
            console.log('Socket closed:', event);
            // Optionally, attempt to reconnect here
            setTimeout(startServer, 1000); // Attempt to reconnect after 1 second
        };
    }
}

// Click event for minus button
$('.minus-button').on('click', function() {
    var span = $(this).closest('.input-btn').find('span').first();
    var currentValue = parseFloat(span.text());
    if (!isNaN(currentValue)) {
        span.text((currentValue - 0.5).toFixed(1));
    }
});

// Click event for plus button
$('.plus-button').on('click', function() {
    var span = $(this).closest('.input-btn').find('span').first();
    var currentValue = parseFloat(span.text());
    if (!isNaN(currentValue)) {
        span.text((currentValue + 0.5).toFixed(1));
    }
});

// Camera functionality
    document.addEventListener('DOMContentLoaded', function() {
        const cameraButton = document.querySelector('.footer_btn_one .btn.first');
        const cameraModal = document.getElementById('cameraModal');
        const closeCamera = document.querySelector('.close-camera');
        const videoStream = document.getElementById('videoStream');
        let mediaStream = null;

        // Function to start the camera
        async function startCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });
                mediaStream = stream;
                videoStream.srcObject = stream;
                videoStream.play();
            } catch (err) {
                console.error('Error accessing camera:', err);
            }
        }

        // Function to stop the camera
        function stopCamera() {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                videoStream.srcObject = null;
                mediaStream = null;
            }
        }

        // Open camera modal
        cameraButton.addEventListener('click', function(e) {
            e.preventDefault();
            cameraModal.style.display = 'block';
            startCamera();
        });

        // Close camera modal
        closeCamera.addEventListener('click', function() {
            cameraModal.style.display = 'none';
            stopCamera();
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === cameraModal) {
                cameraModal.style.display = 'none';
                stopCamera();
            }
        });
    });

// Start the server automatically on page load
window.onload = function() {
    startServer();
};

// Add handleBackClick function
function handleBackClick() {
    const selectedBone = localStorage.getItem('selectedBone');
    if (selectedBone === 'femur') {
        window.location.href = "inner-page.html";
    } else {
        window.location.href = "tibia-inner-page3.html";
    }
}

</script>



</html>