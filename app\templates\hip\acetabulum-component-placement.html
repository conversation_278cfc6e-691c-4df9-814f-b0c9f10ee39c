<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="/static/css/style.css">
    <link rel="styl~esheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Acetabulum Component Placement</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>

                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center align-items-start">
                                        <div class="col-6">
                                            <div class="grid STEP0">
                                                <img src="../static/images/hip-bone/Re-Register_Acetabulum_0.jpg" class="img-fluid" alt="">
                                            </div>
                                            <div class="grid FO d-none">
                                                <img src="../static/images/hip-bone/Re-Register_Acetabulum_1_G.jpg" class="img-fluid" alt="">
                                            </div>
                                            <!--div class="grid acetabulum-component-placement-STEP1A d-none">
                                                <img src="../static/images/hip-bone/Re-Register_Acetabulum_1.jpg" class="img-fluid" alt="">
                                            </div-->
                                            <div class="grid SU d-none">
                                                <img src="../static/images/hip-bone/Re-Register_Acetabulum_2.jpg" class="img-fluid" alt="">
                                            </div>
                                            <div class="grid CA d-none">
                                                <img src="../static/images/hip-bone/Re-Register_Acetabulum_3.jpg" class="img-fluid" alt="">
                                            </div>
                                            <div class="grid AT d-none">
                                                <img src="../static/images/hip-bone/Re-Register_Acetabulum_4.jpg" class="img-fluid" alt="">
                                            </div>
                                             <div class="grid PT d-none">
                                                <img src="../static/images/hip-bone/Re-Register_Acetabulum_5.jpg" class="img-fluid" alt="">
                                            </div>
                                        </div>
                                        <div class="col-6 points-acetabulum-box">
                                            <div class="note new-note text-start">
                                                <p class="note_txt fw-normal mb-0 turquoise-blue FO acetabulum-component-placement-STEP1A">A: Register Fovea For Intial Hip Centre</p>
                                            </div>
                                            <div class="note new-note text-start mt-3">
                                                <p class="note_txt fw-normal turquoise-blue">B: Register 4 Points On Acetabulum</p>
                                                <P class="SU">(1)  Superior</P>
                                                <P class="CA">(2)  Caudal</P>
                                                <P class="AT">(3)  Anterior Tal</P>
                                                <P class="PT">(4)  Posterior Tal</P>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="btn green_border pointer_bg">
                                        <span class="mr-20"><img src="../static/images/icon/check.png" /></span>Pointer
                                    </div>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a id="backBtn" href="final-cup-position.html"><span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back</a>
                                    </div>
                                    <div class="btn">
                                        <a id="nextBtn" href="TP2_marking.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
							   <a id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
							</li>
						   <li class="footer_btn_three">
								<a id="powerbuttonLink">
									<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
								</a>
								<script>
									document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
										event.preventDefault();  // Prevent default anchor behavior
										window.location.href = '/';  // Redirect to root path
									});
								</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>


<script type="text/javascript">
    
    let socket;
    let delay = 100; // Initial delay of 1 second
    let startFetching = false; // Flag to indicate when to start fetching data

    function startServer(index) {
        
        if (!socket || socket.readyState !== WebSocket.OPEN) {
        const currentUrl = window.location.href;
            const pageName = currentUrl.substring(currentUrl.lastIndexOf('/') + 1).split('?')[0];
        socket = new WebSocket('ws://127.0.0.1:8000/ws');

        socket.onopen = function(event) {
            console.log('Socket opened for ' + index);

            socket.send(JSON.stringify({ file: pageName }));
        };

        socket.onmessage = function(event) {
            const values = event.data.split(',');
            const abbreviation = values[0];
            const data = values[1]; 
       
			
			
			// Hide all sections
			$('.grid').addClass('d-none');
			$('.grid.' + values[0]).removeClass('d-none');
			if(values[0] == 'FO'){
				$("." + values[0]).removeClass('text-orange');
				$("." + values[0]).addClass('text-success');
			}
			else if(values[0] == 'SU' || values[0] == 'CA' || values[0] == 'AT' || values[0] == 'PT'){
				$("." + values[0]).addClass('yellow');
			}
			
			// Get the current URL
			var currentUrl = window.location.href;
			// Remove the page name from the URL
			var projectUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);
			if (abbreviation == 'PT') {
				setTimeout(function() {
					window.location.href = projectUrl + 'TP2_marking.html';
				}, 1000); // Delay of 3000 milliseconds (1 seconds)
				socket.close();
			}
        };

        socket.onerror = function(error) {
            console.error('Socket error:', error);
        };
		
		socket.onclose = function(event) {
				console.log('Socket closed. Attempting to reconnect...');
				// Optionally implement reconnection logic here
				setTimeout(() => startServer(index),2000); // Attempt to reconnect after 1 second
			};
      }
    };
	function handleNavigation(event) {
		event.preventDefault();
		const targetUrl = event.currentTarget.href;

		if (socket && socket.readyState === WebSocket.OPEN) {
			socket.send(JSON.stringify({ file: " " }));
			socket.addEventListener('close', function () {
				window.location.href = targetUrl;
			}, { once: true });

			socket.close();
		} else {
			window.location.href = targetUrl;
		}
	};
	document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
    document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);
	
	// Camera functionality
    document.addEventListener('DOMContentLoaded', function() {
        const cameraButton = document.querySelector('.footer_btn_one .btn.first');
        const cameraModal = document.getElementById('cameraModal');
        const closeCamera = document.querySelector('.close-camera');
        const videoStream = document.getElementById('videoStream');
        let mediaStream = null;

        // Function to start the camera
        async function startCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });
                mediaStream = stream;
                videoStream.srcObject = stream;
                videoStream.play();
            } catch (err) {
                console.error('Error accessing camera:', err);
            }
        }

        // Function to stop the camera
        function stopCamera() {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                videoStream.srcObject = null;
                mediaStream = null;
            }
        }
		
        // Open camera modal
        cameraButton.addEventListener('click', function(e) {
            e.preventDefault();
            cameraModal.style.display = 'block';
            startCamera();
        });

        // Close camera modal
        closeCamera.addEventListener('click', function() {
            cameraModal.style.display = 'none';
            stopCamera();
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === cameraModal) {
                cameraModal.style.display = 'none';
                stopCamera();
            }
        });
    });

    //  Start the server automatically on page load
     window.onload = function() {
        startServer('Register_Acetabulum');
    };

</script>
</html>