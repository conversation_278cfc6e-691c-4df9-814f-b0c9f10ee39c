// create a function to update the date and time
function updateTime() {
  // create a new `Date` object
  const now = new Date();

  const currentDate = now.getDate()+"/"+(now.getMonth()+1)+"/"+ now.getFullYear();
  document.querySelector('#date').textContent = currentDate;

  // get the current time as a string
  const currentTime = now.getHours()+":"+now.getMinutes()+":"+ now.getSeconds();

  // update the `textContent` property of the `span` element with the `id` of `datetime`
  document.querySelector('#time').textContent = currentTime;
}

// call the `updateDateTime` function every second
setInterval(updateTime, 1000);

// Year dynamic
$('#year').html(new Date().getFullYear());