#!/usr/bin/env python
import json

import sys
import threading
import pickle
import msvcrt
import os
import cv2
import time
import numpy as np
from ctypes import *
import sys
import os

from matplotlib import pyplot as plt
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
# Get the current directory of the script
current_dir = os.path.dirname(os.path.abspath(__file__))
# Construct the absolute path to 'general/camera/MvImport'
mv_import_path = os.path.join(current_dir, 'general', 'camera', 'MvImport')
# Append the constructed path to sys.path
sys.path.append(mv_import_path)
from general.camera.MvCameraControl_class import *
from general.common.tracker_object import TriangleTracker, FemureTracker
from general.common import utils
from general.common import calibration

g_bExit = False

#
# # SpineMarking/registration_data/PointCloud.pickle
# current_dir = os.path.dirname(__file__)
# xml_path = os.path.join(current_dir, '../camera/Calibration_data/stereoCalibration.xml')
# import pyvista as pv

try:
    with open('registration_data/PointCloud.pickle', 'rb') as handle:
        points = pickle.load(handle)
except Exception as e:
    print(f"Error loading point cloud data: {e}")
C_values = [value['C'].tolist() for key, value in points.items()]

C_values_np = np.array(C_values)  # Convert to numpy array


def fit_plane(point_cloud):
    """
    input
        point_cloud : list of xyz values　numpy.array
    output
        plane_v : (normal vector of the best fit plane)
        com : center of mass
    """

    com = np.sum(point_cloud, axis=0) / len(point_cloud)
    # calculate the center of mass
    q = point_cloud - com
    # move the com to the origin and translate all the points (use numpy broadcasting)
    Q = np.dot(q.T, q)
    # calculate 3x3 matrix. The inner product returns total sum of 3x3 matrix
    la, vectors = np.linalg.eig(Q)
    # Calculate eigenvalues and eigenvectors
    plane_v = vectors.T[np.argmin(la)]
    # Extract the eigenvector of the minimum eigenvalue

    return plane_v, com


normal_vector, com1 = fit_plane(C_values_np)





def plot_points_and_plane(point_cloud, plane_v, com):
    # Create the figure and axis
    fig = plt.figure()
    ax = fig.add_subplot(111, projection='3d')

    # Plot the point cloud
    ax.scatter(point_cloud[:, 0], point_cloud[:, 1], point_cloud[:, 2], color='b', label='Point Cloud')

    # Create a grid to represent the plane
    d = np.linspace(-1, 1, 10)
    x_grid, y_grid = np.meshgrid(d, d)

    # Calculate the corresponding z for the plane using the plane equation
    z_grid = (-plane_v[0] * (x_grid - com[0]) - plane_v[1] * (y_grid - com[1])) / plane_v[2] + com[2]

    # Plot the plane
    ax.plot_surface(x_grid + com[0], y_grid + com[1], z_grid, color='r', alpha=0.5)

    # Set labels and title
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.set_title('Point Cloud and Best Fit Plane')

    # Show the plot
    plt.show()

# plot_points_and_plane(C_values_np, normal_vector, com1)

# from sklearn.decomposition import PCA
# # Step 1: Apply PCA to find the plane that best fits the points
# pca = PCA(n_components=3)
# pca.fit(C_values_np)
#
# # Step 2: The normal vector of the plane is the last principal component
# normal_vector = pca.components_[-1]
# #
# # Step 3: The centroid of the points
# centroid = np.mean(C_values_np, axis=0)
#
# # Step 4: Define the plane equation: a*x + b*y + c*z + d = 0
# a, b, c = normal_vector
# d = -centroid.dot(normal_vector)
#
# # Step 5: Create a meshgrid for the plane
# xx, yy = np.meshgrid(np.linspace(min(C_values_np[:, 0]), max(C_values_np[:, 0]), 10),
#                      np.linspace(min(C_values_np[:, 1]), max(C_values_np[:, 1]), 10))
#
# # Calculate the z-values for the plane
# zz = (-a * xx - b * yy - d) / c
#
# # Step 6: Plot the points and the fitted plane
# fig = plt.figure()
# ax = fig.add_subplot(111, projection='3d')
#
# # Plot the points
# ax.scatter(C_values_np[:, 0], C_values_np[:, 1], C_values_np[:, 2], color='r', label='Data Points')
#
# # Plot the plane
# ax.plot_surface(xx, yy, zz, color='cyan', alpha=0.5, rstride=100, cstride=100)
#
# ax.set_xlabel('X')
# ax.set_ylabel('Y')
# ax.set_zlabel('Z')
# ax.legend()
#
# plt.show()

#
# # Extract the C values into a list of lists and convert to numpy array
# C_values = [value['C'].tolist() for key, value in points.items()]
# # for i in C_values:
# #     print(f'{i}')
# C_values_np = np.array(C_values)  # 100 random 3D points as an example
#
# # Create the point cloud in pyvista
# point_cloud = pv.PolyData(C_values_np)
#
# # Create a plotter object (interactive mode)
# plotter = pv.Plotter(off_screen=False)
#
# # Add the original points to the scene with increased point size
# plotter.add_mesh(point_cloud, color='white', point_size=10, render_points_as_spheres=True)
#
# # Set the camera to the xy-plane view
# plotter.view_xy()
#
# # Show the interactive plot
# plotter.show()


def angle_between_vectors(v1, v2):
    # Project both vectors into the xz-plane (ignore the y-component)
    v1_proj = np.array([v1[0], 0, v1[2]])
    v2_proj = np.array([v2[0], 0, v2[2]])

    # Calculate the dot product and magnitudes
    dot_product = np.dot(v1_proj, v2_proj)
    mag_v1 = np.linalg.norm(v1_proj)
    mag_v2 = np.linalg.norm(v2_proj)

    # Calculate the angle in radians and convert to degrees
    angle_rad = np.arccos(dot_product / (mag_v1 * mag_v2))
    angle_deg = np.degrees(angle_rad)

    return angle_deg


def work_thread(cam=0, pData=0, nDataSize=0, cam2=0, pData2=0, nDataSize2=0, framecount=0, normal_vector=None):
    variable_dict = {}
    stFrameInfo = MV_FRAME_OUT_INFO_EX()
    memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))

    pointer_data = []
    while True:
        ret = cam.MV_CC_GetOneFrameTimeout(pData, nDataSize, stFrameInfo, 500)
        ret2 = cam2.MV_CC_GetOneFrameTimeout(pData2, nDataSize2, stFrameInfo, 500)

        if ret == 0 and ret2 == 0:

            # Name the images left and right taking into consideration the opening streams.

            img_right = np.frombuffer(bytes(pData._obj), np.uint8).reshape((stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
            img_left = np.frombuffer(bytes(pData2._obj), np.uint8).reshape((stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

            # Rectify images

            rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)

            # Detect contours on both frames

            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)

            if len(detections_right) == 6 and 6 == len(detections_left):
                detections_right = sorted(detections_right, key=lambda x: x[0])
                detections_left = sorted(detections_left, key=lambda x: x[0])
                # Extract first 3 coordinates
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]
                next_three_right = detections_right[-3:]
                next_three_left = detections_left[-3:]

                Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                 rec_img_left)
                Pointer_traker.find_depth()
                Leds = Pointer_traker.geLEDcordinate()
                print(f' Leds {Leds}')
                pointer_tip_gcs = Pointer_traker.getStylusPoint()

                vector2 = np.array(Leds[2]) - np.array(pointer_tip_gcs)

                Angle = angle_between_vectors(vector2, normal_vector)
                print(f'Angle {Angle}')
                time.sleep(0.1)
                framecount = framecount + 1
                continue
        else:
            print("no data[0x%x]" % ret)

        if framecount == 25:
            print("All points registered.")

            with open('SpineMarking/registration_data/PointCloud.pickle', 'wb') as handle:
                pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

            print("Pickle dump successful")

            break

        if g_bExit == True:
            break


if __name__ == "__main__":

    deviceList = MV_CC_DEVICE_INFO_LIST()
    tlayerType = MV_GIGE_DEVICE | MV_USB_DEVICE

    # ch:枚举设备 | en:Enum device
    ret = MvCamera.MV_CC_EnumDevices(tlayerType, deviceList)
    if ret != 0:
        print("enum devices fail! ret[0x%x]" % ret)
        sys.exit()

    if deviceList.nDeviceNum == 0:
        print("find no device!")
        sys.exit()

    print("Find %d devices!" % deviceList.nDeviceNum)

    for i in range(0, deviceList.nDeviceNum):
        mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
        if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE:
            print("\ngige device: [%d]" % i)
            strModeName = ""
            for per in mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName:
                strModeName = strModeName + chr(per)
            print("device model name: %s" % strModeName)

            nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
            nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
            nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
            nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
            print("current ip: %d.%d.%d.%d\n" % (nip1, nip2, nip3, nip4))
        elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
            print("\nu3v device: [%d]" % i)
            strModeName = ""
            for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName:
                if per == 0:
                    break
                strModeName = strModeName + chr(per)
            print("device model name: %s" % strModeName)

            strSerialNumber = ""
            for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                if per == 0:
                    break
                strSerialNumber = strSerialNumber + chr(per)
            print("user serial number: %s" % strSerialNumber)

    nConnectionNum = 0  # For device based selections.

    if int(nConnectionNum) >= deviceList.nDeviceNum:
        print("input error!")
        sys.exit()

    # ch:创建相机实例 | en:Creat Camera Object
    cam = MvCamera()
    cam2 = MvCamera()
    # ch:选择设备并创建句柄 | en:Select device and create handle
    stDeviceList = cast(deviceList.pDeviceInfo[int(nConnectionNum)], POINTER(MV_CC_DEVICE_INFO)).contents
    stDeviceList2 = cast(deviceList.pDeviceInfo[int(1)], POINTER(MV_CC_DEVICE_INFO)).contents

    ret = cam.MV_CC_CreateHandle(stDeviceList)
    ret2 = cam2.MV_CC_CreateHandle(stDeviceList2)

    if ret != 0:
        print("create handle fail! ret[0x%x]" % ret)
        sys.exit()

    # ch:打开设备 | en:Open device
    ret = cam.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
    ret2 = cam2.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)

    if ret != 0:
        print("open device fail! ret[0x%x]" % ret)
        sys.exit()

    # ch:探测网络最佳包大小(只对GigE相机有效) | en:Detection network optimal package size(It only works for the GigE camera)
    if stDeviceList.nTLayerType == MV_GIGE_DEVICE:
        nPacketSize = cam.MV_CC_GetOptimalPacketSize()
        if int(nPacketSize) > 0:
            ret = cam.MV_CC_SetIntValue("GevSCPSPacketSize", nPacketSize)
            if ret != 0:
                print("Warning: Set Packet Size fail! ret[0x%x]" % ret)
        else:
            print("Warning: Get Packet Size fail! ret[0x%x]" % nPacketSize)

    # Second device
    if stDeviceList2.nTLayerType == MV_GIGE_DEVICE:
        nPacketSize2 = cam2.MV_CC_GetOptimalPacketSize()
        if int(nPacketSize2) > 0:
            ret2 = cam2.MV_CC_SetIntValue("GevSCPSPacketSize", nPacketSize2)
            if ret2 != 0:
                print("Warning: Set Packet Size fail! ret[0x%x]" % ret2)
        else:
            print("Warning: Get Packet Size fail! ret[0x%x]" % nPacketSize2)

    stBool = c_bool(False)

    ret = cam.MV_CC_GetBoolValue("AcquisitionFrameRateEnable", stBool)
    ret2 = cam2.MV_CC_GetBoolValue("AcquisitionFrameRateEnable", stBool)

    if ret != 0:
        print("get AcquisitionFrameRateEnable fail! ret[0x%x]" % ret)
        sys.exit()

    # ch:设置触发模式为off | en:Set trigger mode as off
    ret = cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)
    ret2 = cam2.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)

    if ret != 0:
        print("set trigger mode fail! ret[0x%x]" % ret)
        sys.exit()

    # ch:获取数据包大小 | en:Get payload size
    stParam = MVCC_INTVALUE()
    memset(byref(stParam), 0, sizeof(MVCC_INTVALUE))

    ret = cam.MV_CC_GetIntValue("PayloadSize", stParam)
    ret2 = cam2.MV_CC_GetIntValue("PayloadSize", stParam)

    if ret != 0:
        print("get payload size fail! ret[0x%x]" % ret)
        sys.exit()
    nPayloadSize = stParam.nCurValue

    # ch:开始取流 | en:Start grab image
    ret = cam.MV_CC_StartGrabbing()
    ret2 = cam2.MV_CC_StartGrabbing()

    if ret != 0:
        print("start grabbing fail! ret[0x%x]" % ret)
        sys.exit()

    data_buf = (c_ubyte * nPayloadSize)()
    data_buf2 = (c_ubyte * nPayloadSize)()

    try:
        work_thread(cam, byref(data_buf), nPayloadSize, cam2, byref(data_buf2), nPayloadSize,
                    normal_vector=normal_vector)
        # hThreadHandle = threading.Thread(target=work_thread, args=(cam, byref(data_buf), nPayloadSize))
        # hThreadHandle.start()
    finally:
        # ch:停止取流 | en:Stop grab image
        ret = cam.MV_CC_StopGrabbing()
        ret2 = cam2.MV_CC_StopGrabbing()

        if ret != 0:
            print("stop grabbing fail! ret[0x%x]" % ret)
            del data_buf
            sys.exit()

        # ch:关闭设备 | Close device
        ret = cam.MV_CC_CloseDevice()
        ret2 = cam2.MV_CC_CloseDevice()

        if ret != 0:
            print("close deivce fail! ret[0x%x]" % ret)
            del data_buf
            sys.exit()

        # ch:销毁句柄 | Destroy handle
        ret = cam.MV_CC_DestroyHandle()
        ret2 = cam2.MV_CC_DestroyHandle()

        if ret != 0:
            print("destroy handle fail! ret[0x%x]" % ret)
            del data_buf

            sys.exit()

        del data_buf
        del data_buf2
