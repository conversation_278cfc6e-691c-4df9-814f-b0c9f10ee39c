import numpy as np
import re

def compute_similarity_transform(source_points, target_points):
    """
    Compute similarity transformation (rotation, translation, scaling)
    to align source_points to target_points using SVD (Umeyama method).

    Parameters:
    - source_points: (N, 3) numpy array
    - target_points: (N, 3) numpy array

    Returns:
    - transformation_matrix: (4, 4) numpy array, homogeneous transformation matrix
    - rotation_matrix: (3, 3)
    - translation_vector: (3,)
    - scale: scalar
    """

    source_points = np.asarray(source_points)
    target_points = np.asarray(target_points)

    assert source_points.shape == target_points.shape, "Shape mismatch between source and target points"
    N = source_points.shape[0]

    # Step 1: Compute centroids
    centroid_src = np.mean(source_points, axis=0)
    centroid_tgt = np.mean(target_points, axis=0)

    # Step 2: Center the points
    src_centered = source_points - centroid_src
    tgt_centered = target_points - centroid_tgt

    # Step 3: Compute covariance matrix
    H = src_centered.T @ tgt_centered / N

    # Step 4: SVD
    U, S, Vt = np.linalg.svd(H)
    R = Vt.T @ U.T

    # Handle reflection
    if np.linalg.det(R) < 0:
        Vt[2, :] *= -1
        R = Vt.T @ U.T

    # Step 5: Compute scale
    var_src = np.var(src_centered, axis=0).sum()
    scale = np.sum(S) / var_src

    # Step 6: Compute translation
    t = centroid_tgt - scale * R @ centroid_src

    # Step 7: Create homogeneous transformation matrix
    T = np.eye(4)
    T[:3, :3] = scale * R
    T[:3, 3] = t

    return T

def calculate_transformation_matrix_with_scaling(source, target):
    """Calculate transformation matrix with scaling from source points to target points."""
    # Calculate centroids
    centroid_source = np.mean(source, axis=0)
    centroid_target = np.mean(target, axis=0)

    # Center the points
    centered_source = source - centroid_source
    centered_target = target - centroid_target

    # Estimate scaling factor
    # scale=1
    scale = np.sum(np.linalg.norm(centered_target, axis=1)) / np.sum(
        np.linalg.norm(centered_source, axis=1)
    )

    # Apply scaling to source points
    scaled_source = centered_source * scale

    # Compute H matrix for SVD
    H = np.dot(scaled_source.T, centered_target)

    # Compute rotation matrix using SVD
    U, S, Vt = np.linalg.svd(H)
    R_matrix = np.dot(Vt.T, U.T)

    # Correct for improper rotation (reflection) if determinant is negative
    if np.linalg.det(R_matrix) < 0:
        Vt[-1, :] *= -1
        R_matrix = np.dot(Vt.T, U.T)

    # Compute translation vector
    t = centroid_target - np.dot(R_matrix, centroid_source * scale)

    # Construct the final transformation matrix
    transformation_matrix = np.eye(4)
    transformation_matrix[:3, :3] = R_matrix * scale
    transformation_matrix[:3, 3] = t

    return transformation_matrix

def predict_point(point, transformation_matrix):
    transformed_point = transformation_matrix @ np.append(point, 1)
    return transformed_point

# driver code
led_points = np.array([[488.61263277, 475.61115606,  325.22952438]])
robot_points = np.array([[0, 0, 0]])

# led_points = np.empty((0, 3))
# robot_points = np.empty((0, 3))
with open('D:\\SpineSurgery\\pythonProject\\partialKneeClibration_30_6_25_LHS_50_3.txt', 'r') as file:
    for line in file:
        match_led = re.search(r'LED\s*\[([^\]]+)\]', line)
        led_str = match_led.group(1)
        row = np.fromstring(led_str, sep=' ').reshape(1, -1)
        led_points = np.append(led_points, row, axis=0)

        match_robot = re.search(r'robot\s*\(([^)]+)\)', line)
        robot_str = match_robot.group(1)
        row = np.fromstring(robot_str, sep=', ').reshape(1, -1)
        robot_points = np.append(robot_points, row, axis=0)

# print('led points = ', led_points)
# print('robot points = ', robot_points)
led_points[:, [1, 2]]  = led_points[:, [2, 1]]

transformation_matrix = compute_similarity_transform(led_points, robot_points)
#transformation_matrix[:, [1, 2]]  = transformation_matrix[:, [2, 1]]

transformed_point = predict_point([543.74711088, 499.1812485 , 281.18018009], transformation_matrix)
print('\nled point = [489.06788695, 476.98340502, 324.01485757]')
transformed_point[[1, 2]]  = transformed_point[[2, 1]]
print('transformed point = ', transformed_point[:3])
print('expected robot point = [0, 0, 0]')

transformed_point = predict_point([499.05197083, 480.14345668, 263.48340469], transformation_matrix)
print('\nled point = [499.05197083, 480.14345668, 263.48340469]')
transformed_point[[1, 2]]  = transformed_point[[2, 1]]
print('transformed point = ', transformed_point[:3])
print('expected robot point = [403.930756, 81.497841, 156.532562]')
