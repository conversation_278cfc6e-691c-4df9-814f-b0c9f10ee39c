<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="../css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="../css/style.css">
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap system-setup-screen">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>System Setup</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="grid">
                                                <img src="../images/hip-bone/camp.png" class="img-fluid camp-img" alt="">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a href="operating-positio-screen.html"><span class="mr-20"><img src="../images/left-arrow.png" /></span>Back</a>
                                    </div>
                                    <div class="btn">
                                        <a href="femur-registration.html"><span class="mr-20">Next</span><img src="../images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>Place Pins In Iliac Crest. Position The Camera To Ensure Crest Tracker Is Aligned With Camera Fov</p>
                        </div>
                    </div>
                    <div class="check_box">
                        <div class="new">
                            <form>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question1" class="section-toggle APT"/>
                                    <label for="coupon_question1">Activate parvis Tracker</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question2" class="section-toggle AFT"/>
                                    <label for="coupon_question2">Activate Femur Tracker</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question3" class="section-toggle ACT"/>
                                    <label for="coupon_question3">Activate Crest Tracker</label>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="supine-img">
                        <img src="../images/hip-bone/supine.png" class="img-fluid" alt="">
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#">
                                    <span><img src="../images/home.png" /></span>Main Menu
                                </a>
                            </li>
                            <li class="footer_btn_three">
                                <a href="#">
                                    <span><img src="../images/union.png" /></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../js/common.js"></script>
<!-- <script type="text/javascript">
    let socket;

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
        // socket = new WebSocket('ws://**************:8080/' + index);
        socket = new WebSocket('ws://**************:8080/' + index);

        socket.onopen = function(event) {
          console.log('Socket opened for ' + index);
        };

        socket.onmessage = function(event) {
            console.log('Received message:', event.data);
            const values = event.data.split(',');

            const abbriviation = values[0];
            // Hide all sections
            $('.section-toggle.' + values[0]).attr( 'checked', true );

        };

        socket.onerror = function(error) {
          console.error('Socket error:', error);
        };
      }
    };

    // Start the server automatically on page load
    window.onload = function() {
      startServer('hip_system_setup');
    };

</script> -->

<!-- new js -->

<script type="text/javascript">

    let socket;
    let delay = 1000; // Initial delay of 1 second
    let startFetching = false; // Flag to indicate when to start fetching data

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
        // socket = new WebSocket('ws://**************:8080/' + index);
        socket = new WebSocket('ws://**************:8080/' + index);

        socket.onopen = function(event) {
          console.log('Socket opened for ' + index);
        };

        socket.onmessage = function(event) {
            console.log('Received message:', event.data);
            const values = event.data.split(',');
            const abbreviation = values[0];
            const data = values[1]; 

            if (startFetching && ['APT','AFT','ACT'].includes(abbreviation)) 
            {
                setTimeout(() => {

                    // Get the current URL
                    var currentUrl = window.location.href;
                    // Remove the page name from the URL
                    var projectUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);
                    if (abbreviation == 'ACT') {
                        window.location.href = projectUrl + 'femur-registration.html';
                    }                  

                    $('.section-toggle.' + abbreviation).attr( 'checked', true );
                   
                }, delay);

                delay += 1000; // Increment the delay for the next message
            }
     
            if (abbreviation === 'graph-screen-6') {
                startFetching = true; // Set flag to start fetching data
                console.log("start fetching");
            }
        };

        socket.onerror = function(error) {
          console.error('Socket error:', error);
        };
      }
    };

    // new js ends

    // Start the server automatically on page load
    window.onload = function() {
      startServer('hip_system_setup');
    };

</script>

</html>