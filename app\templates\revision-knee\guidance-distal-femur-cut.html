<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Artikon</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
		<link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                         <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">07/01/2025</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut resect_proximal">
                        <div class="profile">
                            <ul>
                                <li>Guidance Proximal Femur Cut</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="grid position-relative">
                                                <div class="sub-grid">
                                                    <div class="depth">
                                                        <div class="depth_bg">Varus / Valgus</div>
                                                        <div class="depth_btn">
                                                            <ul>
                                                                 <a class="minus-button cursor-pointer">
                                                                    <img src="../static/images/icon/minus-button.png" class="w-28" alt="">
                                                                </a>
                                                                <li id="femur_li-1">0</li>
                                                                <sup class="fs-14">0</sup>
<!--                                                                <span class="input-btn-text text-white">var</span>-->
                                                                <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                                <img src="../static/images/revision-tkr/guidance-distal-femur-cut/leg_bone_v2_front__00000.jpg" />
                                                <div class="txt left">
                                                    M
                                                </div>
                                                <div class="txt right">
                                                    L
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="grid position-relative">
                                                <div class="sub-grid">
                                                    <div class="depth btn_box_spacing">
                                                        <div class="depth_bg">Slope</div>
                                                        <div class="depth_btn">
                                                            <ul>
                                                                <a class="minus-button cursor-pointer">
                                                                    <img src="../static/images/icon/minus-button.png" class="w-28" alt="">
                                                                </a>
                                                                <li id="femur_li-2">3</li>
                                                                <sup class="fs-14">0</sup>
                                                                <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                                <img src="../static/images/revision-tkr/guidance-distal-femur-cut/leg_bone_v2_side__00072.jpg" />
                                                <div class="txt left">
                                                    A
                                                </div>
                                                <div class="txt right">
                                                    P
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="grid position-relative">
                                                <div class="sub-grid">
                                                    <div class="depth btn_box_spacing">
                                                        <div class="depth_bg">Depth of Resction</div>
                                                        <div class="depth_btn">
                                                            <ul>
                                                                <a class="minus-button cursor-pointer">
                                                                    <img src="../static/images/icon/minus-button.png" class="w-28" alt="">
                                                                </a>
                                                                <li id="femur_li-3">3</li>
																<span class="input-btn-text text-white">mm</span>
                                                                <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                                <img src="../static/images/femur/femur-cut-planning-3.png" />
                                                <div class="txt left">
                                                    M
                                                </div>
                                                <div class="txt right">
                                                    L
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">

                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="blank blank-none"></div>
                                    <div class="btn">
                                        <a id="backBtn" href="planning-distal-femur-cut.html">
                                            <span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back
                                        </a>
                                    </div>
                                    <div class="btn">
                                        <a id="nextBtn" href="revision-knee-burrFemur.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
								<a href="#" id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
                            </li>
                            <li class="footer_btn_three">
								<a href="#" id="powerbuttonLink">
									<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
								</a>
								<script>
									document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
										event.preventDefault();  // Prevent default anchor behavior
										window.location.href = '/';  // Redirect to root path
									});
								</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>
<script>
    let socket;

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            const currentUrl = window.location.href;
            const pageName = "rev" + currentUrl.substring(currentUrl.lastIndexOf('/') + 1).split('?')[0];
            socket = new WebSocket('ws://127.0.0.1:8000/ws');

            socket.onopen = function () {
                console.log('Socket opened for', pageName);
                socket.send(JSON.stringify({ file: pageName }));
            };

            socket.onmessage = function (event) {
<!--                const values = event.data.split(',');-->
<!--                const abbreviation = values[0];-->
<!--                const data = values[1];-->

<!--                if (abbreviation === 'guidance-proximal-tibia-cut-1') {-->
<!--                    $('#femur_li-1').text(data);-->
<!--                } else if (abbreviation === 'guidance-proximal-tibia-cut-2') {-->
<!--                    $('#femur_li-2').text(data);-->
<!--                } else if (abbreviation === 'guidance-proximal-tibia-cut-3') {-->
<!--                    $('#femur_li-3').text(data);-->
<!--                } else if (abbreviation === 'exit') {-->
<!--                    const projectUrl = window.location.href.substring(0, window.location.href.lastIndexOf('/') + 1);-->
<!--                    window.location.href = projectUrl + 'revision-knee-burr.html';-->
<!--                }-->
            };

            socket.onerror = function (error) {
                console.error('Socket error:', error);
            };
        }
    }

    function sendData(inputId, inputValue) {
        if (socket && socket.readyState === WebSocket.OPEN) {
            console.log("Payload to send:", JSON.stringify({ inputId, inputValue }));
            socket.send(JSON.stringify({ inputId, inputValue }));
            console.log(`Sent via WebSocket: ${inputId} = ${inputValue}`);
        } else {
            console.error("WebSocket not connected!");
        }
    }

    const inputRanges = {
        'femur_li-1': { min: -5, max: 5},  // Varus/Valgus
        'femur_li-2': { min: -5, max: 5 },   // Slope
        'femur_li-3': { min: 0, max: 10 }     // Depth of Resection
    };

    function handleButtonClick(change) {
        return function () {
            const $btn = $(this);
            const $ul = $btn.closest('ul');
            const $li = $ul.find('li[id^="femur_li-"]');
            const inputId = $li.attr('id');

            if (!inputId || !inputRanges[inputId]) return;

            const currentValue = parseFloat($li.text());
            if (!isNaN(currentValue)) {
                let newValue = currentValue + change;

                const range = inputRanges[inputId];
                newValue = Math.max(range.min, Math.min(range.max, newValue));

                if (newValue !== currentValue) {
                    $li.text(newValue.toFixed(1));
                    sendData(inputId, newValue.toFixed(1));
                }
            }
        };
    }

    document.addEventListener('DOMContentLoaded', function () {
        // Start WebSocket server connection
        startServer('Guidance_Proximal_Tibia_Cut');

        // Camera functionality
        const cameraButton = document.querySelector('.footer_btn_one .btn.first');
        const cameraModal = document.getElementById('cameraModal');
        const closeCamera = document.querySelector('.close-camera');
        const videoStream = document.getElementById('videoStream');
        let mediaStream = null;

        async function startCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });
                mediaStream = stream;
                videoStream.srcObject = stream;
                videoStream.play();
            } catch (err) {
                console.error('Error accessing camera:', err);
            }
        }

        function stopCamera() {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                videoStream.srcObject = null;
                mediaStream = null;
            }
        }

        cameraButton?.addEventListener('click', function (e) {
            e.preventDefault();
            cameraModal.style.display = 'block';
            startCamera();
        });

        closeCamera?.addEventListener('click', function () {
            cameraModal.style.display = 'none';
            stopCamera();
        });

        window.addEventListener('click', function (event) {
            if (event.target === cameraModal) {
                cameraModal.style.display = 'none';
                stopCamera();
            }
        });

        // Button click handlers
        $('.minus-button').on('click', handleButtonClick(-0.5));
        $('.plus-button').on('click', handleButtonClick(0.5));
    });
	
	function handleNavigation(event) {
    event.preventDefault();
    const targetUrl = event.currentTarget.href;

    if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({ file: " " }));
        socket.addEventListener('close', function () {
            window.location.href = targetUrl;
        }, { once: true });

        socket.close();
    } else {
        window.location.href = targetUrl;
    }
}
	
	document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
    document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);
	
</script>



</html>