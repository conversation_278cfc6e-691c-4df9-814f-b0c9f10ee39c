<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">07/01/2025</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page second_page">
                    <div class="middle_section bone_cut hip_in_flexion HIF">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Femur</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/knee-bone/femur-points/1-hip-in-flexion(hif).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                <ul>
                                   <li>Hold the Hip in 90 Degree Flexion</li>
                                </ul>
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut hip_center HC d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Femur</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row align-items-start">
                                        <div class="col-5">
                                            <div class="grid text-end">
                                                <img src="../static/images/knee-bone/femur-points/2-hip-centre(hc).jpg" class="hc-image" />
                                            </div>
                                        </div>
                                        <div class="col-5">
                                            <div class="grid buffer-img text-start">
                                                <img src="../static/images/knee-bone/sequence/Buffer002.jpg" class="img-fluid HC1" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer005.jpg" class="img-fluid HC2 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer008.jpg" class="img-fluid HC3 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer011.jpg" class="img-fluid HC4 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer014.jpg" class="img-fluid HC5 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer017.jpg" class="img-fluid HC6 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer020.jpg" class="img-fluid HC7 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer023.jpg" class="img-fluid HC8 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer026.jpg" class="img-fluid HC9 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer029.jpg" class="img-fluid HC10 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer032.jpg" class="img-fluid HC11 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer035.jpg" class="img-fluid HC12 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer038.jpg" class="img-fluid HC13 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer041.jpg" class="img-fluid HC14 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer044.jpg" class="img-fluid HC15 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer047.jpg" class="img-fluid HC16 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer050.jpg" class="img-fluid HC17 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer053.jpg" class="img-fluid HC18 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer056.jpg" class="img-fluid HC19 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer059.jpg" class="img-fluid HC20 d-none" alt="">
                                                <img src="../static/images/knee-bone/sequence/Buffer062.jpg" class="img-fluid HC21 d-none" alt="">
                                                <!-- <img src="../static/images/knee-bone/sequence/Buffer074.jpg" class="img-fluid buffer-img-22" alt=""> -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                <ul>
                                    <li>Slowly circumduct the femur with<br> changing radii avoiding pelvic<br> movement</li>
                                </ul>
                            </p>
                        </div>
                    </div>
                    	<div class="middle_section bone_cut femur_center FC d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Femur</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/knee-bone/femur-points/5-femur-center(fc).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                <ul>
                                    <li>Place Pointer at the center of trochlear<br> sulcus 1.0cm above PCL attachment<br> </li>
                                    <li>To Record, Press Button</li>
                                </ul>
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut medial_epicondyle ME d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Femur</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/knee-bone/femur-points/3-medial-epicondyle(me).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                <li>Place Pointer at the medial-epicondyle </li>
                                <li>To Record, Press Button</li>
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut lateral_epicondyle LE d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Femur</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/knee-bone/femur-points/4-lateral-epicondyle(le).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                <ul>
                                   <li>Place Pointer at the Lateral-epicondyle </li>
									<li>To Record, Press Button</li>
                                </ul>
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut femoral_ap_axis FAP d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Femur</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/knee-bone/femur-points/6-femur-ap-axis(fap).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                <ul>
                                    <li>Place Pointer along the Whiteside's line </li>
									<li>To Record, Press Button</li>
                                </ul>
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut medial_distal_condyle MDC d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Femur</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/knee-bone/femur-points/7-medial-distal-condyle(mdc).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                <ul>
                                   <li>Place Pointer at the Medial distal condyle </li>
									<li>To Record, Press Button</li>
                                </ul>
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut lateral_distal_condyle LDC d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Femur</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/knee-bone/femur-points/8-lateral-distal-condyle(ldc).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                <ul>
                                    <li>Place Pointer at the lateral distal condyle </li>
									<li>To Record, Press Button</li>
                                </ul>
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut medial_posterior_condyle MPC d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Femur</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/knee-bone/femur-points/9-medial-posteror-condylr(mpc).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                <ul>
                                    <li>Place Pointer at the medial posterior condyle </li>
									<li>To Record, Press Button</li>
                                </ul>
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut lateral_posterior_condyle LPC d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Femur</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/knee-bone/femur-points/10-lateral-posterior-condyle(lpc).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                <ul>
                                    <li>Place Pointer at the lateral posterior condyle </li>
									<li>To Record, Press Button</li>
                                </ul>
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut anterior_cortex AC d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Femur</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/knee-bone/femur-points/1-ac.jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                <ul>
                                    <li>Place Pointer at the anterior cortex</li>
									<li>To Record, Press Button</li>
                                </ul>
                            </p>
                        </div>
                    </div>
                    <div class="check_box">
                        <div class="new">
                            <form>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question1" class="section-toggle HIF" data-section="hip_in_flexion" />
                                    <label for="coupon_question1">Hip in Flexion</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question2" class="section-toggle HC" data-section="hip_center" />
                                    <label for="coupon_question2">Hip Center</label>
                                </div>
								<div class="form-group">
                                    <input type="checkbox" id="coupon_question5" class="section-toggle FC" data-section="femur_center" />
                                    <label for="coupon_question5">Femur Center</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question3" class="section-toggle ME" data-section="medial_epicondyle" />
                                    <label for="coupon_question3">Medial Epicondyle</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question4" class="section-toggle LE" data-section="lateral_epicondyle" />
                                    <label for="coupon_question4">Lateral Epicondyle</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question6" class="section-toggle FAP" data-section="femoral_ap_axis" />
                                    <label for="coupon_question6">Femoral AP Axis</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question7" class="section-toggle MDC" data-section="medial_distal_condyle" />
                                    <label for="coupon_question7">Medial Distal Condyle</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question8" class="section-toggle LDC" data-section="lateral_distal_condyle" />
                                    <label for="coupon_question8">Lateral Distal Condyle</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question9" class="section-toggle MPC" data-section="medial_posterior_condyle" />
                                    <label for="coupon_question9">Medial Posterior Condyle</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question10" class="section-toggle LPC" data-section="lateral_posterior_condyle" />
                                    <label for="coupon_question10">Lateral Posterior Condyle</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question11" class="section-toggle AC" data-section="anterior_cortex" />
                                    <label for="coupon_question11">Anterior Cortex</label>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="bottom_btn">
                    <div class="btn green_border pointer_bg">
                        <span class="mr-20"><img src="../static/images/icon/check.png" /></span>Pointer
                    </div>
                    <div class="blank"></div>
                    <div class="btn" >
                        <a id="backBtn" href="../landing-page.html"><span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back</a>
                    </div>
                    <div class="btn" >
                        <a  id="nextBtn" href="multiple-page.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
                    </div>
                </div>

                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../static/images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#" id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
                            </li>
                           <li class="footer_btn_three">
								<a href="#" id="powerbuttonLink">
									<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
								</a>
								<script>
									document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
										event.preventDefault();  // Prevent default anchor behavior
										window.location.href = '/';  // Redirect to root path
									});
								</script>
							</li>

                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="receivedValues"></div>

    <!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>

</body>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>


<!-- Scripting code starts -->
<script>
let socket;
let isSocketOpen = false;
let pageName;
let isPageNameSent = false;

function startServer() {
    if (!socket || socket.readyState === WebSocket.CLOSED) {
        const currentUrl = window.location.href;
        pageName = currentUrl.substring(currentUrl.lastIndexOf('/') + 1).split('?')[0];

        socket = new WebSocket('ws://127.0.0.1:8000/ws');

        socket.onopen = () => {
            console.log('Socket opened');
            isSocketOpen = true;
            if (!isPageNameSent) {
                socket.send(JSON.stringify({ file: pageName }));
                isPageNameSent = true;
            }
        };

        socket.onmessage = (event) => {
            console.log('Received message:', event.data);
            processMessage(event.data);
        };

        socket.onerror = (error) => {
            console.error('Socket error:', error);
        };

        socket.onclose = () => {
            console.log('Socket closed');
            isSocketOpen = false;
            isPageNameSent = false;
        };
    }
}

function handleNavigation(event) {
    event.preventDefault();
    const targetUrl = event.currentTarget.href;

    if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({ file: " " }));
        socket.addEventListener('close', function () {
            window.location.href = targetUrl;
        }, { once: true });

        socket.close();
    } else {
        window.location.href = targetUrl;
    }
}

function redirectToPage(url) {
    if (socket) {
        socket.close();
    }
    window.location.href = url;
}

function processMessage(data) {
    const values = data.split(',');
    const abbreviation = values[0];
    const currentUrl = window.location.href;
    const projectUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);

    if (abbreviation === 'AC') {
        setTimeout(() => {
            redirectToPage(projectUrl + 'multiple-page.html');
        }, 100);
    }

    $('.middle_section').addClass('d-none');
    $('.' + abbreviation).next().removeClass('d-none');
    $('.section-toggle.' + abbreviation).prop('checked', true);

    if (abbreviation !== 'HC' && abbreviation.includes("HC")) {
        $('.hc-image').removeClass('d-none');
        $('.HC').removeClass('d-none');

        if (abbreviation !== "HC") {
            $('[class^="img-fluid"]').addClass('d-none');
        } else {
            $('.HC1').removeClass('d-none');
        }

        $('.grid .' + abbreviation).removeClass('d-none');
    }
}

document.addEventListener('DOMContentLoaded', () => {
    startServer();
    document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
    document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);

    // Camera functionality
    const cameraButton = document.querySelector('.footer_btn_one .btn.first');
    const cameraModal = document.getElementById('cameraModal');
    const closeCamera = document.querySelector('.close-camera');
    const videoStream = document.getElementById('videoStream');
    let mediaStream = null;

    async function startCamera() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                }
            });
            mediaStream = stream;
            videoStream.srcObject = stream;
            videoStream.play();
        } catch (err) {
            console.error('Error accessing camera:', err);
        }
    }

    function stopCamera() {
        if (mediaStream) {
            mediaStream.getTracks().forEach(track => track.stop());
            videoStream.srcObject = null;
            mediaStream = null;
        }
    }

    cameraButton?.addEventListener('click', function (e) {
        e.preventDefault();
        cameraModal.style.display = 'block';
        startCamera();
    });

    closeCamera?.addEventListener('click', function () {
        cameraModal.style.display = 'none';
        stopCamera();
    });

    window.addEventListener('click', function (event) {
        if (event.target === cameraModal) {
            cameraModal.style.display = 'none';
            stopCamera();
        }
    });
});
</script>

</html>