import numpy as np
from xarm.wrapper import XArmAPI
from scipy.spatial.transform import Rotation as R
from scipy.spatial.transform import Rotation as R
import numpy as np
from scipy.spatial.transform import Rotation as R

def xarm_pose_to_transformation_matrix(ip_address='*************'):
    """
    Retrieves current pose and converts it to a 4x4 transformation matrix
    Returns: (4x4 numpy array, success status)
    """
    # Initialize connection
    arm = XArmAPI(ip_address)
    
    try:
        # Enable motion and set mode
        arm.motion_enable(True)
        arm.set_mode(0)
        arm.set_state(0)

        # Get current position (in degrees)
        code, pose = arm.get_position_aa(is_radian=False)
        if code != 0:
            print(f"Error getting position: Code {code}")
            return None, False

        x, y, z, roll_deg, pitch_deg, yaw_deg = pose
        #x, y, z, rx, ry, rz,  = pose
        
        # Convert to radians
        roll = np.radians(roll_deg)
        pitch = np.radians(pitch_deg)
        yaw = np.radians(yaw_deg)

        
        # Euler angles in radians: [Y, Z, X]
        #angles = [pitch, roll, yaw]
        #rotation = R.from_euler('yzx', angles)
        # rotvec = np.array(angles)  # axis-angle vector
        # rotation = R.from_rotvec(rotvec)
        # rotation_matrix = rotation.as_matrix()
        
        # Calculate individual rotation matrices
        # Roll (X-axis)
        Rx = np.array([
            [1, 0, 0],
            [0, np.cos(roll), -np.sin(roll)],
            [0, np.sin(roll), np.cos(roll)]
        ])
        
        # Pitch (Y-axis)
        Ry = np.array([
            [np.cos(pitch), 0, np.sin(pitch)],
            [0, 1, 0],
            [-np.sin(pitch), 0, np.cos(pitch)]
        ])
        
        # Yaw (Z-axis)
        Rz = np.array([
            [np.cos(yaw), -np.sin(yaw), 0],
            [np.sin(yaw), np.cos(yaw), 0],
            [0, 0, 1]
        ])

        # # Combined rotation matrix (extrinsic XYZ: Rz @ Ry @ Rx)
        # #rotation_matrix = Rx @ Rz @ Ry
        rotation_matrix = Rz @ Ry @ Rx

        # Create 4x4 transformation matrix
        transformation_matrix = np.eye(4)
        transformation_matrix[:3, :3] = rotation_matrix
        transformation_matrix[:3, 3] = [x, y, z]

        return transformation_matrix, True

    except Exception as e:
        print(f"Error: {str(e)}")
        return None, False
    
    finally:
        arm.disconnect()

if __name__ == "__main__":
    # Example usage
    np.set_printoptions(precision=4, suppress=True)
    
    tf_matrix, success = xarm_pose_to_transformation_matrix()   

    
    if success:
        print("4x4 Transformation Matrix:")
        print(tf_matrix)
        print("\nStructure:")
        print(f"Position (mm): {tf_matrix[:3,3]}")
        print("Rotation Matrix:")
        print(tf_matrix[:3,:3])