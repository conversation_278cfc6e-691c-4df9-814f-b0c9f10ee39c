import serial
import time
import threading
import serial.tools.list_ports


# Initialize serial connection
def get_available_ports():
    """Returns a list of available serial ports."""
    ports = serial.tools.list_ports.comports()
    return [port.device for port in ports]



try:
    # Find an available port
    available_ports = get_available_ports()
    print(f'available_ports  {available_ports}')
    if not available_ports:
        print("No available serial ports found. Please check the connection.")
        # exit(1)
    port = available_ports[1]
    print(f"Connecting to {port}...")
    arduino = serial.Serial(
        port=port,  # Update to the correct COM port for your Mega 2560
        baudrate=9600,  # Matches the baud rate set in your Arduino code
        timeout=1,  # Timeout for serial read (in seconds)
        parity=serial.PARITY_NONE,
        stopbits=serial.STOPBITS_ONE,
        bytesize=serial.EIGHTBITS,
        xonxoff=False,
        rtscts=False,
        dsrdtr=False
    )
    time.sleep(2)  # Allow Arduino to initialize
except serial.SerialException as e:
    print(f"Failed to connect to Arduino: {e}")
    arduino = None

# Threading lock and motor state event
lock = threading.Lock()
motor_running = threading.Event()  # Event to track motor state (running or stopped)


def send_data(data):
    """Thread-safe function to send data to Arduino."""
    with lock:
        if arduino and arduino.is_open:
            arduino.write(data.encode())
            time.sleep(0.1)
        else:
            print("Serial connection is not open.")


def receive_data():
    """Thread-safe function to receive data from Arduino."""
    with lock:
        if arduino and arduino.is_open:
            data = arduino.readline().decode('utf-8').rstrip()
            return data
        return None


def start_motor():
    """Start the motor if it's not already running."""
    if not motor_running.is_set():  # Check if the motor is already running
        send_data('S\r')
        motor_running.set()  # Mark the motor as running
        print("Motor started.")
    else:
        print("Motor is already running.")


def stop_motor():
    """Stop the motor if it's running."""
    if motor_running.is_set():  # Check if the motor is currently running
        send_data('T\r')
        motor_running.clear()  # Mark the motor as stopped
        print("Motor stopped.")
    else:
        print("Motor is already stopped.")


def close_connection():
    """Close the serial connection."""
    with lock:
        if arduino and arduino.is_open:
            arduino.close()
            print("Serial connection closed.")

#
# start_motor()
# time.sleep(60)
# stop_motor()