from PyQt6.QtWidgets import QApplication, QMainWindow
from PyQt6.Qt3DCore import QEntity, QTransform
from PyQt6.Qt3DExtras import (
    Qt3DWindow,
    QPlaneMesh,
    QSphereMesh,
    QPhongMaterial,
    QOrbitCameraController,
)
from PyQt6.QtGui import QColor
from PyQt6.Qt3DRender import QCamera

def create_3d_scene():
    app = QApplication([])

    # Create the 3D window
    view = Qt3DWindow()
    view.defaultFrameGraph().setClearColor(QColor("lightgray"))
    container = QMainWindow()
    container.setCentralWidget(view)
    container.setWindowTitle("Road and Semi-Spherical Marker")
    container.resize(800, 600)

    # Root entity
    root_entity = QEntity()

    # Add a road (plane)
    road_entity = QEntity(root_entity)
    road_mesh = QPlaneMesh()
    road_mesh.setWidth(10)
    road_mesh.setHeight(1)
    road_material = QPhongMaterial()
    road_material.setDiffuse(QColor("gray"))
    road_entity.addComponent(road_mesh)
    road_entity.addComponent(road_material)

    # Add a semi-spherical head as a marker
    marker_entity = QEntity(root_entity)
    marker_mesh = QSphereMesh()
    marker_mesh.setRadius(0.5)
    marker_material = QPhongMaterial()
    marker_material.setDiffuse(QColor("blue"))
    marker_entity.addComponent(marker_mesh)
    marker_entity.addComponent(marker_material)

    # Position the semi-spherical marker
    marker_transform = QTransform()
    marker_transform.setTranslation(QVector3D(0, 0.5, 0))
    marker_entity.addComponent(marker_transform)

    # Add a camera controller for interactivity
    camera = view.camera()
    camera.lens().setPerspectiveProjection(45.0, 16.0/9.0, 0.1, 100.0)
    camera.setPosition(QVector3D(0, 2, 10))
    camera.setViewCenter(QVector3D(0, 0, 0))
    camera_controller = QOrbitCameraController(root_entity)
    camera_controller.setCamera(camera)

    view.setRootEntity(root_entity)
    container.show()

    app.exec()

create_3d_scene()
