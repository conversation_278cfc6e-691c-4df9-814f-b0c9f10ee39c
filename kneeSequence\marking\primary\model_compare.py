import numpy as np
import json
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from scipy.spatial import KDTree
from plyfile import PlyData
from stl import mesh


def compute_transformation(reference_normal_model, target_model_points):
    """
    Compute the transformation matrix (rotation + translation) that aligns
    the source points to the reference points using SVD.

    Args:
        reference_normal_model (numpy.ndarray): Nx3 array of reference points.
        target_model_points (numpy.ndarray): Nx3 array of source points.

    Returns:
        numpy.ndarray: 4x4 transformation matrix.
    """
    # Compute centroids
    centroid_ref = np.mean(reference_normal_model, axis=0)
    centroid_target = np.mean(target_model_points, axis=0)

    # Center the points
    ref_centered = reference_normal_model - centroid_ref
    target_centered = target_model_points - centroid_target

    # Compute covariance matrix
    H = np.dot(target_centered.T, ref_centered)

    # Compute SVD
    U, _, Vt = np.linalg.svd(H)
    R = np.dot(Vt.T, U.T)

    # Ensure right-handed coordinate system
    if np.linalg.det(R) < 0:
        Vt[-1, :] *= -1
        R = np.dot(Vt.T, U.T)

    # Compute translation
    t = centroid_ref - np.dot(R, centroid_target)

    # Create transformation matrix
    transformation_matrix = np.eye(4)
    transformation_matrix[:3, :3] = R
    transformation_matrix[:3, 3] = t

    return transformation_matrix


# Input models
# reference_normal_model = np.array([
#     [-4.4544983, -45.4923515, -92.0291138],
#     [-118.3544769, 43.5593414, -119.3513947],
#     [-101.8402863, -55.0170021, -238.8080292],
#     [-21.8630066, 2.8947372, -148.2806702]
# ])

# Input models
reference_normal_model = np.array([
    [-3.686203, -45.2578011, -92.2927094],
    [-120.6771698, 43.4365997, -120.0930939],
    [-99.371048, -55.7264137, -237.451416],
    [-21.8630066, 2.8947372, -148.2806702]
])

models = {
    # "GRADE1": [
    #     [199.68396, -2.4612885, -134.7665863],
    #     [82.0971069, 75.8616791, -97.7077179],
    #     [44.4090843, -41.223156, -189.2983856],
    #     [145.2841187, 34.6798706, -172.7322235]
    # ],
    "GRADE1": [
        [164.9216614, -15.1739521, -88.3786011],  # point_0
        [41.3269577, 68.5887299, -115.3978043],  # point_1
        [63.9419479, -30.8830795, -232.4360046],  # point_2
        [143.0863953, 31.5878315, -144.2380066]  # point_3
    ],
    "GRADE2A": [
        [91.2101059, 229.0073395, 108.5774231],
        [-25.5542984, 315.8197632, 123.1131592],
        [-38.3161316, 221.6100769, 3.8524628],
        [54.6489944, 278.6124573, 62.104744]
    ],
    "GRADE2B": [
        [66.8091125, 338.002533, 201.3783112],
        [-44.555191, 435.4537964, 194.4881592],
        [-59.2566528, 331.5387878, 79.399292],
        [41.1133575, 386.4836731, 149.0575104]
    ],
    "GRADE2C": [
        [607.4470215, -31.2199402, -111.6118164],
        [497.9041138, 60.6724548, -150.3721924],
        [468.9524536, -82.0849915, -205.6183472],
        [576.99646, -8.5259399, -176.4898376]
    ],
    "GRADE3A": [
        [217.1698608, 25.5885315, -614.4119263],
        [116.8717804, 119.9926453, -667.303894],
        [128.8491821, -5.5860901, -754.446106],
        [207.1898499, 61.2421875, -679.947876]
    ],
    "GRADE3B": [
        [222.0783081, -34.3432083, -755.5569458],
        [124.0453491, -78.315033, -859.1224976],
        [135.3878174, 76.0894623, -858.6884766],
        [215.3179321, -11.8577271, -826.8530273]
    ]
}

# Compute and save transformation matrices
transformations = {}
for model_name, points in models.items():
    target_model_points = np.array(points)
    transformation_matrix = compute_transformation(reference_normal_model, target_model_points)
    transformations[model_name] = transformation_matrix.tolist()

# Save to JSON file
with open("transformations.json", "w") as f:
    json.dump(transformations, f, indent=4)

# Load from JSON file
with open("transformations.json", "r") as f:
    loaded_transformations = json.load(f)

# Print loaded transformations
for model_name, matrix in loaded_transformations.items():
    print(f"{model_name} Transformation Matrix:\n{np.array(matrix)}\n")


# import numpy as np
# import json
# from scipy.spatial import KDTree

# import os

# def load_obj_points(obj_file_path):
#     """
#     Load points from an OBJ file.
#     The OBJ file must contain vertices in the 'v' format.
#     """
#     with open(obj_file_path, 'r') as f:
#         vertices = []
#         for line in f:
#             if line.startswith('v '):  # Look for lines starting with 'v', which represent vertices
#                 parts = line.split()
#                 vertex = [float(parts[1]), float(parts[2]), float(parts[3])]
#                 vertices.append(vertex)
#     return np.array(vertices)

def load_obj_points(obj_file_path):
    """
    Load points from an OBJ file.
    The OBJ file must contain vertices in the 'v' format.
    """
    stl_mesh = mesh.Mesh.from_file(obj_file_path)

    # Extract vertices from the mesh
    vertices = np.concatenate([stl_mesh.v0, stl_mesh.v1, stl_mesh.v2])

    # Remove duplicate points to get the point cloud
    point_cloud = np.unique(vertices, axis=0)
    return point_cloud


def find_absent_points(reference_cloud, test_cloud, tolerance=1e-5):
    """
    Finds points in the reference cloud that are absent in the test cloud.

    Args:
        reference_cloud (numpy.ndarray): Nx3 array of reference points.
        test_cloud (numpy.ndarray): Mx3 array of test points.
        tolerance (float): Distance threshold to consider points as matching.

    Returns:
        numpy.ndarray: Points in the reference cloud not found in the test cloud.
    """
    # Build a KDTree for the test cloud
    tree = KDTree(test_cloud)

    # Query the test cloud for each point in the reference cloud
    distances, indices = tree.query(reference_cloud, k=1)

    # Find points with distances greater than the tolerance
    absent_points = reference_cloud[distances > tolerance]

    return absent_points


def apply_transformation(points, transformation_matrix):
    """
    Apply a 4x4 transformation matrix to a set of 3D points.
    """
    homogeneous_points = np.hstack((points, np.ones((points.shape[0], 1))))  # Convert to homogeneous coordinates
    transformed_points = (transformation_matrix @ homogeneous_points.T).T  # Apply transformation matrix
    return transformed_points[:, :3]  # Return only the x, y, z coordinates


# Path to the reference model (pelvis.obj)
reference_model_path = r'D:\SpineSurgery\pythonProject\app\static\images\hip-bone\3D\3Dhip\pelvis.stl'
test_models = {
    "GRADE1": r'D:\SpineSurgery\pythonProject\app\static\images\hip-bone\3D\3Dhip\GRADE 1.stl',
    "GRADE2A": r'D:\SpineSurgery\pythonProject\app\static\images\hip-bone\3D\3Dhip\GRADE 2A.stl',
    "GRADE2C": r'D:\SpineSurgery\pythonProject\app\static\images\hip-bone\3D\3Dhip\GRADE 2C.stl',
    "GRADE2B": r'D:\SpineSurgery\pythonProject\app\static\images\hip-bone\3D\3Dhip\GRADE 2B.stl',
    "GRADE3A": r'D:\SpineSurgery\pythonProject\app\static\images\hip-bone\3D\3Dhip\GRADE 3A.stl',
    "GRADE3B": r'D:\SpineSurgery\pythonProject\app\static\images\hip-bone\3D\3Dhip\GRADE 3B.stl'
}

# Tolerances for each model
tolerances = {
    "GRADE1": 2.5,
    "GRADE2A": 2.0,
    "GRADE2B": 2.5,
    "GRADE2C": 2.0,
    "GRADE3A": 1.5,
    "GRADE3B": 2.0
}

# Load the reference cloud (pelvis.obj)
reference_cloud = load_obj_points(reference_model_path)

# Load the transformations from the previously saved file (transformations.json)
with open("transformations.json", "r") as f:
    loaded_transformations = json.load(f)

# Iterate through the test models, apply the transformations and find absent points
absent_points_by_model = {}
for model_name, model_path in test_models.items():
    # Load the test model points
    test_cloud = load_obj_points(model_path)

    # Apply the transformation to the test model points
    transformation_matrix = np.array(loaded_transformations[model_name])
    transformed_test_cloud = apply_transformation(test_cloud, transformation_matrix)
    tolerance = tolerances.get(model_name, 1)  # Default to 2.5 if not found
    # Find absent points
    absent_points = find_absent_points(reference_cloud, transformed_test_cloud, tolerance=tolerance)

    # Store the absent points
    absent_points_by_model[model_name] = absent_points

# Print absent points for each model
for model_name, absent_points in absent_points_by_model.items():
    print(f"Absent points in {model_name}:")
    print(absent_points)
    print("\n")

fig = plt.figure(figsize=(12, 8))  # Create a figure to hold the subplots

# Number of models to plot
num_models = len(absent_points_by_model)

# Create a 3D subplot for each model
for i, (model_name, absent_points) in enumerate(absent_points_by_model.items()):
    ax = fig.add_subplot(2, 3, i + 1, projection='3d')  # Create a 3D subplot

    # Plot the absent points
    ax.scatter(absent_points[:, 0], absent_points[:, 1], absent_points[:, 2], label=model_name)

    # Set the title and labels
    ax.set_title(f"Absent Points in {model_name}")
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')

# Adjust layout and show the plot
plt.tight_layout()
plt.show()
