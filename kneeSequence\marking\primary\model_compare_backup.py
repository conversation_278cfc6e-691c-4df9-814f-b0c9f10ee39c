











#Robot_workSpaceChecking


from xarm.wrapper import XArmAPI
import numpy as np

xarm_ip = '*************'  # Your xArm's IP

arm = XArmAPI(xarm_ip)
arm.motion_enable(enable=True)
arm.set_mode(0)
arm.set_state(0)
arm.set_simulation_robot(True)

# Your model's predicted point (in meters)
predicted_point = np.array([800, 200, -108])

pose = [predicted_point[0], predicted_point[1], predicted_point[2], 180, 0, 0]

# IK check
code, joint_angles = arm.get_inverse_kinematics(pose=pose)
print(f'code {code}')
if code == 0:
    print("✅ Pose is reachable! Joint Angles:", joint_angles)
    arm.set_position(*pose, wait=True)
else:
    print("❌ Pose is NOT reachable by xArm 6.")
