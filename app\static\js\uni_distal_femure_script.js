const imagesByColor = {
    yellow: [
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_2.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_3.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_4.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_5.jpg',
    ],
    yellow1: [
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_1-1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_2-1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_3-1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_4-1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_5-1.jpg',
    ],
    red: [
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_2.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_3.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_4.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_5.jpg',
    ],
    red1: [
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_1-1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_2-1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_3-1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_4-1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_5-1.jpg',
    ],
    green: [
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_2.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_3.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_4.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_5.jpg',
    ],
    green1: [
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_1-1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_2-1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_3-1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_4-1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_5-1.jpg',
    ]
};

function startImageAnimation(color) {
    const images = imagesByColor[color];
    let index = 0;
    const imgElement = document.getElementById(`animated-image${color.charAt(0).toUpperCase() + color.slice(1)}`);

    setInterval(() => {
        imgElement.src = images[index];
        index = (index + 1) % images.length; // Loop back to the start
    }, 1000); // Change image every 1 second
}

const colors = ['yellow', 'red', 'green'];

colors.forEach(color => {
    startImageAnimation(color);
    startImageAnimation(`${color}1`); // Start animation for color1 as well
});
