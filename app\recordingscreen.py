import sys
import cv2
import numpy as np
import pyautogui
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QWidget, QVBoxLayout, QPushButton, QLabel, QFileDialog, QInputDialog
from PyQt5.QtCore import QTimer, QRect

class ScreenRecorder(QWidget):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("Screen Recorder")
        self.setGeometry(100, 100, 300, 150)

        self.recording = False
        self.paused = False
        self.out = None
        self.screen_region = None  # Store the selected region to capture

        # Create UI elements
        layout = QVBoxLayout()

        self.status_label = QLabel("Status: Idle")
        layout.addWidget(self.status_label)

        self.start_button = QPushButton("Start Recording")
        self.start_button.clicked.connect(self.start_recording)
        layout.addWidget(self.start_button)

        self.pause_button = QPushButton("Pause Recording")
        self.pause_button.clicked.connect(self.pause_recording)
        self.pause_button.setEnabled(False)
        layout.addWidget(self.pause_button)

        self.stop_button = QPushButton("Stop Recording")
        self.stop_button.clicked.connect(self.stop_recording)
        self.stop_button.setEnabled(False)
        layout.addWidget(self.stop_button)

        self.setLayout(layout)

        # Screen recording timer
        self.timer = QTimer()
        self.timer.timeout.connect(self.record_frame)

    def start_recording(self):
        save_path, _ = QFileDialog.getSaveFileName(self, "Save Recording", "", "Video Files (*.mp4)")
        if not save_path:
            return

        # Let the user select a screen region
        self.screen_region = self.select_screen_region()
        if not self.screen_region:
            return  # If no region selected, return early

        # Set up screen recording
        self.fourcc = cv2.VideoWriter_fourcc(*"mp4v")
        self.out = cv2.VideoWriter(save_path, self.fourcc, 20.0, (self.screen_region.width(), self.screen_region.height()))

        self.recording = True
        self.paused = False
        self.timer.start(50)  # Capture frames at 20 FPS

        self.status_label.setText("Status: Recording")
        self.start_button.setEnabled(False)
        self.pause_button.setEnabled(True)
        self.stop_button.setEnabled(True)

    def pause_recording(self):
        if self.paused:
            self.paused = False
            self.timer.start(50)
            self.status_label.setText("Status: Recording")
            self.pause_button.setText("Pause Recording")
        else:
            self.paused = True
            self.timer.stop()
            self.status_label.setText("Status: Paused")
            self.pause_button.setText("Resume Recording")

    def stop_recording(self):
        self.recording = False
        self.paused = False
        self.timer.stop()

        if self.out:
            self.out.release()
            self.out = None

        self.status_label.setText("Status: Idle")
        self.start_button.setEnabled(True)
        self.pause_button.setEnabled(False)
        self.stop_button.setEnabled(False)

    def record_frame(self):
        if not self.recording or self.paused:
            return

        # Capture the screen region
        img = pyautogui.screenshot(region=(self.screen_region.x(), self.screen_region.y(), self.screen_region.width(), self.screen_region.height()))
        frame = np.array(img)
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # Write the frame to the video file
        self.out.write(frame)

    def select_screen_region(self):
        # Open a dialog for selecting the screen region (for simplicity, just input the region)
        width, ok = QInputDialog.getInt(self, "Select Width", "Enter the width of the region to capture:", 800, 100, pyautogui.size()[0])
        if not ok:
            return None
        height, ok = QInputDialog.getInt(self, "Select Height", "Enter the height of the region to capture:", 600, 100, pyautogui.size()[1])
        if not ok:
            return None

        x, ok = QInputDialog.getInt(self, "Select X Position", "Enter the X position of the region to capture:", 0, 0, pyautogui.size()[0] - width)
        if not ok:
            return None
        y, ok = QInputDialog.getInt(self, "Select Y Position", "Enter the Y position of the region to capture:", 0, 0, pyautogui.size()[1] - height)
        if not ok:
            return None

        return QRect(x, y, width, height)

    def closeEvent(self, event):
        # Ensure resources are released when the application is closed
        if self.out:
            self.out.release()
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ScreenRecorder()
    window.show()
    sys.exit(app.exec_())
