#!/usr/bin/env python
import json

import sys
import threading
import pickle
import msvcrt
import os
import cv2
import time
import numpy as np
from ctypes import *
import sys
import os

from matplotlib import pyplot as plt
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# Get the current directory of the script
current_dir = os.path.dirname(os.path.abspath(__file__))
# Construct the absolute path to 'general/camera/MvImport'
mv_import_path = os.path.join(current_dir, 'general', 'camera', 'MvImport')
# Append the constructed path to sys.path
sys.path.append(mv_import_path)
from general.camera.MvCameraControl_class import *
from general.common.tracker_object import TriangleTracker, FemureTracker
from general.common import utils
from general.common import calibration

g_bExit = False

def bezier_curve(points, num_points=100):
    points = np.array(points)
    t_values = np.linspace(0, 1, num_points)

    curve = np.zeros((num_points, 3))

    for i, t in enumerate(t_values):
        curve[i] = (1 - t) ** 3 * points[0] + \
                   3 * (1 - t) ** 2 * t * points[1] + \
                   3 * (1 - t) * t ** 2 * points[2] + \
                   t ** 3 * points[3]

    return curve, t_values

#
# def bezier_tangent(points, t):
#     """
#     Calculate the tangent of the Bézier curve at parameter t.
#     This function assumes a quadratic Bézier curve (3 control points).
#     """
#     P0, P1, P2, P3= points
#     tangent = 3 * (1 - t)**2 * (P1 - P0) + 6 * (1 - t) * t * (P2 - P1) + 3 * t**2 * (P3 - P2)
#     return tangent


def bezier_tangent(points, t):
    # Ensure points are in 3D
    P0, P1, P2, P3 = points
    tangent = 3 * (1 - t) ** 2 * (P1 - P0) + \
              6 * (1 - t) * t * (P2 - P1) + \
              3 * t ** 2 * (P3 - P2)

    return tangent

def calculate_tangents(points, t_values):
    points = np.array(points)  # Convert to NumPy array

    tangents = np.zeros((len(t_values), 3))  # Initialize array to store tangent vectors in 3D

    # Loop through all values of t and calculate tangents
    for i, t in enumerate(t_values):
        tangents[i] = bezier_tangent(points, t)  # Ensure this returns a 3D vector

    return tangents


def calculate_normals(tangents):
    normals = np.zeros_like(tangents)
    up_vector = np.array([0, 0, 1])

    for i, tangent in enumerate(tangents):
        normal = np.cross(tangent, up_vector)
        normal /= np.linalg.norm(normal)  # Normalize the normal vector
        normals[i] = normal

    return normals


from scipy.optimize import minimize


try:
    with open('registration_data/PointCloudbezier.pickle', 'rb') as handle:
        points = pickle.load(handle)
except Exception as e:
    print(f"Error loading point cloud data: {e}")

# Convert the control points to NumPy arrays
C_values = [np.array(value['C']) for key, value in points.items()]
for i in C_values:
    print(f'{i}')
# Define Bézier curve, tangent, and curvature functions
def bezier_cubic(points, t):
    P0, P1, P2, P3 = points
    curve_point = (1 - t) ** 3 * P0 + \
                  3 * (1 - t) ** 2 * t * P1 + \
                  3 * (1 - t) * t ** 2 * P2 + \
                  t ** 3 * P3
    return curve_point

def bezier_tangent(points, t):
    P0, P1, P2, P3 = points
    tangent = 3 * (1 - t) ** 2 * (P1 - P0) + \
              6 * (1 - t) * t * (P2 - P1) + \
              3 * t ** 2 * (P3 - P2)
    return tangent

def bezier_second_derivative(points, t):
    P0, P1, P2, P3 = points
    second_derivative = 6 * (1 - t) * (P2 - 2 * P1 + P0) + \
                        6 * t * (P3 - 2 * P2 + P1)
    return second_derivative

def curvature_magnitude(points, t):
    tangent = bezier_tangent(points, t)
    second_derivative = bezier_second_derivative(points, t)
    curvature = np.linalg.norm(np.cross(tangent, second_derivative)) / (np.linalg.norm(tangent) ** 3)
    return curvature

# Find the vertex by minimizing the negative curvature magnitude
# result = minimize(lambda t: -curvature_magnitude(C_values, t), x0=0.5, bounds=[(0, 1)])

# Get the vertex t and point
# vertex_t = result.x[0]
# vertex_point = bezier_cubic(C_values, vertex_t)


mid_point_t = 0.5
mid_point = bezier_cubic(C_values, mid_point_t)

# Calculate the tangent and normal at the vertex
tangent_at_vertex = bezier_tangent(C_values, mid_point)

def calculate_normal(tangent):
    up_vector = np.array([0, 0, 1])
    normal = np.cross(tangent, up_vector)
    normal /= np.linalg.norm(normal)  # Normalize
    return normal

normal_at_vertex = calculate_normal(tangent_at_vertex)

# print("Vertex t:", vertex_t)
# print("Vertex point:", vertex_point)
# print("Tangent at vertex:", tangent_at_vertex)
# print("Normal at vertex:", normal_at_vertex)
# t_values = np.linspace(0, 1, 100)
# bezier_points = np.array([bezier_cubic(C_values, t) for t in t_values])
#
# # Plot Bézier curve
# plt.plot(bezier_points[:, 0], bezier_points[:, 1], label='Bézier Curve', color='orange')
#
# # Plot vertex point
# plt.scatter([mid_point[0]], [mid_point[1]], color='red', label='Vertex', zorder=5)
#
# # Scale the tangent and normal vectors for better visibility
# tangent_at_vertex_normalized = tangent_at_vertex / np.linalg.norm(tangent_at_vertex)
# normal_at_vertex = calculate_normal(tangent_at_vertex)
#
# # Adjust the scaling factor
# scale_factor = 0.2
#
# # Plot tangent vector at the vertex
# plt.quiver(mid_point[0], mid_point[1],
#            tangent_at_vertex_normalized[0], tangent_at_vertex_normalized[1],
#            angles='xy', scale_units='xy', scale=1/scale_factor, color='blue', label='Tangent at Vertex')
#
# # Plot normal vector at the vertex
# plt.quiver(mid_point[0], mid_point[1],
#            normal_at_vertex[0], normal_at_vertex[1],
#            angles='xy', scale_units='xy', scale=1/scale_factor, color='green', label='Normal at Vertex')
#
# # Add labels for vertex, tangent, and normal
# plt.text(mid_point[0], mid_point[1], '  Vertex', fontsize=12, verticalalignment='bottom', color='red')
# plt.text(mid_point[0] + tangent_at_vertex_normalized[0] * scale_factor,
#          mid_point[1] + tangent_at_vertex_normalized[1] * scale_factor, '  Tangent', fontsize=12, color='blue')
# plt.text(mid_point[0] + normal_at_vertex[0] * scale_factor,
#          mid_point[1] + normal_at_vertex[1] * scale_factor, '  Normal', fontsize=12, color='green')
#
# # Set plot details
# plt.title("Bézier Curve with Vertex, Tangent, and Normal")
# plt.xlabel("X")
# plt.ylabel("Y")
#
# # Adjust the aspect ratio to make the plot square for better visualization
# plt.gca().set_aspect('equal', adjustable='box')
#
# # Add legend outside the plot to avoid overlap
# plt.legend(loc='upper right', bbox_to_anchor=(1.15, 1.05))
#
# # Enable grid for better visual reference
# plt.grid(True)
#
# # Show the plot
# plt.show()
















































#
#
# curve, t_values = bezier_curve(C_values)
# tangents = calculate_tangents(C_values, t_values)
# normals = calculate_normals(tangents)
#
#
#
# from scipy.optimize import minimize
#
# vertex_t_index = np.argmin(np.linalg.norm(curve - curve[0], axis=1))  # This is an approximation
# vertex_point = curve[vertex_t_index]
#
# # Calculate the tangent at the vertex
# vertex_t = t_values[vertex_t_index]
# tangent_at_vertex = bezier_tangent(points, vertex_t)
#
# # Calculate the normal at the vertex
# normal_at_vertex = calculate_normals(tangent_at_vertex)
#
#

'''
# Plot the Bézier curve and control points
fig = plt.figure()
ax = fig.add_subplot(111, projection='3d')
C_values = np.array(C_values)
# Plot control points
ax.scatter(C_values[:, 0], C_values[:, 1], C_values[:, 2], color='r', label='Control Points')

# Plot the Bézier curve
ax.plot(curve[:, 0], curve[:, 1], curve[:, 2], color='b', label='Bézier Curve')

# Plot tangents at some specific points (for clarity, use fewer points for tangent visualization)
for i in range(0, len(t_values), 10):
    tangent_point = curve[i]
    tangent_vector = tangents[i]
    normal_vector = normals[i]

    # Scale vectors for visibility
    tangent_vector /= np.linalg.norm(tangent_vector)  # Normalize the tangent vector
    normal_vector /= np.linalg.norm(normal_vector)  # Normalize the normal vector

    # Scale for visibility
    tangent_end = tangent_point + 0.2 * tangent_vector
    normal_end = tangent_point + 0.2 * normal_vector

    # Plot the tangent as a line
    ax.plot([tangent_point[0], tangent_end[0]],
            [tangent_point[1], tangent_end[1]],
            [tangent_point[2], tangent_end[2]], color='g', label='Tangent' if i == 0 else "")

    # Plot the normal as a line
    ax.plot([tangent_point[0], normal_end[0]],
            [tangent_point[1], normal_end[1]],
            [tangent_point[2], normal_end[2]], color='purple', label='Normal' if i == 0 else "")

    # Optionally, plot a point at the tangent and normal end for better visibility
    ax.scatter(tangent_end[0], tangent_end[1], tangent_end[2], color='g', s=20)
    ax.scatter(normal_end[0], normal_end[1], normal_end[2], color='purple', s=20)

# Set labels and title
ax.set_xlabel('X')
ax.set_ylabel('Y')
ax.set_zlabel('Z')
ax.set_title('Bézier Curve with Tangents')

# Show the plot with legend
plt.legend()
plt.show()
'''
def work_thread(cam=0, pData=0, nDataSize=0, cam2=0, pData2=0, nDataSize2=0, framecount=0, normal_vector=None):
    variable_dict = {}
    stFrameInfo = MV_FRAME_OUT_INFO_EX()
    memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))

    pointer_data = []
    while True:
        ret = cam.MV_CC_GetOneFrameTimeout(pData, nDataSize, stFrameInfo, 500)
        ret2 = cam2.MV_CC_GetOneFrameTimeout(pData2, nDataSize2, stFrameInfo, 500)

        if ret == 0 and ret2 == 0:

            # Name the images left and right taking into consideration the opening streams.

            img_right = np.frombuffer(bytes(pData._obj), np.uint8).reshape((stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
            img_left = np.frombuffer(bytes(pData2._obj), np.uint8).reshape((stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

            # Rectify images

            rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)

            # Detect contours on both frames

            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)

            if len(detections_right) == 6 and 6 == len(detections_left):
                detections_right = sorted(detections_right, key=lambda x: x[0])
                detections_left = sorted(detections_left, key=lambda x: x[0])
                # Extract first 3 coordinates
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]
                next_three_right = detections_right[-3:]
                next_three_left = detections_left[-3:]

                Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                 rec_img_left)
                Pointer_traker.find_depth()
                Leds = Pointer_traker.geLEDcordinate()
                # print(f' Leds {Leds}')
                pointer_tip_gcs = Pointer_traker.getStylusPoint()

                vector2 = np.array(Leds[2]) - np.array(pointer_tip_gcs)
                up_vector = np.array([0, 0, 1])
                vector2 = np.cross(vector2, up_vector)
                def angle_between_vectors(v1, v2):
                    # Project both vectors into the xz-plane (ignore the y-component)
                    # v1_proj = np.array([v1[0], 0, v1[2]])
                    # v2_proj = np.array([v2[0], 0, v2[2]])

                    # Calculate the dot product and magnitudes
                    dot_product = np.dot(v1, v2)
                    mag_v1 = np.linalg.norm(v1)
                    mag_v2 = np.linalg.norm(v2)

                    # Calculate the angle in radians and convert to degrees
                    angle_rad = np.arccos(dot_product / (mag_v1 * mag_v2))
                    angle_deg = np.degrees(angle_rad)

                    return angle_deg

                Angle = angle_between_vectors(vector2, normal_vector)
                print(f'Angle {Angle}')
                time.sleep(0.1)
                framecount = framecount + 1
                continue
        else:
            print("no data[0x%x]" % ret)

        if framecount == 25:
            print("All points registered.")

            with open('SpineMarking/registration_data/PointCloud.pickle', 'wb') as handle:
                pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

            print("Pickle dump successful")

            break

        if g_bExit == True:
            break


if __name__ == "__main__":

    deviceList = MV_CC_DEVICE_INFO_LIST()
    tlayerType = MV_GIGE_DEVICE | MV_USB_DEVICE

    # ch:枚举设备 | en:Enum device
    ret = MvCamera.MV_CC_EnumDevices(tlayerType, deviceList)
    if ret != 0:
        print("enum devices fail! ret[0x%x]" % ret)
        sys.exit()

    if deviceList.nDeviceNum == 0:
        print("find no device!")
        sys.exit()

    print("Find %d devices!" % deviceList.nDeviceNum)

    for i in range(0, deviceList.nDeviceNum):
        mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
        if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE:
            print("\ngige device: [%d]" % i)
            strModeName = ""
            for per in mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName:
                strModeName = strModeName + chr(per)
            print("device model name: %s" % strModeName)

            nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
            nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
            nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
            nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
            print("current ip: %d.%d.%d.%d\n" % (nip1, nip2, nip3, nip4))
        elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
            print("\nu3v device: [%d]" % i)
            strModeName = ""
            for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName:
                if per == 0:
                    break
                strModeName = strModeName + chr(per)
            print("device model name: %s" % strModeName)

            strSerialNumber = ""
            for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                if per == 0:
                    break
                strSerialNumber = strSerialNumber + chr(per)
            print("user serial number: %s" % strSerialNumber)

    nConnectionNum = 0  # For device based selections.

    if int(nConnectionNum) >= deviceList.nDeviceNum:
        print("input error!")
        sys.exit()

    # ch:创建相机实例 | en:Creat Camera Object
    cam = MvCamera()
    cam2 = MvCamera()
    # ch:选择设备并创建句柄 | en:Select device and create handle
    stDeviceList = cast(deviceList.pDeviceInfo[int(nConnectionNum)], POINTER(MV_CC_DEVICE_INFO)).contents
    stDeviceList2 = cast(deviceList.pDeviceInfo[int(1)], POINTER(MV_CC_DEVICE_INFO)).contents

    ret = cam.MV_CC_CreateHandle(stDeviceList)
    ret2 = cam2.MV_CC_CreateHandle(stDeviceList2)

    if ret != 0:
        print("create handle fail! ret[0x%x]" % ret)
        sys.exit()

    # ch:打开设备 | en:Open device
    ret = cam.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
    ret2 = cam2.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)

    if ret != 0:
        print("open device fail! ret[0x%x]" % ret)
        sys.exit()

    # ch:探测网络最佳包大小(只对GigE相机有效) | en:Detection network optimal package size(It only works for the GigE camera)
    if stDeviceList.nTLayerType == MV_GIGE_DEVICE:
        nPacketSize = cam.MV_CC_GetOptimalPacketSize()
        if int(nPacketSize) > 0:
            ret = cam.MV_CC_SetIntValue("GevSCPSPacketSize", nPacketSize)
            if ret != 0:
                print("Warning: Set Packet Size fail! ret[0x%x]" % ret)
        else:
            print("Warning: Get Packet Size fail! ret[0x%x]" % nPacketSize)

    # Second device
    if stDeviceList2.nTLayerType == MV_GIGE_DEVICE:
        nPacketSize2 = cam2.MV_CC_GetOptimalPacketSize()
        if int(nPacketSize2) > 0:
            ret2 = cam2.MV_CC_SetIntValue("GevSCPSPacketSize", nPacketSize2)
            if ret2 != 0:
                print("Warning: Set Packet Size fail! ret[0x%x]" % ret2)
        else:
            print("Warning: Get Packet Size fail! ret[0x%x]" % nPacketSize2)

    stBool = c_bool(False)

    ret = cam.MV_CC_GetBoolValue("AcquisitionFrameRateEnable", stBool)
    ret2 = cam2.MV_CC_GetBoolValue("AcquisitionFrameRateEnable", stBool)

    if ret != 0:
        print("get AcquisitionFrameRateEnable fail! ret[0x%x]" % ret)
        sys.exit()

    # ch:设置触发模式为off | en:Set trigger mode as off
    ret = cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)
    ret2 = cam2.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)

    if ret != 0:
        print("set trigger mode fail! ret[0x%x]" % ret)
        sys.exit()

    # ch:获取数据包大小 | en:Get payload size
    stParam = MVCC_INTVALUE()
    memset(byref(stParam), 0, sizeof(MVCC_INTVALUE))

    ret = cam.MV_CC_GetIntValue("PayloadSize", stParam)
    ret2 = cam2.MV_CC_GetIntValue("PayloadSize", stParam)

    if ret != 0:
        print("get payload size fail! ret[0x%x]" % ret)
        sys.exit()
    nPayloadSize = stParam.nCurValue

    # ch:开始取流 | en:Start grab image
    ret = cam.MV_CC_StartGrabbing()
    ret2 = cam2.MV_CC_StartGrabbing()

    if ret != 0:
        print("start grabbing fail! ret[0x%x]" % ret)
        sys.exit()

    data_buf = (c_ubyte * nPayloadSize)()
    data_buf2 = (c_ubyte * nPayloadSize)()

    try:
        work_thread(cam, byref(data_buf), nPayloadSize, cam2, byref(data_buf2), nPayloadSize,
                    normal_vector=normal_at_vertex)
        # hThreadHandle = threading.Thread(target=work_thread, args=(cam, byref(data_buf), nPayloadSize))
        # hThreadHandle.start()
    finally:
        # ch:停止取流 | en:Stop grab image
        ret = cam.MV_CC_StopGrabbing()
        ret2 = cam2.MV_CC_StopGrabbing()

        if ret != 0:
            print("stop grabbing fail! ret[0x%x]" % ret)
            del data_buf
            sys.exit()

        # ch:关闭设备 | Close device
        ret = cam.MV_CC_CloseDevice()
        ret2 = cam2.MV_CC_CloseDevice()

        if ret != 0:
            print("close deivce fail! ret[0x%x]" % ret)
            del data_buf
            sys.exit()

        # ch:销毁句柄 | Destroy handle
        ret = cam.MV_CC_DestroyHandle()
        ret2 = cam2.MV_CC_DestroyHandle()

        if ret != 0:
            print("destroy handle fail! ret[0x%x]" % ret)
            del data_buf

            sys.exit()

        del data_buf
        del data_buf2
