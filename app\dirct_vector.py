import numpy as np

# === Replace with your actual coordinates ===
M_front = np.array([315, 427.5, 49.3])  # Middle of front face
M_back  = np.array([317, 448, -14.7])  # Middle of back face

# === Compute direction vector ===
direction_vector = M_front - M_back
norm = np.linalg.norm(direction_vector)

if norm == 0:
    raise ValueError("Front and back points are the same — can't compute direction.")

direction_unit = direction_vector / norm

print("Direction Vector (not normalized):", direction_vector)
print("Direction Unit Vector (normalized):", direction_unit)
