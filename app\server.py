import base64
from scipy.spatial import ConvexHull
from starlette.websockets import WebSocketState
import inspect
import math
import time
import cv2
from sklearn.cluster import DBSCAN
from sklearn.linear_model import LinearRegression
import json
import random
import threading
import os
import pickle
import asyncio
import queue
import numpy as np
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from starlette.responses import J<PERSON>NResponse
from general.camera.CaptureCameraImage import DualCameraOperation
from general.common import calibration, utils
from general.common.tracker_object import TriangleTracker


from general.camera.CameraParams_header import MV_FRAME_OUT_INFO_EX
from ctypes import c_ubyte, byref, memset, sizeof
from xarm.wrapper import XArmAPI
from scipy.optimize import least_squares
from pydantic import BaseModel
from general.common.utils import SetplanningInputs

# from PyQt6.QtWidgets import QApplication
# from PyQt6.QtWebEngineWidgets import Q<PERSON>eb<PERSON><PERSON>ineView
# from PyQt6.QtCore import QTimer

current_dir = os.path.dirname(os.path.abspath(__file__))
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.mount(
    "/static",
    StaticFiles(directory=os.path.join(os.path.dirname(__file__), "static")),
    name="static",
)
templates = Jinja2Templates(
    directory=os.path.join(os.path.dirname(__file__), "templates")
)

framecount = 0  # Track how many points are registered
variable_dict = {}  # Store registered points persistently
AutoAlign = False
MixAxialPlane = os.path.abspath(
    os.path.join(current_dir, "..", "..", "registration_data", "mid_Axial_skip.pickle")
)

arm_ip = "*************"  # Replace with your xArm6 IP
USE_ROBOT = True
ROBOT_SPEED = 25
page = None  # Global variable to track the current page
page_lock = asyncio.Lock()  # Lock for thread safety

USE_BURR=False
if USE_BURR:
    from general.common.motor_control import start_motor, stop_motor

async def is_same_page(current_page: str) -> bool:
    """Checks if the global page matches the current page. If not, terminates execution."""
    async with page_lock:
        if page != current_page:
            print(f"Terminating process: Expected {current_page}, but got {page}")
            return False
    return True


class CheckboxState(BaseModel):
    id: str  # Checkbox ID
    state: bool  # True if checked, False if unchecked


def calculate_contact_point(led1, led2, led3, surface_normal, point, radius=6.28):
    """
    Calculate 3D contact coordinates of a tilted hemispherical-tipped pointer.

    Parameters:
    led1, led2, led3 (np.array): 3D coordinates of the three base LEDs
    center_point (np.array): 3D coordinates of pointer tip center (C)
    radius (float): Hemisphere radius in mm

    Returns:
    np.array: Corrected 3D contact coordinates
    """
    # Ensure numpy arrays
    led1, led2, led3, surface_normal, point = map(
        np.array, (led1, led2, led3, surface_normal, point)
    )
    #
    # # Compute surface normal from the three LED positions
    # surface_normal = np.cross(led2 - led1, led3 - led1)
    # surface_normal /= np.linalg.norm(surface_normal)  # Normalize

    # Compute midpoint of LED B and C
    midpoint_bc = (led2 + led3) / 2.0

    # Compute pointer direction vector (from LED A to midpoint of LED B and C)
    v_pointer = midpoint_bc - point
    v_pointer /= np.linalg.norm(v_pointer)  # Normalize

    # Angle between pointer and surface normal
    cos_theta = np.dot(v_pointer, surface_normal)
    cos_theta = np.clip(cos_theta, -1.0, 1.0)  # Avoid numerical issues
    theta = np.arccos(cos_theta)

    # Compute displacement
    displacement = radius * np.sin(theta) * v_pointer

    # Compute contact point
    contact_point = point - displacement

    return contact_point


def load_data(file_path):
    led_points = []
    robot_points = []

    with open(file_path, "r") as file:
        for line in file:
            if "LED" in line and "robot" in line:
                led_part = line.split("LED [")[1].split("]")[0].split()
                robot_part = line.split("robot (")[1].split(")")[0].split(", ")

                # Convert to float
                led_x, led_y, led_z = map(float, led_part)
                robot_x, robot_y, robot_z = map(float, robot_part)

                led_points.append([led_x, led_y, led_z])
                robot_points.append([robot_x, robot_y, robot_z])

    return np.array(led_points), np.array(robot_points)


def get_size(dFCSIZE):
    size_ranges = {
        (float("-inf"), 52.4): "SIZE A",
        (52.5, 54.3): "SIZE B",
        (54.4, 58): "SIZE C",
        (58.1, 59.4): "SIZE D",
        (59.5, 63.2): "SIZE E",
        (63.3, 66.4): "SIZE F",
        (66.5, 70.4): "SIZE G",
        (70.5, float("inf")): "SIZE H",
    }

    for size_range, size in size_ranges.items():
        if size_range[0] <= dFCSIZE <= size_range[1]:
            return size


def get_offset(size_label):
    size_offsets = {
        "SIZE A": 10,
        "SIZE B": 20,
        "SIZE C": 30,
        "SIZE D": 35,
        "SIZE E": 40,
        "SIZE F": 44,
        "SIZE G": 50,
        "SIZE H": 55,
    }
    return size_offsets.get(size_label, None)  # Return None if size not found


def GetRoboticArmInitialData():
    model = LinearRegression()
    robot_calib_data = os.path.join(
        current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
    )
    led_points, robot_points = load_data(file_path=robot_calib_data)
    led_points[:, [1, 2]] = led_points[:, [2, 1]]
    model.fit(led_points, robot_points)
    robot_tracker = np.array(
        [
            [500.94807244, 506.44096767, 351.06449629],
            [508.71337125, 506.19307735, 351.23823142],
            [504.84879152, 495.16849619, 355.14650081],
        ]
    )
    # robot_tracker = np.array(
    #     [
    #         [488.84374735, 504.22307847, 343.85010308],
    #         [496.52488382, 503.91468358, 343.80846174],
    #         [492.78469778, 492.91088977, 347.73767163],
    #     ]
    # )

    if robot_tracker[1][1] >= robot_tracker[0][1]:
        robot_tracker_plane = [robot_tracker[2], robot_tracker[0], robot_tracker[1]]
    else:
        robot_tracker_plane = [robot_tracker[2], robot_tracker[1], robot_tracker[0]]
    modelR = LinearRegression()
    modelR.fit(robot_points, led_points)
    C = [0, 0, 0]
    C = np.array(C).reshape(1, -1)
    old_mark_point = modelR.predict(C)
    old_mark_point[0][1], old_mark_point[0][2] = (
        old_mark_point[0][2],
        old_mark_point[0][1],
    )
    # Step 1: Compute centroid
    centroid = np.mean(robot_tracker_plane, axis=0)

    # Step 2: Compute translation vector
    translation_vector = old_mark_point[0] - centroid

    # Step 3: Translate the plane
    translated_plane = robot_tracker_plane + translation_vector

    data = {
        "robot_tracker_plane": robot_tracker_plane,
        "old_mark_point": old_mark_point[0],
        "translated_plane": translated_plane,
    }
    # print(f'data***************** {data}')
    return data


class HipMarking:
    """Manages camera operations and multi-pipeline processing."""

    def __init__(self):
        self.running = False
        self.frame_queue = queue.Queue(
            maxsize=2
        )  # Increased queue size for better buffering
        self.lock = threading.Lock()
        self.clients = set()
        # Initialize cameras
        self.dual_cam = DualCameraOperation()
        self.dual_cam.initialize_cameras()
        self.dual_cam.start_grabbing()

        self.Camera_left = self.dual_cam.cam
        self.Camera_right = self.dual_cam.cam2
        self.nPayloadSize_left = self.dual_cam.nPayloadSize
        self.nPayloadSize_right = self.dual_cam.nPayloadSize2
        self.data_buf_left = byref((c_ubyte * self.nPayloadSize_left)())
        self.data_buf_right = byref((c_ubyte * self.nPayloadSize_right)())

        self.running = True

        # Start frame capture in a separate thread
        self.capture_thread = threading.Thread(target=self.capture_frames, daemon=True)
        self.capture_thread.start()

    def capture_frames(self):
        """Continuously capture frames in a separate thread and store only the latest one."""
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))

        while self.running:
            try:
                ret1 = self.Camera_left.MV_CC_GetOneFrameTimeout(
                    self.data_buf_left, self.nPayloadSize_left, stFrameInfo, 500
                )
                ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(
                    self.data_buf_right, self.nPayloadSize_right, stFrameInfo, 500
                )

                if ret1 == 0 and ret2 == 0:
                    img_left = np.frombuffer(
                        bytes(self.data_buf_left._obj), np.uint8
                    ).reshape((stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                    img_right = np.frombuffer(
                        bytes(self.data_buf_right._obj), np.uint8
                    ).reshape((stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

                    # Rectify images
                    rec_img_right, rec_img_left = calibration.rectify_images(
                        img_right, img_left
                    )

                    # Store the latest frame
                    with self.lock:
                        if self.frame_queue.full():
                            self.frame_queue.get_nowait()  # Remove the oldest frame
                        self.frame_queue.put((rec_img_left, rec_img_right))
                        time.sleep(0.1)

            except Exception as e:
                print(f"Error capturing frames: {e}")

    async def get_latest_frame(self):
        """Fetch the latest frame if available, otherwise wait briefly."""
        while self.running:
            try:
                with self.lock:
                    if not self.frame_queue.empty():
                        return self.frame_queue.get_nowait()
            except queue.Empty:
                pass
            await asyncio.sleep(0.02)  # Retry after a short delay
        return None

    def write_mid_axial(self, value: bool):
        with self.lock:  # Ensure only one thread writes at a time
            with open(MixAxialPlane, "wb") as file:
                pickle.dump(
                    {"mid_Axial": value}, file, protocol=pickle.HIGHEST_PROTOCOL
                )

    # Thread-Safe Read
    def read_mid_axial(self):
        with self.lock:  # Ensure thread-safe reading
            try:
                with open(MixAxialPlane, "rb") as file:
                    data = pickle.load(file)
                    return data.get(
                        "mid_Axial", None
                    )  # Return True/False or None if missing
            except (FileNotFoundError, EOFError):
                return None  # Handle missing/empty file safely

    async def stop(self):
        """Stop grabbing and clean up."""
        self.running = False
        self.capture_thread.join()  # Ensure the thread stops cleanly
        self.dual_cam.stop_grabbing()
        print("Cameras stopped")


# Global instance of `HipMarking`
marking = HipMarking()
marking.write_mid_axial(False)


def calculate_transformation_matrix_with_scaling(source, target):
    """Calculate transformation matrix with scaling from source points to target points."""
    # Calculate centroids
    centroid_source = np.mean(source, axis=0)
    centroid_target = np.mean(target, axis=0)

    # Center the points
    centered_source = source - centroid_source
    centered_target = target - centroid_target

    # Estimate scaling factor
    scale = np.sum(np.linalg.norm(centered_target, axis=1)) / np.sum(
        np.linalg.norm(centered_source, axis=1)
    )

    # Apply scaling to source points
    scaled_source = centered_source * scale

    # Compute H matrix for SVD
    H = np.dot(scaled_source.T, centered_target)

    # Compute rotation matrix using SVD
    U, S, Vt = np.linalg.svd(H)
    R_matrix = np.dot(Vt.T, U.T)

    # Correct for improper rotation (reflection) if determinant is negative
    if np.linalg.det(R_matrix) < 0:
        Vt[-1, :] *= -1
        R_matrix = np.dot(Vt.T, U.T)

    # Compute translation vector
    t = centroid_target - np.dot(R_matrix, centroid_source * scale)

    # Construct the final transformation matrix
    transformation_matrix = np.eye(4)
    transformation_matrix[:3, :3] = R_matrix * scale
    transformation_matrix[:3, 3] = t

    return transformation_matrix


async def send_tuple(websocket: WebSocket, data: tuple):
    serialized_data = ",".join(map(str, data))
    await websocket.send_text(serialized_data)


async def send_json(websocket: WebSocket, data: dict):
    serialized_data = json.dumps(data)
    await websocket.send_text(serialized_data)


@app.get("/")
async def read_index(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})


@app.get("/values")
async def get_values():
    try:
        # Open the JSON file and read it
        with open(r"D:\SpineSurgery\pythonProject\values.json", "r") as file:
            data = json.load(file)
        return JSONResponse(content=data)
    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)


@app.post("/update_checkbox")
async def update_checkbox_state(data: CheckboxState):
    # Process the received data
    # You can store the state in a database or perform other actions
    print(f"Checkbox ID: {data.id}, State: {data.state}")
    SetplanningInputs(filename=str(data.id), data=str(data.state))
    return {"message": "Checkbox state updated successfully"}


class SkipClickRequest(BaseModel):
    clicked: bool


@app.post("/log_skip_click")
async def log_skip_click(request: SkipClickRequest):
    try:
        # with open(MixAxialPlane, "wb") as file:
        #     variable_memory = {'mid_Axial': request.clicked}
        #     pickle.dump(variable_memory, file, protocol=pickle.HIGHEST_PROTOCOL)
        marking.write_mid_axial(request.clicked)
        return {"success": True}
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.get("/form.html", response_class=HTMLResponse)
async def form(request: Request):
    return templates.TemplateResponse(
        "form.html", {"request": request}
    )  # Load form.html


@app.get("/landing-page.html")
async def read_landing_page(request: Request):
    return templates.TemplateResponse("landing-page.html", {"request": request})


@app.get("/hip/register-acetabulum.html")
async def register_acetabulum1(request: Request):
    return templates.TemplateResponse(
        "/hip/register-acetabulum.html", {"request": request}
    )


@app.get("/revision-thr/handle-position.html")
async def register_acetabulum1(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/handle-position.html", {"request": request}
    )


@app.get("/revision-thr/TP2_marking.html")
async def register_acetabulum1(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/TP2_marking.html", {"request": request}
    )


@app.get("/revision-thr/final-cup-position.html")
async def register_acetabulum1(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/final-cup-position.html", {"request": request}
    )


@app.get("/revision-thr/revhipFreePointCollection.html")
async def register_acetabulum1(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/revhipFreePointCollection.html", {"request": request}
    )


@app.get("/hip/hipRingPointCollection.html")
async def hipRingPointCollection(request: Request):
    return templates.TemplateResponse(
        "/hip/hipRingPointCollection.html", {"request": request}
    )


@app.get("/hip/hipFreePointCollection.html")
async def hipFreePointCollection1(request: Request):
    return templates.TemplateResponse(
        "/hip/hipFreePointCollection.html", {"request": request}
    )


@app.get("/revision-thr/result.html")
async def revhipFreePointCollection1(request: Request):
    return templates.TemplateResponse("/revision-thr/result.html", {"request": request})


@app.get("/hip/handle-position.html")
async def read_handle_position(request: Request):
    return templates.TemplateResponse("/hip/handle-position.html", {"request": request})


@app.get("/hip/anterior-pelvic.html")
async def read_handle_position(request: Request):
    return templates.TemplateResponse("/hip/anterior-pelvic.html", {"request": request})


@app.get("/hip/final-cup-position.html")
async def read_final_cup_position(request: Request):
    return templates.TemplateResponse(
        "/hip/final-cup-position.html", {"request": request}
    )


@app.get("/revision-thr/mid-axial-body-plane.html")
async def revisionHipmidAxisPlane(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/mid-axial-body-plane.html", {"request": request}
    )


@app.get("/revision-thr/register-acetabulum.html")
async def revisionHipmidAxisPlane(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/register-acetabulum.html", {"request": request}
    )





async def rev_register_acetabulum(marking, websocket, callingStage=None):
    global framecount, variable_dict

    try:
        # Load Transformation Plane (TP) variables
        pickle_path_TP = os.path.join(
            current_dir, "..", "..", "registration_data", "tp_variable.pickle"
        )
        with open(pickle_path_TP, "rb") as handle:
            TP = pickle.load(handle)
        print("✅ TP variables loaded successfully")

    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    point_reg = ["ASIS", "TF", "PSIS", "IT", "TAL", "PS"]

    try:
        while True:
            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame
            else:
                return

            # Find contours using K-means clustering
            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)

            if len(detections_right) == 6 and len(detections_left) == 6:
                break  # Exit loop when both have 6 detections
            else:
                print("⚠️ Detection failed: Expected 6 points per frame.")
                continue  # Retry in the loop

        detections_right.sort(key=lambda x: x[0])
        detections_left.sort(key=lambda x: x[0])

        # Extract tracked points
        first_three_right, next_three_right = (
            detections_right[:3],
            detections_right[-3:],
        )
        first_three_left, next_three_left = detections_left[:3], detections_left[-3:]

        # Track femur (static reference points)
        femur = TriangleTracker(
            tuple(first_three_right),
            tuple(first_three_left),
            rec_img_right,
            rec_img_left,
        )
        femur.find_depth()
        F_points = femur.getLEDcordinates()
        if F_points[1][1] >= F_points[0][1]:
            Tracker_plane = [F_points[2], F_points[0], F_points[1]]
        else:
            Tracker_plane = [F_points[2], F_points[1], F_points[0]]

        Pointer_tracker = TriangleTracker(
            tuple(next_three_right), tuple(next_three_left), rec_img_right, rec_img_left
        )
        Pointer_tracker.find_depth()
        pointer_tip_gcs = Pointer_tracker.getStylusPoint()

        pointRegistered = utils.find_new_point_location(
            old_plane_points=Tracker_plane,
            new_plane_points=TP["TP"]["Plane"],
            old_marked_point=pointer_tip_gcs,
        )

        variable_memory = {"C": pointRegistered, "Plane": TP["TP"]["Plane"]}
        variable_dict[point_reg[framecount]] = variable_memory
        print(f"🔵 Registered {point_reg[framecount]}: {pointRegistered}")
        # Attempt WebSocket communication
        try:
            await send_tuple(websocket, (f"{point_reg[framecount]}", 4.0, 5.6, 7.3))
        except Exception as e:
            print(f"⚠️ WebSocket send failed: {e}")
        # Play notification sound
        utils.play_notification_sound()

        framecount += 1  # Move to the next point
        await asyncio.sleep(0.1)  # 100ms delay

        if framecount < len(point_reg):
            await rev_register_acetabulum(
                marking, websocket, callingStage
            )  # Avoid deep recursion
        else:
            print("🎯 All 6 points registered. Saving data...")
            await send_tuple(websocket, ("exit", 1, 3, 0))
            if not callingStage:
                Hip_threePoint_path = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "registration_data",
                    "revision_hip_variables.pickle",
                )
            else:
                Hip_threePoint_path = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "registration_data",
                    f"revision_hip_variables.pickle{callingStage}",
                )
            os.makedirs(os.path.dirname(Hip_threePoint_path), exist_ok=True)

            try:
                with open(Hip_threePoint_path, "wb") as handle:
                    pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
                print("✅ Pickle dump successful.")

            except (OSError, pickle.PickleError) as e:
                print(f"❌ Error saving hip variables: {e}")

            # Reset variables for the next session
            framecount = 0
            variable_dict = {}
            return

    except Exception as e:
        print(f"❌ Unexpected error in register_acetabulum: {e}")
        await asyncio.sleep(0.1)  # 100ms delay
        return


async def register_acetabulum(marking, websocket, callingStage=None):
    global framecount, variable_dict

    try:
        # Load Transformation Plane (TP) variables
        pickle_path_TP = os.path.join(
            current_dir, "..", "..", "registration_data", "tp_variable.pickle"
        )
        with open(pickle_path_TP, "rb") as handle:
            TP = pickle.load(handle)
        print("✅ TP variables loaded successfully")

    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    point_reg = ["FO", "SU", "CA", "AT", "PT"]

    try:
        while True:
            # Ensure frames are available in the queue
            # if marking.frame_queue.qsize() == 0:
            #     print("⚠️ Frame queue is empty, skipping this cycle.")
            #     await asyncio.sleep(0.5)  # Wait before checking again
            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame


            # Find contours using K-means clustering
            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)

            if len(detections_right) == 6 and len(detections_left) == 6:
                print("⚠️ Detection failed: Expected 6 points per frame.")
                break
            if page != "register-acetabulum.html":
                return
        detections_right.sort(key=lambda x: x[0])
        detections_left.sort(key=lambda x: x[0])

        # Extract tracked points
        first_three_right, next_three_right = (
            detections_right[:3],
            detections_right[-3:],
        )
        first_three_left, next_three_left = detections_left[:3], detections_left[-3:]

        # Track femur (static reference points)
        femur = TriangleTracker(
            tuple(first_three_right),
            tuple(first_three_left),
            rec_img_right,
            rec_img_left,
        )
        femur.find_depth()
        F_points = femur.getLEDcordinates()
        Tracker_plane = [F_points[2], F_points[1], F_points[0]]

        Pointer_tracker = TriangleTracker(
            tuple(next_three_right), tuple(next_three_left), rec_img_right, rec_img_left
        )
        Pointer_tracker.find_depth()
        pointer_tip_gcs = Pointer_tracker.getStylusPoint()

        pointRegistered = utils.find_new_point_location(
            old_plane_points=Tracker_plane,
            new_plane_points=TP["TP"]["Plane"],
            old_marked_point=pointer_tip_gcs,
        )
        variable_memory = {"C": pointRegistered, "Plane": TP["TP"]["Plane"]}
        variable_dict[point_reg[framecount]] = variable_memory
        print(f"🔵 Registered {point_reg[framecount]}: {pointRegistered}")

        # Attempt WebSocket communication
        try:
            await send_tuple(websocket, (f"{point_reg[framecount]}", 4.0, 5.6, 7.3))
        except Exception as e:
            print(f"⚠️ WebSocket send failed: {e}")
        # Play notification sound
        utils.play_notification_sound()

        framecount += 1  # Move to the next point
        await asyncio.sleep(0.1)  # 100ms delay

        if framecount < 5:
            await register_acetabulum(
                marking, websocket, callingStage
            )  # Avoid deep recursion
        else:
            print("🎯 All 5 points registered. Saving data...")
            if not callingStage:
                Hip_threePoint_path = os.path.join(
                    current_dir, "..", "..", "registration_data", "hip_variables.pickle"
                )
            else:
                Hip_threePoint_path = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "registration_data",
                    f"hip_variables.pickle{callingStage}",
                )
            os.makedirs(os.path.dirname(Hip_threePoint_path), exist_ok=True)

            try:
                with open(Hip_threePoint_path, "wb") as handle:
                    pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
                print("✅ Pickle dump successful.")

            except (OSError, pickle.PickleError) as e:
                print(f"❌ Error saving hip variables: {e}")

            # Reset variables for the next session
            framecount = 0
            variable_dict = {}
            return

    except Exception as e:
        print(f"❌ Unexpected error in register_acetabulum: {e}")
        await asyncio.sleep(0.1)  # 100ms delay
        return


async def hipmidAxialBodyPlane(marking, websocket):
    global framecount, variable_dict

    try:
        pickle_path_TP = os.path.join(
            current_dir, "..", "..", "registration_data", "tp_variable.pickle"
        )
        with open(pickle_path_TP, "rb") as handle:
            TP = pickle.load(handle)
            print("TP variables loaded successfully")
    except Exception as e:
        print(f"Error loading TP variables {e}")

    point_reg = ["NOTE1", "NOTE2"]

    try:
        while True:
            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame
            else:
                return
            early_stop = marking.read_mid_axial()
            print(f"early_stop {early_stop}")
            if early_stop in (None, True):
                framecount = 0
                return
            # Find contours using K-means clustering
            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)
            print(f"{len(detections_right)} {len(detections_left)}")
            if len(detections_right) == 6 and len(detections_left) == 6:
                print("⚠️ Detection failed: Expected 6 points per frame.")
                await asyncio.sleep(0.1)
                break

        detections_right.sort(key=lambda x: x[0])
        detections_left.sort(key=lambda x: x[0])

        # Extract tracked points
        first_three_right, next_three_right = (
            detections_right[:3],
            detections_right[-3:],
        )
        first_three_left, next_three_left = detections_left[:3], detections_left[-3:]

        # Track femur (static reference points)
        femur = TriangleTracker(
            tuple(first_three_right),
            tuple(first_three_left),
            rec_img_right,
            rec_img_left,
        )
        femur.find_depth()
        F_points = femur.getLEDcordinates()
        Tracker_plane = [F_points[2], F_points[1], F_points[0]]

        Pointer_tracker = TriangleTracker(
            tuple(next_three_right), tuple(next_three_left), rec_img_right, rec_img_left
        )
        Pointer_tracker.find_depth()
        pointer_tip_gcs = Pointer_tracker.getStylusPoint()

        pointRegistered = utils.find_new_point_location(
            old_plane_points=Tracker_plane,
            new_plane_points=TP["TP"]["Plane"],
            old_marked_point=pointer_tip_gcs,
        )

        variable_memory = {"C": pointRegistered, "Plane": TP["TP"]["Plane"]}
        variable_dict[point_reg[framecount]] = variable_memory
        print(f"🔵 Registered {point_reg[framecount]}: {pointRegistered}")
        try:
            await send_tuple(websocket, (f"{point_reg[framecount]}", 4.0, 5.6, 7.3))
        except Exception as e:
            print(f"⚠️ WebSocket send failed: {e}")
        # Play notification sound
        utils.play_notification_sound()

        # Attempt WebSocket communication

        framecount += 1  # Move to the next point
        await asyncio.sleep(0.1)  # 100ms delay

        if framecount < len(point_reg):

            await hipmidAxialBodyPlane(marking, websocket)  # Avoid deep recursion
        else:
            print("🎯 All 2 points registered. Saving data...")

            Hip_threePoint_path = os.path.join(
                current_dir,
                "..",
                "..",
                "registration_data",
                f"hip_two_variables.pickle",
            )
            os.makedirs(os.path.dirname(Hip_threePoint_path), exist_ok=True)

            try:
                with open(Hip_threePoint_path, "wb") as handle:
                    pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
                print("✅ Pickle dump successful.")

            except (OSError, pickle.PickleError) as e:
                print(f"❌ Error saving hip variables: {e}")

            # Reset variables for the next session
            framecount = 0
            variable_dict = {}

            return

    except Exception as e:
        print(f"❌ Unexpected error in register_acetabulum: {e}")
        await asyncio.sleep(0.1)  # 100ms delay
        return


@app.get("/hip/leg-length-assessment.html")
async def read_leg_length_assessment(request: Request):
    return templates.TemplateResponse(
        "/hip/leg-length-assessment.html", {"request": request}
    )


@app.get("/hip/mid-axial-body-plane.html")
async def mid_axial_body_plane(request: Request):
    return templates.TemplateResponse(
        "/hip/mid-axial-body-plane.html", {"request": request}
    )


@app.get("/revision-thr/leg-length-assessment.html")
async def read_leg_length_assessment(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/leg-length-assessment.html", {"request": request}
    )


@app.get("/hip/acetabulum-component-placement.html")
async def read_acetabulum_component_placement(request: Request):
    return templates.TemplateResponse(
        "/hip/acetabulum-component-placement.html", {"request": request}
    )


@app.get("/hip/TP2_marking.html")
async def revtp2_marking(request: Request):
    return templates.TemplateResponse("/hip/TP2_marking.html", {"request": request})


@app.get("/revision-thr/TP2_marking.html")
async def revtp2_marking(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/TP2_marking.html", {"request": request}
    )


@app.get("/hip/operating-positio-screen.html")
async def read_operating_position_screen(request: Request):
    return templates.TemplateResponse(
        "/hip/operating-positio-screen.html", {"request": request}
    )


@app.get("/revision-thr/operating-positio-screen.html")
async def read_operating_position_screen(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/operating-positio-screen.html", {"request": request}
    )


@app.get("/hip/system-setup.html")
async def system_setup():
    return templates.TemplateResponse("/hip/system-setup.html", {"request": {}})


@app.get("/revision-thr/system-setup.html")
async def system_setup():
    return templates.TemplateResponse(
        "/revision-thr/system-setup.html", {"request": {}}
    )


@app.get("/hip/femur-registration.html")
async def read_femur_registration():
    return templates.TemplateResponse("/hip/femur-registration.html", {"request": {}})


@app.get("/revision-thr/femur-registration.html")
async def read_femur_registration():
    return templates.TemplateResponse(
        "/revision-thr/femur-registration.html", {"request": {}}
    )


# @app.get("/hip/pelvis-registration.html")
# async def read_pelvis_registration():
#     return templates.TemplateResponse("/hip/pelvis-registration.html", {"request": {}})


# @app.get("/revision-thr/pelvis-registration.html")
# async def read_pelvis_registration():
#     return templates.TemplateResponse("/revision-thr/pelvis-registration.html", {"request": {}})


@app.get("/hip/pelvis-registration-2.html")
async def read_pelvis_registration_2(request: Request):
    return templates.TemplateResponse(
        "/hip/pelvis-registration-2.html", {"request": request}
    )


@app.get("/revision-thr/pelvis-registration-2.html")
async def read_pelvis_registration_2(request: Request):
    return templates.TemplateResponse(
        "/revision-thr/pelvis-registration-2.html", {"request": request}
    )


async def hip_pelvis_registration_2(marking, websocket):
    global framecount, variable_dict

    try:
        pickle_path_TP = os.path.join(
            current_dir, "..", "..", "registration_data", "tp_variable.pickle"
        )
        with open(pickle_path_TP, "rb") as handle:
            TP = pickle.load(handle)
            print("✅ TP variables loaded successfully")
    except Exception as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    point_reg = ["ASL", "ASR", "PSC"]  # Points to register

    try:
        while True:

            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame
            else:
                return

            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)
            if len(detections_right) == 6 and len(detections_left) == 6:
                print("⚠️ Detection failed: Expected 6 points per frame.")
                break
        # if len(detections_right) == 6 and len(detections_left) == 6:
        detections_right.sort(key=lambda x: x[0])
        detections_left.sort(key=lambda x: x[0])

        first_three_right = detections_right[:3]
        first_three_left = detections_left[:3]
        next_three_right = detections_right[-3:]
        next_three_left = detections_left[-3:]

        femur = TriangleTracker(
            tuple(first_three_right),
            tuple(first_three_left),
            rec_img_right,
            rec_img_left,
        )
        femur.find_depth()
        F_points = femur.getLEDcordinates()
        Tracker_plane = [F_points[2], F_points[1], F_points[0]]

        if framecount < 3:
            Pointer_tracker = TriangleTracker(
                tuple(next_three_right),
                tuple(next_three_left),
                rec_img_right,
                rec_img_left,
            )
            Pointer_tracker.find_depth()
            pointer_tip_gcs = Pointer_tracker.getStylusPoint()

            pointRegistered = utils.find_new_point_location(
                old_plane_points=Tracker_plane,
                new_plane_points=TP["TP"]["Plane"],
                old_marked_point=pointer_tip_gcs,
            )

            variable_memory = {"C": pointRegistered, "Plane": TP["TP"]["Plane"]}
            variable_dict[point_reg[framecount]] = variable_memory
            print(f"🔵 Registered {point_reg[framecount]}: {pointRegistered}")
            await send_tuple(websocket, (point_reg[framecount], 1, 3, 0))
            utils.play_notification_sound()
            # data = {
            #     "status": "point_registered",
            #     "point_name": point_reg[framecount],
            #     "point_coordinates": pointRegistered.tolist()
            # }
            # await websocket.send_json(data)

            framecount += 1
            await asyncio.sleep(0.1)

            if framecount < 3:
                await hip_pelvis_registration_2(marking, websocket)
            else:
                print("🎯 All 3 points registered. Saving data...")
                Hip_threePoint_path = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "registration_data",
                    "hip_three_variables.pickle",
                )
                os.makedirs(os.path.dirname(Hip_threePoint_path), exist_ok=True)

                with open(Hip_threePoint_path, "wb") as handle:
                    pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

                print("✅ Pickle dump successful.")
                framecount = 0
                variable_dict = {}
                return {}
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

    finally:
        print("🛑 Terminating registration process and discarding partial points.")
        framecount = 0
        variable_dict = {}
        return {}


def set_robotic_arm_manual_mode():
    try:
        # Connect to xArm6
        arm = XArmAPI(arm_ip)

        # Check if the connection was successful
        if not arm.connected:
            print("Failed to connect to xArm6.")
            return

        # Retrieve current mode and state
        mode = arm.mode
        state = arm.state

        print(f"Current Mode: {mode}, State: {state}")

        # If the arm is paused, set it to IDLE
        if state == 2:  # Paused state
            print("Arm is in Paused state. Switching to IDLE...")
            arm.set_state(0)  # Set state to IDLE
            time.sleep(0.5)

        # Enable motion & servo power
        arm.motion_enable(True)
        arm.set_servo_attach()  # Ensure servos are powered on
        time.sleep(0.5)

        # Verify motion enable
        if arm.state in [4, 5]:  # Error or protective stop state
            print("Motion enable failed. Trying to clear errors...")
            arm.clean_error()
            arm.clean_warn()
            time.sleep(0.5)
            arm.motion_enable(True)
            arm.set_servo_attach()

        # Ensure it's in Manual Mode
        if mode == 2:
            print("Already in Manual Mode.")
        else:
            print("Switching to Manual Mode...")
            arm.set_mode(2)
            time.sleep(0.5)
            arm.set_state(0)  # Set state to IDLE
            time.sleep(0.5)

        print("Manual Mode is now active. You can move the robotic arm manually.")

    except Exception as e:
        print(f"An error occurred: {e}")

    finally:
        # Ensure disconnection only if connected
        if arm.connected:
            try:
                arm.disconnect()
            except Exception as e:
                print(f"Error while disconnecting: {e}")


def set_robotic_arm_ready_to_move(axis_angle_pose):
    try:
        # Connect to xArm6
        arm = XArmAPI(arm_ip)  # Replace with your xArm6 IP

        if arm is None:
            print("Failed to connect to xArm6.")
            return

        # Get the current mode and state
        try:
            mode = arm.mode  # Read mode
            state = arm.state  # Read state
            print(f"Current Mode: {mode}, State: {state}")
        except Exception as e:
            print(f"Error retrieving mode/state: {e}")
            arm.disconnect()
            return

        # Switch to Manual Mode if not already
        if mode != 0:
            try:
                arm.motion_enable(enable=True)  # Enable motion
                arm.set_mode(0)  # Set to manual mode
                arm.set_state(0)  # Set to ready state
                print("Switched to Manual Mode.")
            except Exception as e:
                print(f"Error switching to Manual Mode: {e}")
                arm.disconnect()
                return
        else:
            print("Already in Manual Mode.")

        # Get current position
        try:
            code, position = arm.get_position_aa()
            if code == 0:
                x, y, z, rx, ry, rz = position  # RX, RY, RZ are in radians
                axis_angle_pose[:3] = [x, y, z]  # Update X, Y, Z
            else:
                print(f"Failed to retrieve position, error code: {code}")
                arm.disconnect()
                return
        except Exception as e:
            print(f"Error retrieving position: {e}")
            arm.disconnect()
            return

        # Move the robotic arm
        try:
            code = arm.set_position_aa(axis_angle_pose, speed=45, mvacc=500, wait=True)
            if code != 0:
                print(f"Failed to move arm, error code: {code}")
        except Exception as e:
            print(f"Error setting position: {e}")

    except Exception as e:
        print(f"Unexpected error: {e}")

    finally:
        # Ensure the arm disconnects even in case of an error
        arm.disconnect()
        print("Disconnected from xArm6.")


async def hip_final_cup_position(marking, websocket):
    global USE_ROBOT
    cup_seating = None
    set_robotic_arm_manual_mode()
    initial_data = GetRoboticArmInitialData()
    FILE_PATH = os.path.join(
        current_dir, "..", "..", "registration_data", "robotic_positions.json"
    )
    if os.path.exists(FILE_PATH):
        with open(FILE_PATH, "r") as file:
            loaded_data = json.load(file)
    data_dict = {
        "antiversion": random.randint(20, 70),  # Random between 0 and 50
        "inclination": random.randint(5, 50),  # Random between 20 and 70
        "status_colour": "red",  # Keeping within expected range
        "status_text": "Auto Align",  # Keeping within expected range
    }
    await send_json(websocket, data_dict)
    await asyncio.sleep(2)  # ✅ Prevents CPU Overload

    try:
        while True:  # ✅ Continuous Loop Instead of Recursion
            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame
            else:
                return
                # continue  # Skip execution if no frames are available

            # ✅ Process hip position as before...
            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)

            if len(detections_right) == 6 and len(detections_left) == 6:
                detections_right = sorted(
                    detections_right, key=lambda x: x[1], reverse=True
                )
                detections_left = sorted(
                    detections_left, key=lambda x: x[1], reverse=True
                )
                first_three_right = sorted(detections_right[:3], key=lambda x: x[0])
                first_three_left = sorted(detections_left[:3], key=lambda x: x[0])

                next_three_right = sorted(detections_right[3:], key=lambda x: x[0])
                next_three_left = sorted(detections_left[3:], key=lambda x: x[0])

                femure = TriangleTracker(
                    next_three_right,
                    next_three_left,
                    rec_img_right,
                    rec_img_left,
                )
                femure.find_depth()
                F_points = femure.getLEDcordinates()

                robot_tracker = TriangleTracker(
                    first_three_right,
                    first_three_left,
                    rec_img_right,
                    rec_img_left,
                )
                robot_tracker.find_depth()
                R_points = robot_tracker.getLEDcordinates()

                new_origin = utils.find_new_point_location(
                    old_plane_points=initial_data["robot_tracker_plane"],
                    new_plane_points=tuple(R_points),
                    old_marked_point=initial_data["old_mark_point"],
                )

                # Step 1: Compute centroid
                centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)

                # Step 2: Compute translation vector
                translation_vector = new_origin - centroid

                # Step 3: Translate the plane
                Newtranslated_plane = (
                    initial_data["robot_tracker_plane"] + translation_vector
                )
                robot_calib_data = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "robotic_calib_data",
                    "robotic_init_data.txt",
                )
                led_points, robot_points = load_data(file_path=robot_calib_data)
                # led_points[:, [1, 2]] = led_points[:, [2, 1]]
                transformed_led_points = np.array(
                    [
                        utils.find_new_point_location(
                            old_plane_points=initial_data["translated_plane"],
                            new_plane_points=tuple(Newtranslated_plane),
                            old_marked_point=led,
                        )
                        for led in led_points
                    ]
                )

                # Swap Y and Z back to match coordinate system
                transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

                updated_model = LinearRegression()
                updated_model.fit(transformed_led_points, robot_points)
                # hip_dict['FO']['C']
                point1 = utils.find_new_point_location(
                    old_plane_points=hip_dict["FO"]["Plane"],
                    new_plane_points=tuple(Newtranslated_plane),
                    old_marked_point=hip_dict["FO"]["C"],
                )
                pointF0 = np.array(point1).reshape(1, -1)
                # point1 = point1 - old_origin_optical + new_origin
                predicted_dest = updated_model.predict(pointF0)

                ANTERVERSION_angle, INCLINATION_angle = (
                    utils.calculate_ANTERVERSION_INCLINATION_angles(
                        R_points[1] - R_points[0],
                        F_points[1] - F_points[0],
                        np.cross(F_points[1] - F_points[0], F_points[2] - F_points[1]),
                    )
                )
                try:
                    message = load_data(websocket.receive_text())
                    if message.get("action") == "auto_align":
                        print(f"*" * 100)
                        print(f'message.get("action")')
                        print(f"*" * 100)
                        AutoAlign = True

                except Exception as e:
                    continue
                if AutoAlign:
                    axis_angle_pose = [
                        loaded_data["X"],
                        loaded_data["Y"],
                        loaded_data["Z"],
                        loaded_data["RX"],
                        loaded_data["RY"],
                        loaded_data["RZ"],
                    ]
                    set_robotic_arm_ready_to_move(axis_angle_pose)
                    AutoAlign = False
                    data_dict = {
                        "antiversion": random.randint(
                            20, 70
                        ),  # Random between 0 and 50
                        "inclination": random.randint(
                            5, 50
                        ),  # Random between 20 and 70
                        "status_colour": "green",  # Keeping within expected range
                        "status_text": "Auto Align",  # Keeping within expected range
                    }
                    await send_json(websocket, data_dict)
                    await asyncio.sleep(2)  # ✅ Prevents CPU Overload
                    cup_seating = True
                    continue
                if cup_seating:
                    data_dict = {
                        "antiversion": random.randint(
                            20, 70
                        ),  # Random between 0 and 50
                        "inclination": random.randint(
                            5, 50
                        ),  # Random between 20 and 70
                        "status_colour": "red",  # Keeping within expected range
                        "status_text": "Cup Seating",  # Keeping within expected range
                    }
                    await send_json(websocket, data_dict)
                    await asyncio.sleep(2)  # ✅ Prevents CPU Overload
                    cup_seating = False
                    continue

                arm = XArmAPI(arm_ip)  # Replace with your xArm6 IP
                if arm.connected:
                    code, position = arm.get_position_aa()
                    if code == 0:  # Successful retrieval
                        x, y, z, rx, ry, rz = position  # RX, RY, RZ are in radians
                        data1 = abs(ANTERVERSION_angle) - rx
                        data2 = abs(INCLINATION_angle) - (180 - ry)
                        distance = utils.distance_3d(
                            np.array(
                                [
                                    predicted_dest[0][0],
                                    predicted_dest[0][1],
                                    predicted_dest[0][2],
                                ]
                            ),
                            np.array([x, y, z]),
                        )
                        if distance < 0.3:
                            data_dict = {
                                "antiversion": random.randint(
                                    20, 70
                                ),  # Random between 0 and 50
                                "inclination": random.randint(
                                    5, 50
                                ),  # Random between 20 and 70
                                "status_colour": "green",  # Keeping within expected range
                                "status_text": "Cup Seating",  # Keeping within expected range
                            }
                await send_json(websocket, data_dict)
                await asyncio.sleep(0.5)  # ✅ Prevents CPU Overload

    except WebSocketDisconnect:
        print("WebSocket disconnected, stopping hip position tracking.")
    except Exception as e:
        print(f"Unexpected error: {e}")


def fit_plane(point_cloud):
    """
    input
        point_cloud : list of xyz values　numpy.array
    output
        plane_v : (normal vector of the best fit plane)
        com : center of mass
    """

    com = np.sum(point_cloud, axis=0) / len(point_cloud)
    # calculate the center of mass
    q = point_cloud - com
    # move the com to the origin and translate all the points (use numpy broadcasting)
    Q = np.dot(q.T, q)
    # calculate 3x3 matrix. The inner product returns total sum of 3x3 matrix
    la, vectors = np.linalg.eig(Q)
    # Calculate eigenvalues and eigenvectors
    plane_v = vectors.T[np.argmin(la)]
    # Extract the eigenvector of the minimum eigenvalue

    return plane_v, com


async def hip_handle_position(marking, websocket):
    global USE_ROBOT

    try:
        Hip_variable_path = os.path.join(
            current_dir, "..", "..", "registration_data", "hip_variables.pickle"
        )
        with open(Hip_variable_path, "rb") as handle:
            hip_dict = pickle.load(handle)
            print("Hip variables loaded successfully")

        Hip_point_cloud_file = os.path.join(
            current_dir, "..", "..", "registration_data", "RingPointCloud.pickle"
        )
        with open(Hip_point_cloud_file, "rb") as handle:
            pointCloud_dict = pickle.load(handle)
            print("Hip PointCloud loaded successfully")

    except Exception as e:
        print(f"Error loading TP variables: {e}")
        return

    for key, value in pointCloud_dict.items():
        locals()[key] = value["C"]
    point_reg = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"]
    initial_data = GetRoboticArmInitialData()
    if USE_ROBOT:
        try:
            set_robotic_arm_manual_mode()
        except Exception as e:
            print("error")

    SAFE_CENTER = {"inclination": 15, "antiversion": 40}
    SAFE_RANGE = 2  # ±2 degrees

    in_safe_zone = (
        SAFE_CENTER["inclination"] - SAFE_RANGE
        <= SAFE_CENTER["inclination"]
        <= SAFE_CENTER["inclination"] + SAFE_RANGE
        and SAFE_CENTER["antiversion"] - SAFE_RANGE
        <= SAFE_CENTER["antiversion"]
        <= SAFE_CENTER["antiversion"] + SAFE_RANGE
    )

    FILE_PATH = os.path.join(
        current_dir, "..", "..", "registration_data", "robotic_positions.json"
    )
    if os.path.exists(FILE_PATH):
        os.remove(FILE_PATH)
        print(f"🗑️ Existing file '{FILE_PATH}' removed before starting.")
    try:
        while True:  # ✅ Continuous Loop Instead of Recursion
            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame
            else:
                return
                # continue  # Skip execution if no frames are available

            # ✅ Process hip position as before...
            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)

            if len(detections_right) == 6 and len(detections_left) == 6:
                detections_right = sorted(
                    detections_right, key=lambda x: x[1], reverse=True
                )
                detections_left = sorted(
                    detections_left, key=lambda x: x[1], reverse=True
                )
                first_three_right = sorted(detections_right[:3], key=lambda x: x[0])
                first_three_left = sorted(detections_left[:3], key=lambda x: x[0])

                next_three_right = sorted(detections_right[3:], key=lambda x: x[0])
                next_three_left = sorted(detections_left[3:], key=lambda x: x[0])

                femure = TriangleTracker(
                    next_three_right,
                    next_three_left,
                    rec_img_right,
                    rec_img_left,
                )
                femure.find_depth()
                F_points = femure.getLEDcordinates()

                robot_tracker = TriangleTracker(
                    first_three_right,
                    first_three_left,
                    rec_img_right,
                    rec_img_left,
                )
                robot_tracker.find_depth()
                R_points = robot_tracker.getLEDcordinates()
                # R  (array([501.44909144, 557.82327568, 366.49651939]), array([509.29146161, 557.83370479, 367.43625405]), array([505.66557745, 547.25822684, 373.17744552]))
                new_origin = utils.find_new_point_location(
                    old_plane_points=initial_data["robot_tracker_plane"],
                    new_plane_points=tuple(R_points),
                    old_marked_point=initial_data["old_mark_point"],
                )

                # Step 1: Compute centroid
                centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)

                # Step 2: Compute translation vector
                translation_vector = new_origin - centroid

                # Step 3: Translate the plane
                Newtranslated_plane = (
                    initial_data["robot_tracker_plane"] + translation_vector
                )
                robot_calib_data = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "robotic_calib_data",
                    "robotic_init_data.txt",
                )
                led_points, robot_points = load_data(file_path=robot_calib_data)
                # led_points[:, [1, 2]] = led_points[:, [2, 1]]
                transformed_led_points = np.array(
                    [
                        utils.find_new_point_location(
                            old_plane_points=initial_data["translated_plane"],
                            new_plane_points=tuple(Newtranslated_plane),
                            old_marked_point=led,
                        )
                        for led in led_points
                    ]
                )

                # Swap Y and Z back to match coordinate system
                transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

                updated_model = LinearRegression()
                updated_model.fit(transformed_led_points, robot_points)
                # hip_dict['FO']['C']
                point1 = utils.find_new_point_location(
                    old_plane_points=hip_dict["FO"]["Plane"],
                    new_plane_points=tuple(Newtranslated_plane),
                    old_marked_point=hip_dict["FO"]["C"],
                )
                pointF0 = np.array(point1).reshape(1, -1)
                # point1 = point1 - old_origin_optical + new_origin
                predicted_dest = updated_model.predict(pointF0)

                Mid_point_RoboTracker = np.abs((R_points[1] + R_points[0]) / 2)
                RoboTracker_y = Mid_point_RoboTracker - R_points[2]
                RoboTracker_x = R_points[1] - R_points[0]

                RoboTrackervector_z = np.cross(RoboTracker_x, RoboTracker_y)

                # Normalize the Z-axis vector (to make it a unit vector)
                RoboTrackernorm_z = np.linalg.norm(RoboTrackervector_z)
                RoboTrackernormalized_z = RoboTrackervector_z / RoboTrackernorm_z
                RoboTrackernorm_x = np.linalg.norm(RoboTracker_x)
                RoboTrackernorm_y = np.linalg.norm(RoboTracker_y)
                RoboTrackernormalized_x = (
                    RoboTracker_x / RoboTrackernorm_x
                    if RoboTrackernorm_x != 0
                    else RoboTracker_x
                )
                RoboTrackernormalized_y = (
                    RoboTracker_y / RoboTrackernorm_y
                    if RoboTrackernorm_y != 0
                    else RoboTracker_y
                )
                if F_points[1][1] >= F_points[0][1]:
                    hip_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    hip_plane = [F_points[2], F_points[1], F_points[0]]

                mid_femure = np.abs((F_points[1] + F_points[0]) / 2)

                scale = 107 / utils.distance_3d(mid_femure, F_points[2])

                SU = utils.find_new_point_location(
                    hip_dict["SU"]["Plane"], hip_plane, hip_dict["SU"]["C"]
                )
                CA = utils.find_new_point_location(
                    hip_dict["CA"]["Plane"], hip_plane, hip_dict["CA"]["C"]
                )
                AT = utils.find_new_point_location(
                    hip_dict["AT"]["Plane"], hip_plane, hip_dict["AT"]["C"]
                )
                PT = utils.find_new_point_location(
                    hip_dict["PT"]["Plane"], hip_plane, hip_dict["PT"]["C"]
                )
                pointCloud = []
                for char in point_reg:
                    result = utils.find_new_point_location(
                        pointCloud_dict[char]["Plane"],
                        hip_plane,
                        pointCloud_dict[char]["C"],
                    )
                    pointCloud.append(result)

                pointCloud_array = np.array(pointCloud)
                accetabulumnAxis_x, com = fit_plane(pointCloud_array)
                accetabulumnAxisXnorm = accetabulumnAxis_x / np.linalg.norm(
                    accetabulumnAxis_x
                )
                accetabulumnAxis_y = np.array(SU) - np.array(
                    CA
                )  # Directly subtract the two arrays
                accetabulumnAxis_ynorm = accetabulumnAxis_y / np.linalg.norm(
                    accetabulumnAxis_y
                )
                accetabulumnAxis_z = np.cross(accetabulumnAxis_x, accetabulumnAxis_y)
                accetabulumnAxis_z_norm = accetabulumnAxis_z / np.linalg.norm(
                    accetabulumnAxis_z
                )

                disance_SU_CA = utils.distance_3d(SU, CA)
                accetabulumn_diameterinMM = disance_SU_CA * scale

                ANTERVERSION_angle, INCLINATION_angle = (
                    utils.calculate_ANTERVERSION_INCLINATION_angles(
                        RoboTrackernormalized_y,
                        accetabulumnAxisXnorm,
                        accetabulumnAxis_z_norm,
                    )
                )
                print(f'ANTERVERSION_angle {ANTERVERSION_angle}   INCLINATION_angle {INCLINATION_angle}')
                if USE_ROBOT:
                    from xarm.wrapper import XArmAPI

                    arm = XArmAPI(arm_ip)
                    arm.connect()

                    if arm.connected:
                        arm.motion_enable(enable=True)
                        code, position = arm.get_position_aa()

                        if code == 0:
                            x, y, z, rx, ry, rz = position
                            print(f"Current Position:\nX: {x} mm\nY: {y} mm\nZ: {z} mm   rx {rx}  ry {ry}")
                            data1 = 15 + abs(ANTERVERSION_angle) - (ry - 90)

                            data2 = 40 + abs(INCLINATION_angle) - rz
                            distance = utils.distance_3d(
                                np.array(
                                    [
                                        predicted_dest[0][0],
                                        predicted_dest[0][1],
                                        predicted_dest[0][2],
                                    ]
                                ),
                                np.array([x, y, z]),
                            )
                            data_dict = {
                                "antiversion": data2,  # Random between 0 and 50
                                "inclination": data1,  # Random between 20 and 70
                                "diameter": accetabulumn_diameterinMM,  # Keep diameter fixed or modify as needed
                                "status_value": distance,  # Keeping within expected range
                            }
                            await send_json(websocket, data_dict)
                            if distance <= 30:
                                # ✅ Check for a perfect match
                                if (
                                    data_dict["inclination"],
                                    data_dict["antiversion"],
                                ) == (40, 15):
                                    best_match = {
                                        "x": x,
                                        "y": y,
                                        "z": z,
                                        "rx": rx,
                                        "ry": ry,
                                        "rz": rz,
                                    }
                                    print("🎯 Perfect match found:", best_match)

                                # ✅ If no perfect match, check if within the safe zone
                                elif (
                                    SAFE_CENTER["inclination"] - SAFE_RANGE
                                    <= data_dict["inclination"]
                                    <= SAFE_CENTER["inclination"] + SAFE_RANGE
                                ) and (
                                    SAFE_CENTER["antiversion"] - SAFE_RANGE
                                    <= data_dict["antiversion"]
                                    <= SAFE_CENTER["antiversion"] + SAFE_RANGE
                                ):

                                    best_match = {
                                        "x": x,
                                        "y": y,
                                        "z": z,
                                        "rx": rx,
                                        "ry": ry,
                                        "rz": rz,
                                    }
                                    print("✅ Close to center (Safe Zone):", best_match)

                                else:
                                    best_match = None
                                    print("❌ Out of safe zone, not recording.")

                                # ✅ Store only valid positions
                                if best_match:
                                    existing_data = []

                                    if os.path.exists(FILE_PATH):
                                        with open(FILE_PATH, "r") as file:
                                            try:
                                                existing_data = json.load(file)
                                                if not isinstance(existing_data, list):
                                                    existing_data = []
                                            except json.JSONDecodeError:
                                                existing_data = []

                                    # Append the valid position without overwriting
                                    existing_data.append(best_match)
                                    with open(FILE_PATH, "w") as file:
                                        json.dump(existing_data, file, indent=4)

                                    print(
                                        f"📌 Updated file with new valid position: {best_match}"
                                    )
                    arm.disconnect()
                await asyncio.sleep(0.5)  # ✅ Prevents CPU Overload

    except WebSocketDisconnect:
        print("WebSocket disconnected, stopping hip position tracking.")
    except Exception as e:
        print(f"Unexpected error: {e}")


async def hipCollectRingPointCloudMarkingPipeline(marking, websocket):
    global framecount, variable_dict
    point_reg = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"]

    try:
        pickle_path_TP = os.path.join(
            current_dir, "..", "..", "registration_data", "tp_variable.pickle"
        )
        with open(pickle_path_TP, "rb") as handle:
            TP = pickle.load(handle)
            print("✅ TP variables loaded successfully")
    except Exception as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    try:
        Hip_variable_path = os.path.join(
            current_dir, "..", "..", "registration_data", "hip_variables.pickle"
        )
        with open(Hip_variable_path, "rb") as handle:
            hip_dict = pickle.load(handle)
            print("✅ Hip variables loaded successfully")
    except Exception as e:
        print(f"❌ Error loading Hip variables: {e}")
        return {}

    camera_reference = np.array(
        [
            hip_dict["FO"]["C"],
            hip_dict["SU"]["C"],
            hip_dict["CA"]["C"],
            hip_dict["AT"]["C"],
            hip_dict["PT"]["C"],
        ]
    )
    # for i in camera_reference:
    #     print(i)
    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])

    hip_STL_obj_points = np.array(
        [
            [-45.5239716, -2.5871658, -154.1749573],
            [-63.6134491, 29.2462463, -157.989151],
            [-22.7026062, 2.6151695, -149.2989197],
            [-30.7326965, 8.4291382, -133.5983887],
            [-15.77771, 17.0255318, -159.7479401],
        ]
    )

    # for org in hip_STL_obj_points:
    #     await send_tuple(websocket, org)
    #     await asyncio.sleep(0.1)

    model = LinearRegression(fit_intercept=True)
    model.fit(camera_reference, hip_STL_obj_points)

    # Extract rotation (scaling included) and translation
    R_estimated = model.coef_  # Rotation and scaling
    t_estimated = model.intercept_  # Translation

    # Apply transformation to each point
    for i in camera_reference:
        transformed_point = np.dot(R_estimated, i) + t_estimated
        print(f"point {i}    transformed_point {transformed_point}")
        await asyncio.sleep(0.1)

    try:
        while True:

            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame
            else:
                return
            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)
            if len(detections_right) == 6 and len(detections_left) == 6:
                break

        detections_right.sort(key=lambda x: x[0])
        detections_left.sort(key=lambda x: x[0])

        first_three_right = detections_right[:3]
        first_three_left = detections_left[:3]
        next_three_right = detections_right[-3:]
        next_three_left = detections_left[-3:]

        femur = TriangleTracker(
            tuple(first_three_right),
            tuple(first_three_left),
            rec_img_right,
            rec_img_left,
        )
        femur.find_depth()
        F_points = femur.getLEDcordinates()
        Tracker_plane = [F_points[2], F_points[1], F_points[0]]

        if framecount < 10:
            Pointer_tracker = TriangleTracker(
                tuple(next_three_right),
                tuple(next_three_left),
                rec_img_right,
                rec_img_left,
            )
            Pointer_tracker.find_depth()
            pointer_tip_gcs = Pointer_tracker.getStylusPoint()

            pointRegistered = utils.find_new_point_location(
                old_plane_points=Tracker_plane,
                new_plane_points=TP["TP"]["Plane"],
                old_marked_point=pointer_tip_gcs,
            )

            variable_memory = {"C": pointRegistered, "Plane": TP["TP"]["Plane"]}
            variable_dict[point_reg[framecount]] = variable_memory
            # print(f"🔵 Registered {point_reg[framecount]}: {pointRegistered}")

            point = np.array(pointRegistered, dtype=np.float32)
            point[1] = -point[1]
            # transformed_point = np.dot(transformation_matrix[:3, :3], point) + transformation_matrix[:3, 3]

            transformed_point = np.dot(R_estimated, point) + t_estimated

            print(
                f"🔵 Registered {point_reg[framecount]}: {pointRegistered}   transformed_point {transformed_point}"
            )
            await send_tuple(websocket, transformed_point)
            utils.play_notification_sound()

            framecount += 1
            await asyncio.sleep(0.1)

            if framecount < 10:
                await hipCollectRingPointCloudMarkingPipeline(marking, websocket)
            else:
                print("🎯 All points registered. Saving data...")
                Hip_point_cloud_file = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "registration_data",
                    "RingPointCloud.pickle",
                )
                os.makedirs(os.path.dirname(Hip_point_cloud_file), exist_ok=True)

                with open(Hip_point_cloud_file, "wb") as handle:
                    pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

                print("✅ Pickle dump successful.")

                framecount = 0
                variable_dict = {}
                return
        else:
            print(
                f"⚠️ Detection count mismatch: Right={len(detections_right)}, Left={len(detections_left)}"
            )
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

    finally:
        framecount = 0
        variable_dict = {}
        await send_tuple(websocket, ("exit", 1, 3, 0))
        return


def save_progress(variable_dict, framecount, filepath):
    """Save progress to a pickle file."""
    with open(filepath, "wb") as handle:
        pickle.dump(
            {"variable_dict": variable_dict, "framecount": framecount},
            handle,
            protocol=pickle.HIGHEST_PROTOCOL,
        )


def load_progress(filepath):
    """Load progress from a pickle file."""
    if os.path.exists(filepath):
        with open(filepath, "rb") as handle:
            return pickle.load(handle)
    return {"variable_dict": {}, "framecount": 0}


async def hipFreePointCollection(marking, websocket: WebSocket, rev=None):
    global variable_dict, framecount
    progress_file = os.path.join(
        current_dir, "..", "..", "registration_data", "hip_progress.pickle"
    )
    progress_data = load_progress(progress_file)
    variable_dict = progress_data["variable_dict"]
    framecount = progress_data["framecount"]

    try:
        pickle_path_TP = os.path.join(
            current_dir, "..", "..", "registration_data", "tp_variable.pickle"
        )
        with open(pickle_path_TP, "rb") as handle:
            TP = pickle.load(handle)
            print("✅ TP variables loaded successfully")
    except Exception as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}
    if rev is None:
        try:
            Hip_variable_path = os.path.join(
                current_dir, "..", "..", "registration_data", "hip_variables.pickle"
            )
            with open(Hip_variable_path, "rb") as handle:
                hip_dict = pickle.load(handle)
                print("✅ Hip variables loaded successfully")
        except Exception as e:
            print(f"❌ Error loading Hip variables: {e}")
            return {}

        camera_reference = np.array(
            [
                hip_dict["FO"]["C"],
                hip_dict["SU"]["C"],
                hip_dict["CA"]["C"],
                hip_dict["AT"]["C"],
                hip_dict["PT"]["C"],
            ]
        )
        hip_STL_obj_points = np.array(
            [
                [-45.5239716, -2.5871658, -154.1749573],
                [-63.6134491, 29.2462463, -157.989151],
                [-22.7026062, 2.6151695, -149.2989197],
                [-15.77771, 17.0255318, -159.7479401],
                [-30.7326965, 8.4291382, -133.5983887],
            ]
        )
    else:
        try:
            Hip_variable_path = os.path.join(
                current_dir,
                "..",
                "..",
                "registration_data",
                "revision_hip_variables.pickle",
            )
            with open(Hip_variable_path, "rb") as handle:
                hip_dict = pickle.load(handle)
                print("✅ Hip variables loaded successfully")
        except Exception as e:
            print(f"❌ Error loading Hip variables: {e}")
            return {}

        camera_reference = np.array(
            [
                hip_dict["ASIS"]["C"],
                hip_dict["TF"]["C"],
                hip_dict["PSIS"]["C"],
                hip_dict["IT"]["C"],
                hip_dict["TAL"]["C"],
            ]
        )
        hip_STL_obj_points = np.array(
            [
                [-41.2922211, 433.4049377, 194.4227905],  # ASIS
                [-108.0319901, 358.8612061, 117.9057465],  # TF
                [-47.8687057, 331.510376, 82.7817383],  # PSIS
                [54.1078644, 397.3883972, 98.4199829],  # IT
                [43.462677, 386.5711975, 151.892395],  # TAL
            ]
        )

    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
    # transformation_matrix = calculate_transformation_matrix_with_scaling(camera_reference, hip_STL_obj_points)

    for org in hip_STL_obj_points:
        await send_tuple(websocket, org)
        await asyncio.sleep(0.1)

    model = LinearRegression(fit_intercept=True)
    model.fit(camera_reference, hip_STL_obj_points)

    # Extract rotation (scaling included) and translation
    R_estimated = model.coef_  # Rotation and scaling
    t_estimated = model.intercept_  # Translation

    # Apply transformation to each point
    for i in camera_reference:
        transformed_point = np.dot(R_estimated, i) + t_estimated
        print(f"point {i}    transformed_point {transformed_point}")
        await asyncio.sleep(0.1)

    frame_limit = 250 if rev is None else 500  # Set limit based on rev
    try:
        while framecount < frame_limit:
            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame
            else:
                return

            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)

            if len(detections_right) == 6 and len(detections_left) == 6:
                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])

                first_three_right, next_three_right = (
                    detections_right[:3],
                    detections_right[-3:],
                )
                first_three_left, next_three_left = (
                    detections_left[:3],
                    detections_left[-3:],
                )

                femur = TriangleTracker(
                    tuple(first_three_right),
                    tuple(first_three_left),
                    rec_img_right,
                    rec_img_left,
                )
                femur.find_depth()
                F_points = femur.getLEDcordinates()
                Tracker_plane = [F_points[2], F_points[1], F_points[0]]

                Pointer_tracker = TriangleTracker(
                    tuple(next_three_right),
                    tuple(next_three_left),
                    rec_img_right,
                    rec_img_left,
                )
                Pointer_tracker.find_depth()
                pointer_tip_gcs = Pointer_tracker.getStylusPoint()

                pointRegistered = utils.find_new_point_location(
                    old_plane_points=Tracker_plane,
                    new_plane_points=TP["TP"]["Plane"],
                    old_marked_point=pointer_tip_gcs,
                )
                variable_dict[str(framecount)] = {
                    "C": pointRegistered,
                    "Plane": TP["TP"]["Plane"],
                }

                point = np.array(pointRegistered, dtype=np.float32)
                point[1] = -point[1]
                transformed_point = np.dot(R_estimated, point) + t_estimated
                # transformed_point = np.dot(transformation_matrix[:3, :3], point) + transformation_matrix[:3, 3]
                await send_tuple(websocket, transformed_point)

                framecount += 1
                save_progress(variable_dict, framecount, progress_file)
                await asyncio.sleep(0.1)

        print("🎯 All points registered. Saving data...")
        if rev is None:
            final_file = os.path.join(
                current_dir, "..", "..", "registration_data", "PointCloud.pickle"
            )
        else:
            final_file = os.path.join(
                current_dir, "..", "..", "registration_data", "revPointCloud.pickle"
            )
        await send_tuple(websocket, ("exit", 1, 3, 0))
        os.makedirs(os.path.dirname(final_file), exist_ok=True)
        with open(final_file, "wb") as handle:
            pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
        print("✅ Pickle dump successful.")

        framecount = 0
        variable_dict = {}
        os.remove(progress_file)
        return

    except WebSocketDisconnect:
        print("⚠️ WebSocket disconnected. Saving progress...")
        save_progress(variable_dict, framecount, progress_file)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        save_progress(variable_dict, framecount, progress_file)


async def pointer_femur_pipeline(marking, websocket):
    global framecount, variable_dict
    point_reg = ["APT"]

    latest_frame = await marking.get_latest_frame()
    if latest_frame:
        rec_img_left, rec_img_right = latest_frame
    else:
        return

    detections_right = sorted(
        utils.find_contours_Kmeans(rec_img_right), key=lambda x: x[0]
    )
    detections_left = sorted(
        utils.find_contours_Kmeans(rec_img_left), key=lambda x: x[0]
    )

    if len(detections_right) == 6 and len(detections_left) == 6:
        pointer_tracker = TriangleTracker(
            tuple(detections_right[-3:]),
            tuple(detections_left[-3:]),
            rec_img_right,
            rec_img_left,
        )
        pointer_tracker.find_depth()
        pointer_tip_gcs = pointer_tracker.getStylusPoint()

        femur_tracker = TriangleTracker(
            tuple(detections_right[:3]),
            tuple(detections_left[:3]),
            rec_img_right,
            rec_img_left,
        )
        femur_tracker.find_depth()
        F_points = femur_tracker.getLEDcordinates()

        variable_memory = {
            "C": pointer_tip_gcs,
            "Plane": [F_points[2], F_points[1], F_points[0]],
        }
        variable_dict[point_reg[framecount]] = variable_memory

        pickle_path_TP = os.path.join(
            current_dir, "..", "..", "registration_data", "act_variable.pickle"
        )
        os.makedirs(os.path.dirname(pickle_path_TP), exist_ok=True)

        with open(pickle_path_TP, "wb") as handle:
            pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

        print("Pickle dump successful")
        await send_tuple(websocket, (f"{point_reg[framecount]}", 1, 3, 0))
        utils.play_notification_sound()
    else:
        await pointer_femur_pipeline(marking, websocket)
        print(
            f" recursive call for system-setup.html  {len(detections_right)}  {len(detections_left)}"
        )


async def stream_video(marking, websocket: WebSocket):
    try:
        while True:
            if page != "video":
                return
            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame
            else:
                return

            # Get frames from queue

            if rec_img_left.shape[0] != rec_img_right.shape[0]:
                print("⚠️ Image heights do not match. Resizing right image.")
                rec_img_right = cv2.resize(
                    rec_img_right, (rec_img_left.shape[1], rec_img_left.shape[0])
                )
            _, thresh = cv2.threshold(rec_img_left, 200, 255, cv2.THRESH_BINARY)

            # Find contours
            contours, _ = cv2.findContours(
                thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
            )

            # Extract centroid points
            centroids = []
            for cnt in contours:
                M = cv2.moments(cnt)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    centroids.append([cx, cy])

            centroids = np.array(centroids)
            eps = 92  # Default value if only one object

            # Apply DBSCAN clustering
            if len(centroids) > 0:
                clustering = DBSCAN(eps=eps, min_samples=1).fit(centroids)

                # Get unique cluster labels
                unique_labels = set(clustering.labels_)

                for label in unique_labels:
                    if label == -1:
                        continue  # Ignore noise points

                    # Collect contours belonging to the same group
                    grouped_contours = [
                        contours[i]
                        for i in range(len(clustering.labels_))
                        if clustering.labels_[i] == label
                    ]
                    if len(grouped_contours) == 0:
                        continue  # Avoid empty contour groups

                    # Create bounding box around the group
                    x, y, w, h = cv2.boundingRect(np.vstack(grouped_contours))
                    cv2.rectangle(rec_img_left, (x, y), (x + w, y + h), (0, 255, 0), 2)

                    # Alternative: Draw convex hull
                    hull = cv2.convexHull(np.vstack(grouped_contours))
                    cv2.polylines(
                        rec_img_left,
                        [hull],
                        isClosed=True,
                        color=(255, 0, 0),
                        thickness=2,
                    )

                    # -------- Step to Add Centroid Calculation and Draw a Circle --------
                    # Compute group centroid
                    all_points = np.vstack(grouped_contours)
                    centroid_x = int(np.mean(all_points[:, 0, 0]))  # X-coordinate
                    centroid_y = int(np.mean(all_points[:, 0, 1]))  # Y-coordinate

                    # Draw a filled circle at the centroid
                    # cv2.circle(rec_img_left, (centroid_x, centroid_y), 100, (255, 0, 0), -1)  # Red filled circle
                    triangle_pts = np.array(
                        [
                            [
                                centroid_x,
                                centroid_y + 50,
                            ],  # Bottom vertex (was top before)
                            [centroid_x - 50, centroid_y - 50],  # Top left
                            [centroid_x + 50, centroid_y - 50],  # Top right
                        ],
                        np.int32,
                    )

                    triangle_pts = triangle_pts.reshape(
                        (-1, 1, 2)
                    )  # Reshape for OpenCV

                    # Draw the solid triangle
                    cv2.fillPoly(
                        rec_img_left, [triangle_pts], (255, 255, 0)
                    )  # Red triangle

            # Encode the combined frame
            _, buffer = cv2.imencode(
                ".jpg", rec_img_left, [cv2.IMWRITE_JPEG_QUALITY, 70]
            )
            jpg_as_text = base64.b64encode(buffer).decode()

            # Send frame over WebSocket
            await websocket.send_text(jpg_as_text)

            # Control frame rate
            await asyncio.sleep(0.03)

    except WebSocketDisconnect:
        print("🔌 WebSocket disconnected, stopping video stream.")

    except Exception as e:
        print(f"⚠️ Error in stream_video: {e}")


async def hip_tp_variable_Marking(marking, websocket, point=None):
    global framecount, variable_dict

    point_reg = ["TP2"] if point else ["TP"]

    while True:
        latest_frame = await marking.get_latest_frame()
        if latest_frame:
            rec_img_left, rec_img_right = latest_frame
        else:
            return
        detections_right = utils.find_contours_Kmeans(rec_img_right)
        detections_left = utils.find_contours_Kmeans(rec_img_left)

        if len(detections_right) == 6 and len(detections_left) == 6:
            break  # Exit the loop once the correct detections are found

        print("⏳ Waiting for exactly 6 detections on both sides...")
        await asyncio.sleep(0.5)  # Avoid excessive CPU usage

    framecount = 0
    detections_right = sorted(detections_right, key=lambda x: x[0])
    detections_left = sorted(detections_left, key=lambda x: x[0])

    # Extract first 3 and last 3 coordinates
    first_three_right = detections_right[:3]
    first_three_left = detections_left[:3]
    next_three_right = detections_right[-3:]
    next_three_left = detections_left[-3:]

    Pointer_traker = TriangleTracker(
        tuple(next_three_right), tuple(next_three_left), rec_img_right, rec_img_left
    )
    Pointer_traker.find_depth()
    pointer_tip_gcs = Pointer_traker.getStylusPoint()

    femure = TriangleTracker(
        tuple(first_three_right), tuple(first_three_left), rec_img_right, rec_img_left
    )
    femure.find_depth()
    F_points = femure.getLEDcordinates()

    Tracker_plane = [F_points[2], F_points[1], F_points[0]]

    pickle_path_TP = os.path.join(
        current_dir,
        "..",
        "..",
        "registration_data",
        "tp2_variable.pickle" if point else "tp_variable.pickle",
    )
    os.makedirs(os.path.dirname(pickle_path_TP), exist_ok=True)

    variable_memory = {"C": pointer_tip_gcs, "Plane": Tracker_plane}
    variable_dict[point_reg[framecount]] = variable_memory

    with open(pickle_path_TP, "wb") as handle:
        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

    print(f"✅ Pickle dump successful {point_reg[framecount]}")

    await send_tuple(websocket, (f"{point_reg[framecount]}", 1, 3, 0))
    utils.play_notification_sound()


@app.get("/knee/multiple-page2.html")
async def multiple_page2(request: Request):
    return templates.TemplateResponse("/knee/multiple-page2.html", {"request": request})


@app.get("/knee/multiple-page.html")
async def multiple_page(request: Request):
    return templates.TemplateResponse("/knee/multiple-page.html", {"request": request})


@app.get("/knee/tkr-screen-2.html")
async def tkr_screen_2(request: Request):
    return templates.TemplateResponse("/knee/tkr-screen-2.html", {"request": request})


@app.get("/knee/inner-page6.html")
async def inner_page6(request: Request):
    return templates.TemplateResponse("/knee/inner-page6.html", {"request": request})


@app.get("/knee/tkr-screen-3.html")
async def tkr_screen_3(request: Request):
    return templates.TemplateResponse("/knee/tkr-screen-3.html", {"request": request})


@app.get("/knee/robot-introduction.html")
async def robot_introduction(request: Request):
    return templates.TemplateResponse(
        "/knee/robot-introduction.html", {"request": request}
    )


@app.get("/knee/robot_position.html")
async def robot_workspace(request: Request):
    return templates.TemplateResponse("/knee/robot_position.html", {"request": request})


@app.get("/knee/robot_workspace.html")
async def robot_workspace_position(request: Request):
    return templates.TemplateResponse(
        "/knee/robot_workspace.html", {"request": request}
    )


@app.get("/knee/inner-page.html")
async def inner_page(request: Request):
    return templates.TemplateResponse("/knee/inner-page.html", {"request": request})


@app.get("/knee/femur-distal-femur-cut.html")
async def knee_distal_femur_cut(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-distal-femur-cut.html", {"request": request}
    )


@app.get("/knee/femur-inner-page5.html")
async def femur_inner_page5(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-inner-page5.html", {"request": request}
    )


@app.get("/knee/femur-anterior-resection.html")
async def femur_anterior_resection(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-anterior-resection.html", {"request": request}
    )


@app.get("/knee/femur-anterior-chamfer-resection.html")
async def femur_anterior_chamfer_resection(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-anterior-chamfer-resection.html", {"request": request}
    )


@app.get("/knee/femur-posterior-resection.html")
async def femur_posterior_resection(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-posterior-resection.html", {"request": request}
    )


@app.get("/knee/femur-posterior-chamfer-resection.html")
async def femur_posterior_chamfer_resection(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-posterior-chamfer-resection.html", {"request": request}
    )


@app.get("/knee/femur-inner-page2.html")
async def femur_inner_page2(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-inner-page2.html", {"request": request}
    )


@app.get("/knee/tibia-inner-page3.html")
async def tibia_inner_page3(request: Request):
    return templates.TemplateResponse(
        "/knee/tibia-inner-page3.html", {"request": request}
    )


@app.get("/knee/femur-graph-screen.html")
async def femur_graph_screen(request: Request):
    return templates.TemplateResponse(
        "/knee/femur-graph-screen.html", {"request": request}
    )


@app.get("/knee/Robot_intoduction.html")
async def femur_graph_screen(request: Request):
    return templates.TemplateResponse(
        "/knee/Robot_intoduction.html", {"request": request}
    )


#############################################################################
#######                             Uni Knee                             ####
#############################################################################


@app.get("/uni-knee/multiple-page2.html")
async def uni_multiple_page2(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/multiple-page2.html", {"request": request}
    )


@app.get("/uni-knee/multiple-page.html")
async def uni_multiple_page(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/multiple-page.html", {"request": request}
    )


@app.get("/uni-knee/tkr-screen-2.html")
async def uni_tkr_screen_2(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/tkr-screen-2.html", {"request": request}
    )


@app.get("/uni-knee/inner-page6.html")
async def uni_inner_page6(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/inner-page6.html", {"request": request}
    )


@app.get("/uni-knee/Free_point_collectionTibia.html")
async def point_collectionTibia(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/Free_point_collectionTibia.html", {"request": request}
    )


@app.get("/uni-knee/Free_point_collection_femur.html")
async def point_collection_femur(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/Free_point_collection_femur.html", {"request": request}
    )

@app.get("/uni-knee/accp_femure_cut.html")
async def revisionHipmidAxisPlane(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/accp_femure_cut.html", {"request": request}
    )

@app.get("/uni-knee/femur-distal-femur-cut.html")
async def uni_distal_femur_cut(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/femur-distal-femur-cut.html", {"request": request}
    )

@app.get("/uni-knee/tibia-cut.html")
async def uni_tibia_cut(request: Request):
    return templates.TemplateResponse("/uni-knee/tibia-cut.html", {"request": request})


@app.get("/uni-knee/Final_leg_Alignment.html")
async def unifinal_leg_alignment(request: Request):
    return templates.TemplateResponse(
        "/uni-knee/Final_leg_Alignment.html", {"request": request}
    )


#############################################################################
#######                            Revision Knee                         ####
#############################################################################
@app.get("/revision-knee/multiple-page2.html")
async def rev_multiple_page2(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/multiple-page2.html", {"request": request}
    )


@app.get("/revision-knee/multiple-page.html")
async def rev_multiple_page(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/multiple-page.html", {"request": request}
    )


@app.get("/revision-knee/tkr-screen-2.html")
async def rev_tkr_screen_2(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/tkr-screen-2.html", {"request": request}
    )


@app.get("/revision-knee/inner-page6.html")
async def rev_inner_page6(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/inner-page6.html", {"request": request}
    )


@app.get("/revision-knee/remove-primary-implants.html")
async def rev_remove_primary_implants(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/remove-primary-implants.html", {"request": request}
    )


@app.get("/revision-knee/ml-size-acquisition.html")
async def rev_ml_size_acquisition(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/ml-size-acquisition.html", {"request": request}
    )


@app.get("/revision-knee/Free_point_collectionTibia.html")
async def rev_free_point_collection_tibia(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/Free_point_collectionTibia.html", {"request": request}
    )


@app.get("/revision-knee/Free_point_collection_femur.html")
async def rev_free_point_collection_tibia(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/Free_point_collection_femur.html", {"request": request}
    )


@app.get("/revision-knee/Aori_type.html")
async def rev_aori_type(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/Aori_type.html", {"request": request}
    )


@app.get("/revision-knee/tibia-im-canal-ream.html")
async def rev_tibia_im_canal_ream(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/tibia-im-canal-ream.html", {"request": request}
    )


@app.get("/revision-knee/conflict-message.html")
async def rev_conflict_message(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/conflict-message.html", {"request": request}
    )
@app.get("/revision-knee/conflict-messageFemure.html")
async def rev_conflict_messageF(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/conflict-messageFemure.html", {"request": request}
    )


@app.get("/revision-knee/planning-proximal-tibia-cut.html")
async def rev_planning_proximal_tibia_cut(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/planning-proximal-tibia-cut.html", {"request": request}
    )


@app.get("/revision-knee/guidance-proximal-tibia-cut.html")
async def rev_guidance_proximal_tibia_cut(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/guidance-proximal-tibia-cut.html", {"request": request}
    )


@app.get("/revision-knee/guidance-distal-femur-cutBackup.html")
async def rev_guidance_proximal_tibia_cut(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/guidance-distal-femur-cutBackup.html", {"request": request}
    )


@app.get("/revision-knee/revision-knee-burr.html")
async def rev_revision_knee_burr(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/revision-knee-burr.html", {"request": request}
    )


@app.get("/revision-knee/verification-proximal-tibia-cut.html")
async def rev_verification_proximal_tibia_cut(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/verification-proximal-tibia-cut.html", {"request": request}
    )


@app.get("/revision-knee/femur-im-canal-ream.html")
async def rev_femur_im_canal_ream(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/femur-im-canal-ream.html", {"request": request}
    )


@app.get("/revision-knee/planning-distal-femur-cut.html")
async def rev_planning_distal_femur_cut(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/planning-distal-femur-cut.html", {"request": request}
    )


@app.get("/revision-knee/guidance-distal-femur-cut.html")
async def rev_guidance_distal_femur_cut(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/guidance-distal-femur-cut.html", {"request": request}
    )


@app.get("/revision-knee/revision-knee-burrFemur.html")
async def rev_revision_knee_burr_femur(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/revision-knee-burrFemur.html", {"request": request}
    )


@app.get("/revision-knee/verification-distal-femur-cut.html")
async def rev_verification_distal_femur_cut(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/verification-distal-femur-cut.html", {"request": request}
    )


@app.get("/revision-knee/balance-in-extension.html")
async def rev_balance_in_extension(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/balance-in-extension.html", {"request": request}
    )


@app.get("/revision-knee/balance-in-flexion.html")
async def rev_balance_in_flexion(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/balance-in-flexion.html", {"request": request}
    )


@app.get("/revision-knee/graph-screen.html")
async def rev_graph_screen(request: Request):
    return templates.TemplateResponse(
        "/revision-knee/graph-screen.html", {"request": request}
    )


class Timer:
    """Timer class to track elapsed time."""

    def __init__(self, duration):
        self.start_time = None
        self.duration = duration  # Total duration in seconds

    def start(self):
        """Start the timer."""
        self.start_time = time.time()

    def elapsed(self):
        """Check elapsed time."""
        return time.time() - self.start_time if self.start_time else 0

    def is_time_up(self):
        """Check if time has exceeded the set duration."""
        return self.elapsed() >= self.duration


async def pivoting_procedure(marking, websocket):
    global framecount
    femur_kneeMarkinglist = []
    point_reg = (
        ["HIF"] + [f"HC{i}" for i in range(1, 21)] + ["HC"]
    )  # 21 registration points (excluding HIF)
    first = False
    timer = Timer(30)  # 30-second timer

    # **Independent Timers**
    collection_interval = 30 / 100  # ≈ 0.3 sec per collected point (100 points)
    registration_interval = 30 / len(
        point_reg
    )  # ≈ 1.43 sec per registration point (21 points)

    next_collection_time = None
    next_registration_time = None

    framecount = 0  # Tracks collected points
    reg_count = 0  # Tracks sent registration points

    while framecount < 50 and not timer.is_time_up():
        while True:
            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame
            else:
                return

            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)
            if len(detections_right) == 6 and len(detections_left) == 6:
                await asyncio.sleep(0.01)  # Prevent rapid resending
                break
            else:
                await asyncio.sleep(0.02)  # Prevent rapid resending
                print(f"detections {len(detections_right)}  {len(detections_left)}  {page}")
            if page not in [
                "unimultiple-page2.html",
                "revmultiple-page2.html",
                "multiple-page2.html",
            ]:

                print(f'Exiting.. pivoting procedure')
                return
        detections_right = sorted(detections_right, key=lambda detection: detection[0])
        detections_left = sorted(detections_left, key=lambda detection: detection[0])
        # femure = TriangleTracker(tuple(detections_right[:3]), tuple(detections_left[:3]),
        #                          rec_img_right, rec_img_left)
        femure = TriangleTracker(
            detections_right[:3], detections_left[:3], rec_img_right, rec_img_left,  mode='F'
        )
        femure.find_depth()
        F_points = femure.getLEDcordinates()

        centroid = utils.calculate_centroid(F_points)
        point_A = (F_points[1] + F_points[0]) / 2.0
        vector = F_points[2] - point_A
        angle = math.degrees(math.atan2(vector[1], vector[0]))

        if framecount == 0 and not first:
            if angle <= 88:
                print(
                    f"Hold the LEG in a single position at most 90° | Current angle: {angle}"
                )

                centroid = utils.calculate_centroid(F_points)
                femur_kneeMarkinglist.append(centroid)

                await asyncio.sleep(0.1)  # Prevent rapid resending
                continue
            try:
                await send_tuple(websocket, (f"{point_reg[reg_count]}", 0.2, 0.2, 0.2))
                utils.play_notification_sound()
            except Exception as e:
                print(f"⚠️ WebSocket send error: {e}")
            first = True
            timer.start()  # Start the timer
            next_collection_time = timer.elapsed() + collection_interval
            next_registration_time = timer.elapsed() + registration_interval
            print("✅ Timer started. Collecting points...")

        if first:
            elapsed_time = timer.elapsed()

            # **Collect 100 points (every ~0.3 sec)**
            if elapsed_time >= next_collection_time and framecount < 50:
                femur_kneeMarkinglist.append(centroid)
                framecount += 1
                print(f"🟢 Collected point {framecount} at {elapsed_time:.2f}s")
                next_collection_time += collection_interval

            # **Send 21 registration points (every ~1.43 sec)**
            if elapsed_time >= next_registration_time and reg_count < len(point_reg):
                try:
                    await send_tuple(
                        websocket, (f"{point_reg[reg_count]}", 0.2, 0.2, 0.2)
                    )
                    print(
                        f"📌 Sent registration point {point_reg[reg_count]} at {elapsed_time:.2f}s"
                    )
                    reg_count += 1
                    next_registration_time += registration_interval
                except Exception as e:
                    print(f"⚠️ WebSocket send error: {e}")
                await asyncio.sleep(0.1)  # Prevent rapid resending

        if framecount >= 50 or timer.is_time_up():
            print(
                f"⏹️ Collection completed: {framecount} points in {timer.elapsed():.2f} seconds"
            )
            break

    try:
        await send_tuple(websocket, ("HC", 0.2, 0.2, 0.2))
    except Exception as e:
        print(f"⚠️ Final WebSocket send error: {e}")

    points_all2 = np.array(femur_kneeMarkinglist, dtype=float)
    try:

        def sphere_residuals(p, x, y, z):
            x_c, y_c, z_c, r = p
            return np.sqrt((x - x_c) ** 2 + (y - y_c) ** 2 + (z - z_c) ** 2) - r

        x, y, z = points_all2[:, 0], points_all2[:, 1], points_all2[:, 2]
        p0 = [0, 0, 0, 1]
        result = least_squares(sphere_residuals, p0, args=(x, y, z))
        sphere_center = result.x[:3]
    except Exception as e:
        print(f"ERROR: {e}")
        return

    # Save sphere center
    pickle_path = os.path.join(
        os.path.dirname(__file__), "..", "..", "registration_data", "hip_center.pickle"
    )
    os.makedirs(os.path.dirname(pickle_path), exist_ok=True)
    with open(pickle_path, "wb") as handle:
        pickle.dump(sphere_center, handle, protocol=pickle.HIGHEST_PROTOCOL)

    print(f"🎯 Sphere center: {sphere_center}")
    utils.play_notification_sound()
    return 0


async def Tebia_marking(marking, websocket):

    framecount = 1
    variable_dict = {}
    point_reg = ["TC", "TAP", "MC", "LC", "MM", "LM"]

    try:
        # Common base directory for both files
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")

        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)

        # Tibia TC variables
        pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
        with open(pickle_path_tibia_tc, "rb") as tibia_handle:
            tibia_tc = pickle.load(tibia_handle)
        print("✅ Femur variables loaded successfully")
    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading femur variables: {e}")
        return {}

    while framecount < len(point_reg):
        try:

            while True:

                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    img_left, img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(img_right)
                detections_left = utils.find_contours_Kmeans(img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    break  # Exit loop when both lists have exactly 9 detections
                print(f"{len(detections_right)}   {len(detections_left)}")
                await asyncio.sleep(0.1)  # Wait and retry if condition is not met
            if page not in ["multiple-page.html", "revmultiple-page.html"]:
                return
            detections_right.sort(key=lambda x: x[0])
            detections_left.sort(key=lambda x: x[0])
            # Step 2: Extract first three elements
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            # Remove extracted elements
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]
            if (
                framecount < 4
                and len(detections_right) == 6
                and len(detections_left) == 6
            ):
                # Step 3: Sort remaining by Y-coordinate (descending)
                detections_right.sort(key=lambda x: x[1], reverse=True)
                detections_left.sort(key=lambda x: x[1], reverse=True)

                # Step 4: Extract next three elements
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]

                # Remove extracted elements to ensure mutual exclusivity
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]

                # Step 5: Extract last three elements safely
                last_three_right = detections_right[-3:]
                last_three_left = detections_left[-3:]

                Pointer_traker = TriangleTracker(
                    next_three_right, next_three_left, img_right, img_left
                )
                Pointer_traker.find_depth()
                if framecount == 1:
                    p_points = Pointer_traker.getLEDcordinates()
                pointer_tip_gcs = Pointer_traker.getStylusPoint()
                # print(f'last_three_right {last_three_right}')
                # print(f'last_three_left {last_three_left}')
                femure = TriangleTracker(
                    first_three_right, first_three_left, img_right, img_left, mode="F"
                )
                femure.find_depth()
                F_points = femure.getLEDcordinates()
                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]
                print(f"femur_plane  {femur_plane}")
                tebia = TriangleTracker(
                    last_three_right, last_three_left, img_right, img_left, mode="T"
                )
                tebia.find_depth()
                T_Points = tebia.getLEDcordinates()
                if T_Points[1][1] >= T_Points[0][1]:
                    tibia_plane = [T_Points[2], T_Points[0], T_Points[1]]
                else:
                    tibia_plane = [T_Points[2], T_Points[1], T_Points[0]]
                print(f"tibia_plane  {tibia_plane}")
                NewFC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )
                tibia_plane[2] = NewFC
                NewTC = utils.find_new_point_location(
                    tibia_tc["TC"]["Plane"], tibia_plane, tibia_tc["TC"]["C"]
                )
                tibia_plane[2] = NewTC

                print(f"tibia_plane2  {tibia_plane}")
                pointRegistered = utils.find_new_point_location(
                    tibia_plane, tibia_tc["TC"]["Plane"], pointer_tip_gcs
                )
                # tibia_plane[2] = TC
                if framecount == 1:
                    variable_memory = {
                        "C": (pointRegistered, p_points[2]),
                        "Plane": tibia_tc["TC"]["Plane"],
                    }
                else:
                    variable_memory = {
                        "C": pointRegistered,
                        "Plane": tibia_tc["TC"]["Plane"],
                    }

                variable_dict[point_reg[framecount]] = variable_memory
                p_points = Pointer_traker.getLEDcordinates()
                try:
                    await send_tuple(
                        websocket, (f"{point_reg[framecount]}", 0.2, 0.2, 0.2)
                    )
                    framecount = framecount + 1
                    utils.play_notification_sound()
                except Exception as e:
                    print(f" Error {e}")
            elif (
                framecount >= 4
                and len(detections_right) == 6
                and len(detections_left) == 6
            ):
                last_three_right = detections_right[-3:]
                last_three_left = detections_left[-3:]

                middle_three_right = detections_right[:3]
                middle_three_left = detections_left[:3]
                # Initialize the tracker with sorted points
                Pointer_traker = TriangleTracker(
                    detections_right=last_three_right,
                    detections_left=last_three_left,
                    frame_right=img_right,
                    frame_left=img_left,
                )
                Pointer_traker.find_depth()
                pointer_tip_gcs = Pointer_traker.getStylusPoint()
                femure = TriangleTracker(
                    detections_right=first_three_right,
                    detections_left=first_three_left,
                    frame_right=img_right,
                    frame_left=img_left,
                    mode="F",
                )
                femure.find_depth()
                F_points = femure.getLEDcordinates()
                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]

                tebia = TriangleTracker(
                    middle_three_right, middle_three_left, img_right, img_left, mode="T"
                )
                tebia.find_depth()
                T_Points = tebia.getLEDcordinates()
                if T_Points[1][1] >= T_Points[0][1]:
                    tibia_plane = [T_Points[2], T_Points[0], T_Points[1]]
                else:
                    tibia_plane = [T_Points[2], T_Points[1], T_Points[0]]
                print(
                    f"Point registered {point_reg[framecount]} tibia_plane  {tibia_plane}"
                )
                NewFC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )
                tibia_plane[2] = NewFC
                NewTC = utils.find_new_point_location(
                    tibia_tc["TC"]["Plane"], tibia_plane, tibia_tc["TC"]["C"]
                )
                tibia_plane[2] = NewTC

                pointRegistered = utils.find_new_point_location(
                    tibia_plane, tibia_tc["TC"]["Plane"], pointer_tip_gcs
                )
                variable_memory = {
                    "C": pointRegistered,
                    "Plane": tibia_tc["TC"]["Plane"],
                }

                variable_dict[point_reg[framecount]] = variable_memory

                try:
                    await send_tuple(
                        websocket, (f"{point_reg[framecount]}", 0.2, 0.2, 0.2)
                    )
                    print(
                        f"Point registered {point_reg[framecount]}  pointRegistered{pointRegistered}  pointer_tip_gcs{pointer_tip_gcs}"
                    )
                    utils.play_notification_sound()
                    framecount = framecount + 1
                except Exception as e:
                    print(f" Error {e}")

            else:
                print("no data")

            if framecount == len(point_reg):
                print("All points registered.")

                pickle_path_tebia = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "registration_data",
                    "tibia_variables.pickle",
                )
                os.makedirs(os.path.dirname(pickle_path_tebia), exist_ok=True)
                print(f" variable_dict {variable_dict}")
                with open(pickle_path_tebia, "wb") as handle:
                    pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

                print("Pickle dump successful")
                ANC = (
                    np.array(variable_dict["MM"]["C"])
                    + np.array(variable_dict["LM"]["C"])
                ) / 2

                direction_vector = np.array(ANC) - np.array(tibia_tc["TC"]["C"])

                # Define the normal vector for the plane
                normal_vector = direction_vector / np.linalg.norm(direction_vector)

                # Define the plane equation coefficients based on the point NewTC
                A, B, C = normal_vector
                D = -np.dot(normal_vector, np.array(tibia_tc["TC"]["C"]))

                # Calculate the distance from NewMC to the plane
                def distance_from_point_to_plane(point, A, B, C, D):
                    point = np.array(point)
                    return abs(
                        A * point[0] + B * point[1] + C * point[2] + D
                    ) / np.sqrt(A**2 + B**2 + C**2)

                distance_to_NewMC = distance_from_point_to_plane(
                    variable_dict["MC"]["C"], A, B, C, D
                )
                distance_to_NewLC = distance_from_point_to_plane(
                    variable_dict["LC"]["C"], A, B, C, D
                )

                if distance_to_NewMC > distance_to_NewLC:
                    Tebia_BoneCut_Point = "LC"
                else:
                    Tebia_BoneCut_Point = "MC"
                Tebia_BoneCut_Point_path = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "registration_data",
                    "tibia_cuttingPoint.txt",
                )
                with open(Tebia_BoneCut_Point_path, "wb") as handle:
                    pickle.dump(
                        Tebia_BoneCut_Point, handle, protocol=pickle.HIGHEST_PROTOCOL
                    )
                break
        except Exception as e:
            print(f"Error {e}")


async def UniTebia_marking(marking, websocket):
    framecount = 3
    variable_dict = {}
    point_reg = ["TC", "AL1", "AL2", "MC", "MP", "MM", "LM"]

    try:
        # Common base directory for both files
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")

        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)

        # Tibia TC variables
        pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
        with open(pickle_path_tibia_tc, "rb") as tibia_handle:
            tibia_tc = pickle.load(tibia_handle)
        print("✅ Femur variables loaded successfully")
    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading femur variables: {e}")
        return {}

    while framecount < len(point_reg):
        if page != "unimultiple-page.html":
            return
        try:
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    img_left, img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(img_right)
                detections_left = utils.find_contours_Kmeans(img_left)
                # Wait until at least 9 detections are available

                if len(detections_right) == 9 and len(detections_left) == 9:
                    await asyncio.sleep(0.1)
                    break
                if page != "unimultiple-page.html":
                    return

            # Step 1: Sort by X-coordinate
            detections_right.sort(key=lambda x: x[0])
            detections_left.sort(key=lambda x: x[0])
            # Step 2: Extract first three elements
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            # Remove extracted elements
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            if point_reg[framecount] == "MC" or point_reg[framecount] == "MP":
                detections_right.sort(key=lambda x: x[1], reverse=True)
                detections_left.sort(key=lambda x: x[1], reverse=True)

                # Step 4: Extract next three elements
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]

                # Remove extracted elements to ensure mutual exclusivity
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]

                # Step 5: Extract last three elements safely
                last_three_right = detections_right[-3:]
                last_three_left = detections_left[-3:]

                Pointer_traker = TriangleTracker(
                    next_three_right, next_three_left, img_right, img_left
                )
                Pointer_traker.find_depth()
                pointer_tip_gcs = Pointer_traker.getStylusPoint()

                femure = TriangleTracker(
                    first_three_right, first_three_left, img_right, img_left, mode='F'
                )
                femure.find_depth()
                F_points = femure.getLEDcordinates()

                tebia = TriangleTracker(
                    last_three_right, last_three_left, img_right, img_left, mode='T'
                )
                tebia.find_depth()
                T_Points = tebia.getLEDcordinates()
                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]
                NewFC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )

                if T_Points[1][1] >= T_Points[0][1]:
                    tibia_plane = [T_Points[2], T_Points[0], NewFC]
                else:
                    tibia_plane = [T_Points[2], T_Points[1], NewFC]
                NewTC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )
                tibia_plane[2] = NewTC
                pointRegistered = utils.find_new_point_location(
                    tibia_plane, tibia_tc["TC"]["Plane"], pointer_tip_gcs
                )
                variable_memory = {
                    "C": pointRegistered,
                    "Plane": tibia_tc["TC"]["Plane"],
                }

                variable_dict[point_reg[framecount]] = variable_memory
                try:
                    await send_tuple(
                        websocket, (f"{point_reg[framecount]}", 0.2, 0.2, 0.2)
                    )
                    print(f"Point registered {point_reg[framecount]}")
                    utils.play_notification_sound()
                except Exception as e:
                    print(f" Error {e}")
                framecount = framecount + 1
            elif ((point_reg[framecount] == "MM" or point_reg[framecount] == "LM") and len(detections_right) == 6
                and len(detections_left) == 6) :
                last_three_right = detections_right[-3:]
                last_three_left = detections_left[-3:]

                middle_three_right = detections_right[:3]
                middle_three_left = detections_left[:3]
                Pointer_traker = TriangleTracker(
                    last_three_right, last_three_left, img_right, img_left
                )
                Pointer_traker.find_depth()
                pointer_tip_gcs = Pointer_traker.getStylusPoint()
                femure = TriangleTracker(
                    first_three_right, first_three_left, img_right, img_left, mode='F'
                )
                femure.find_depth()
                F_points = femure.getLEDcordinates()
                tebia = TriangleTracker(
                    middle_three_right, middle_three_left, img_right, img_left, mode='T'
                )
                tebia.find_depth()
                T_Points = tebia.getLEDcordinates()
                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]
                NewFC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )

                if T_Points[1][1] >= T_Points[0][1]:
                    tibia_plane = [T_Points[2], T_Points[0], NewFC]
                else:
                    tibia_plane = [T_Points[2], T_Points[1], NewFC]
                NewTC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )
                tibia_plane[2] = NewTC
                pointRegistered = utils.find_new_point_location(
                    tibia_plane, tibia_tc["TC"]["Plane"], pointer_tip_gcs
                )
                variable_memory = {
                    "C": pointRegistered,
                    "Plane": tibia_tc["TC"]["Plane"],
                }

                variable_dict[point_reg[framecount]] = variable_memory
                try:
                    await send_tuple(
                        websocket, (f"{point_reg[framecount]}", 0.2, 0.2, 0.2)
                    )
                    print(
                        f"Point registered {point_reg[framecount]}  pointRegistered{pointRegistered}  pointer_tip_gcs{pointer_tip_gcs} tibia_plane{tibia_plane}"
                    )
                    utils.play_notification_sound()
                except Exception as e:
                    print(f" Error {e}")
                framecount = framecount + 1
                # else:
                #     print('no data')

            if framecount == len(point_reg):
                print("All points registered.")

                pickle_path_tebia = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "registration_data",
                    "tibia_variables.pickle",
                )
                os.makedirs(os.path.dirname(pickle_path_tebia), exist_ok=True)
                print(f" variable_dict {variable_dict}")
                with open(pickle_path_tebia, "wb") as handle:
                    pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

                print("Pickle dump successful")
                break
        except Exception as e:
            print(f"Error {e}")


async def uniFree_point_collectionTibia(marking, websocket):
    framecount = 0
    variable_dict = {}

    # Base directory
    base_dir = os.path.join(current_dir, "..", "..", "registration_data")

    files_to_load = {
        # "hip_center": os.path.join(base_dir, 'hip_center.pickle'),
        "femur_variables": os.path.join(base_dir, "femur_variables.pickle"),
        "tibia_variables": os.path.join(base_dir, "tibia_variables.pickle"),
        "tibia_variables_TC": os.path.join(base_dir, "tibia_variables_TC.pickle"),
    }

    femur_dict = load_pickle_file(
        files_to_load["femur_variables"], "femur variables variables"
    )
    tibia_dict_tc = load_pickle_file(
        files_to_load["tibia_variables_TC"], "Tibia TC variables"
    )
    tibia_dict = load_pickle_file(files_to_load["tibia_variables"], "Tibia variables")

    stFrameInfo = MV_FRAME_OUT_INFO_EX()
    memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
    # print(f'tibia_dict_tc {tibia_dict_tc}')
    camera_reference = np.array(
        [
            tibia_dict_tc["TC"]["C"],
            tibia_dict_tc["AL1"]["C"],
            tibia_dict_tc["AL2"]["C"],
            tibia_dict["MC"]["C"],
            tibia_dict["MP"]["C"],
            tibia_dict["MM"]["C"],
            tibia_dict["LM"]["C"],
        ]
    )
    print(f'camera_reference {camera_reference}')
    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
    obj_points_uniTibia = np.array(
        [
            [
                -64.6756134,
                -2.9603553,
                -17.7387486,
            ],  # TC [-60.84293912 -17.69269143   2.21923788]
            [-64.6940308, 0.605574, -17.0766163],  # AL1
            [-65.52034, -5.8293805, -17.9646893],  # AL2
            [-64.2572708, -4.0632067, -14.2518415],  # MC
            [-63.9681778, -5.3495793, -12.3823586],  # MP
            [-0.7417173, -8.5226688, -13.8344755],  # MM
            [-0.2158346, -9.4181175, -22.6858425],  # LM
        ]
    )
    # obj_points_uniTibia[:, [1, 2]] = obj_points_uniTibia[:, [2, 1]]
    for i in obj_points_uniTibia:
        await send_tuple(websocket, i)
    transformation_matrix = calculate_transformation_matrix_with_scaling(
        camera_reference, obj_points_uniTibia
    )

    points_cloud = []
    while framecount < 250:
        if page != "uniFree_point_collectionTibia.html":
            return
        try:
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                # Wait until at least 9 detections are available

                if len(detections_right) == 9 and len(detections_left) == 9:
                    await asyncio.sleep(0.1)
                    break
                else:
                    try:
                        # await send_tuple(websocket, ('null', 0, 0, 0))
                        await websocket.send_text("null")
                    except Exception as e:
                        print(f"⚠️ WebSocket send failed: {e}")
                    if page != "uniFree_point_collectionTibia.html":
                        return

            detections_right.sort(key=lambda x: x[0])
            detections_left.sort(key=lambda x: x[0])
            # Step 2: Extract first three elements
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            # Remove extracted elements
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right.sort(key=lambda x: x[1], reverse=True)
            detections_left.sort(key=lambda x: x[1], reverse=True)

            # Step 4: Extract next three elements
            next_three_right = detections_right[:3]
            next_three_left = detections_left[:3]

            # Remove extracted elements to ensure mutual exclusivity
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            # Step 5: Extract last three elements safely
            last_three_right = detections_right[-3:]
            last_three_left = detections_left[-3:]

            femure = TriangleTracker(
                first_three_right, first_three_left, rec_img_right, rec_img_left
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()
            print(f'femur_plane  {F_points}')
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]
            Pointer_traker = TriangleTracker(
                next_three_right, next_three_left, rec_img_right, rec_img_left
            )
            Pointer_traker.find_depth()
            pointer_tip_gcs = Pointer_traker.getStylusPoint()

            tibia = TriangleTracker(
                last_three_right, last_three_left, rec_img_right, rec_img_left
            )
            tibia.find_depth()
            T_points = tibia.getLEDcordinates()
            print(f'T_points  {T_points}')
            NewFC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            if T_points[1][1] >= T_points[0][1]:
                tibia_plane = [T_points[2], T_points[0], NewFC]
            else:
                tibia_plane = [T_points[2], T_points[1], NewFC]
            TC = utils.find_new_point_location(
                tibia_dict_tc["TC"]["Plane"], tibia_plane, tibia_dict_tc["TC"]["C"]
            )
            tibia_plane[2] = TC
            pointRegistered = utils.find_new_point_location(
                old_plane_points=tibia_plane,
                new_plane_points=tibia_dict_tc["TC"]["Plane"],
                old_marked_point=pointer_tip_gcs,
            )
            points_cloud.append(pointRegistered)

            data = f"{pointRegistered[0]},{-pointRegistered[1]},{pointRegistered[2]}\n"

            transformed_point = (
                np.dot(transformation_matrix[:3, :3], eval(data))
                + transformation_matrix[:3, 3]
            )

            try:
                await send_tuple(websocket, transformed_point)
            except Exception as e:
                print(f"⚠️ WebSocket send failed: {e}")

            framecount += 1  # Move to the next point
            await asyncio.sleep(0.1)  # Delay
        except Exception as e:
            print(f"Error {e}")
    utils.play_notification_sound()
    print("All points registered.")

    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloudtibia.pickle"
    )
    try:
        with open(freePoint_knee_uni, "wb") as handle:
            pickle.dump(
                np.array(points_cloud), handle, protocol=pickle.HIGHEST_PROTOCOL
            )
        print("Pickle dump successful")
    except:
        print("Error loading HC variables")

    try:
        await send_tuple(websocket, ("exit", 0, 0, 0))
    except Exception as e:
        print(f"⚠️ WebSocket send failed: {e}")


async def uniKneeDistalFemureCut(marking, websocket):
    global condition
    if not USE_BURR:
        return;


    # from scipy.spatial import ConvexHull
    def load_pickle_file(file_path, var_name):
        """Load a pickle file and handle exceptions."""
        try:
            with open(file_path, "rb") as handle:
                data = pickle.load(handle)
            # print(f"{var_name} loaded successfully")
            return data
        except FileNotFoundError:
            print(f"Error: File not found - {file_path}")
        except pickle.UnpicklingError:
            print(f"Error: Unable to unpickle data from - {file_path}")
        except Exception as e:
            print(f"Error loading {var_name}: {e}")
        return None
        # Base directory

    base_dir = os.path.join(current_dir, "..", "..", "registration_data")

    # File paths and variable names
    files_to_load = {
        "hip_center": os.path.join(base_dir, "hip_center.pickle"),
        # "femur_variables": os.path.join(base_dir, 'femur_variables.pickle'),
        # "tibia_variables_TC": os.path.join(base_dir, 'tibia_variables_TC.pickle'),
    }
    # femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")

    # Construct the correct path to the registration_data folder (up one level)
    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloud.pickle"
    )
    try:
        with open(freePoint_knee_uni, "rb") as handle:
            points_cloud = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")
    # Construct the correct path to the registration_data folder (up one level)
    pickle_path_hipCenter = os.path.join(
        current_dir, "..", "..", "registration_data", "femur_variables.pickle"
    )
    try:
        with open(pickle_path_hipCenter, "rb") as handle:
            femure_dict = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")

    points_cloud = np.array(points_cloud)
    new_points = np.array(
        [
            # femure_dict['FC']['C'],
            femure_dict["TM"]["C"],
            femure_dict["TS1"]["C"],
            femure_dict["TS2"]["C"],
            femure_dict["MDC"]["C"],
            femure_dict["MPC"]["C"],
            femure_dict["ME"]["C"],
        ]
    )
    points_cloud = np.concatenate((points_cloud, new_points), axis=0)
    startmotor = False
    while True:
        try:
            if page != "unifemur-distal-femur-cut.html":
                return
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    await asyncio.sleep(0.2)
                    break
                elif len(detections_right) == 6 and len(detections_left) == 6:
                    stop_motor()
                    await asyncio.sleep(0.2)
                else:
                    print('no detection')
                    stop_motor()
                    await asyncio.sleep(0.2)

                if page != "unifemur-distal-femur-cut.html":
                    # stop_motor()
                    return
            # Step 1: Sort by X-coordinate
            detections_right.sort(key=lambda x: x[0])
            detections_left.sort(key=lambda x: x[0])
            # Step 2: Extract first three elements
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            # Remove extracted elements
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right.sort(key=lambda x: x[1], reverse=True)
            detections_left.sort(key=lambda x: x[1], reverse=True)

            # Step 4: Extract next three elements
            next_three_right = detections_right[:3]
            next_three_left = detections_left[:3]

            # Remove extracted elements to ensure mutual exclusivity
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            # Step 5: Extract last three elements safely
            last_three_right = detections_right[-3:]
            last_three_left = detections_left[-3:]
            femure = TriangleTracker(
                first_three_right,
                first_three_left,
                rec_img_right,
                rec_img_left,
                mode="F",
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = np.array(
                    [F_points[2], F_points[0], F_points[1]]
                )  # Proper order of points
            else:
                femur_plane = np.array(
                    [F_points[2], F_points[1], F_points[0]]
                )  # Proper order of points
            Pointer_traker = TriangleTracker(
                next_three_right, next_three_left, rec_img_right, rec_img_left
            )
            Pointer_traker.find_depth()
            P_point = Pointer_traker.getLEDcordinates()
            pointer_tip_gcs = Pointer_traker.getStylusPoint()
            direction_vector = HC - femure_dict["FC"]["C"]
            tip_point = calculate_contact_point(
                led1=P_point[0],
                led2=P_point[1],
                led3=P_point[2],
                surface_normal=direction_vector,
                point=pointer_tip_gcs,
                radius=6.28,
            )
            #
            pointRegistered = utils.find_new_point_location(
                old_plane_points=femur_plane,
                new_plane_points=femure_dict["FC"]["Plane"],
                old_marked_point=pointer_tip_gcs,
            )

            a, b, c = direction_vector  # Normal vector

            B = np.array(femur_plane[2])
            C = np.array(femur_plane[1])
            distance1 = utils.distance_3d(B, C)
            scale1 = 107 / distance1  # 107 mm is the reference distance
            cutting_points_scalling = 15 / scale1

            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling
            projection_point = femure_dict["MDC"]["C"] + scaled_unit_vector

            x0, y0, z0 = projection_point  # Point on the plane
            # Calculate 'd' using the plane equation
            d = -(a * x0 + b * y0 + c * z0)
            moving_point = np.array(
                pointRegistered
            )  # Replace with your actual moving point
            # Concatenate the projection point to the points_cloud
            points_cloud = np.concatenate((points_cloud, HC[np.newaxis, :]), axis=0)
            hull = ConvexHull(points_cloud)
            moving_point_distance = np.dot(moving_point, np.array([a, b, c])) + d

            def point_in_hull(point, hull):
                new_points = np.vstack([hull.points, point])
                new_hull = ConvexHull(new_points)
                return np.array_equal(new_hull.vertices, hull.vertices)

            if point_in_hull(moving_point, hull):
                startmotor = True
                if moving_point_distance <= 0:
                    condition = 1
                    print(f"moving_point_distance {moving_point_distance} start motor")
                    await send_tuple(websocket, ("GREEN", 11, 5.6, 7.3))
                    start_motor()

                if moving_point_distance >= 0.1:
                    startmotor = False
                    print(f"moving_point_distance {moving_point_distance} stop motor")
                    await send_tuple(websocket, ("RED", 11, 5.6, 7.3))
                    condition = 0
                    stop_motor()

            else:
                condition = 2
                startmotor = False
                # if is_outside_convex_hull(moving_point, hull_points):
                #     print("Pointer is outside the convex hull")
                # else:
                # print("Pointer is inside or on the surface of the convex hull")
                await send_tuple(websocket, ("RED", 11, 5.6, 7.3))
                print(f"moving_point_distance {moving_point_distance} stop motor")
                stop_motor()
                await asyncio.sleep(0.1)
        except Exception as e:
            print(f"Error {e}")


async def accp_femure_cut(marking, websocket):
    while True:
        await asyncio.sleep(3)
        var = {
            "abbreviation": "red",
            "anteversion": 10,
            "inclination": 5,
            "data1": 25,
            "data2": 30,
            "data3": 30,
        }
        await send_json(websocket, var)
        # await asyncio.sleep(5)
        # var = {
        #     "abbreviation": "RED",
        #     "anteversion": int(10),
        #     "inclination": int(5),
        #     "data1": 25,
        #     "data2": 30,
        #     "data3": 30,
        # }
        # await send_json(websocket, var)
        # await asyncio.sleep(5)
        # var = {
        #     "abbreviation": "GREEN",
        #     "anteversion": int(10),
        #     "inclination": int(5),
        #     "data1": 40,
        #     "data2": 50,
        #     "data3": 60,
        # }
        # await send_json(websocket, var)
        # await asyncio.sleep(4)
    pickle_path_hipCenter = os.path.join(
        current_dir, "..", "..", "registration_data", "femur_variables.pickle"
    )
    try:
        with open(pickle_path_hipCenter, "rb") as handle:
            femure_dict = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")

    ValidMDCpoint = False
    while True:
        try:
            if page != "uniaccp_femure_cut.html":
                return
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9  and len(detections_left) == 9:
                    await asyncio.sleep(0.1)
                    if page != "uniaccp_femure_cut.html":
                        return
                    break

            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)
            if len(detections_right) == 9 and 9 == len(detections_left):
                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])
                # Step 2: Extract first three elements
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]

                # Remove extracted elements
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]
                detections_right.sort(key=lambda x: x[1], reverse=True)
                detections_left.sort(key=lambda x: x[1], reverse=True)

                # Step 4: Extract next three elements
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]

                # Remove extracted elements to ensure mutual exclusivity
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]

                # Step 5: Extract last three elements safely
                last_three_right = detections_right[-3:]
                last_three_left = detections_left[-3:]

                femure = TriangleTracker(
                    first_three_right, first_three_left, rec_img_right, rec_img_left
                )
                femure.find_depth()
                F_points = femure.getLEDcordinates()
                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]
                Pointer_traker = TriangleTracker(
                    next_three_right, next_three_left, rec_img_right, rec_img_left
                )
                Pointer_traker.find_depth()
                # P_point = Pointer_traker.getLEDcordinates()
                pointer_tip_gcs = Pointer_traker.getStylusPoint()
                # tip_point = calculate_contact_point(led1=P_point[0], led2=P_point[1], led3=P_point[3], center_point=, point=pointer_tip_gcs, radius=6.28):
                pointRegistered = utils.find_new_point_location(
                    old_plane_points=femur_plane,
                    new_plane_points=femure_dict["FC"]["Plane"],
                    old_marked_point=pointer_tip_gcs,
                )

                # points_cloud.append(pointRegistered)

                def load_pickle_file(file_path, var_name):
                    """Load a pickle file and handle exceptions."""
                    try:
                        with open(file_path, "rb") as handle:
                            data = pickle.load(handle)
                        # print(f"{var_name} loaded successfully")
                        return data
                    except FileNotFoundError:
                        print(f"Error: File not found - {file_path}")
                    except pickle.UnpicklingError:
                        print(f"Error: Unable to unpickle data from - {file_path}")
                    except Exception as e:
                        print(f"Error loading {var_name}: {e}")
                    return None

                # Base directory
                base_dir = os.path.join(current_dir, "..", "..", "registration_data")

                # File paths and variable names
                files_to_load = {
                    "hip_center": os.path.join(base_dir, "hip_center.pickle"),
                    "femur_variables": os.path.join(base_dir, "femur_variables.pickle"),
                    "tibia_variables_TC": os.path.join(
                        base_dir, "tibia_variables_TC.pickle"
                    ),
                }
                femur_dict = load_pickle_file(
                    files_to_load["femur_variables"], "Femur variables"
                )
                HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")

                FC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )
                MDC = utils.find_new_point_location(
                    femur_dict["MDC"]["Plane"], femur_plane, femur_dict["MDC"]["C"]
                )
                TS1 = utils.find_new_point_location(
                    femur_dict["TS1"]["Plane"], femur_plane, femur_dict["TS1"]["C"]
                )
                TS2 = utils.find_new_point_location(
                    femur_dict["TS2"]["Plane"], femur_plane, femur_dict["TS2"]["C"]
                )

                # print(f'Fc{FC} MDC {MDC} {pointer_tip_gcs}')
                # Step 1: Calculate the direction vectors (X-axis and Y-axis)
                X_vector = FC - HC  # Vector from HC to FC (X-axis)
                Y_vector = TS2 - TS1  # Vector from TS1 to TS2 (Y-axis)

                # Step 2: Normalize the vectors
                bon_X_unit = X_vector / np.linalg.norm(
                    X_vector
                )  # Normalized X-axis vector
                bon_Y_unit = Y_vector / np.linalg.norm(
                    Y_vector
                )  # Normalized Y-axis vector

                # Step 3: Calculate the Z-axis using the cross product (perpendicular to X and Y)
                Z_unit = np.cross(
                    bon_X_unit, bon_Y_unit
                )  # Z-axis vector (perpendicular to both X and Y)
                bon_Z_unit = Z_unit / np.linalg.norm(Z_unit)  # Normalize Z-axis vector

                # Step 1: Calculate the midpoint between P_point[0] and P_point[1]
                mid = (P_point[0] + P_point[1]) / 2

                # Step 2: Define the X-axis (from mid to P_point[2])
                X_vector_tracker = P_point[2] - mid

                # Step 3: Define the Y-axis (from P_point[0] to P_point[1])
                Y_vector = P_point[1] - P_point[0]

                trackerY_unit = Y_vector / np.linalg.norm(
                    Y_vector
                )  # Normalized Y-axis vector

                B = np.array(P_point[0])
                C = np.array(P_point[1])

                # Calculate the distance between B and C
                distance1 = np.linalg.norm(B - C)

                # Scale factor based on real-life reference distance
                scale1 = (
                    107 / distance1
                )  # 68mm is the real-life reference distance between B and C

                # Calculate the distance between MDC and pointer_tip_gcs
                distance_Mdc = np.linalg.norm(
                    MDC - pointer_tip_gcs
                )  # (MDC, pointer_tip_gcs)

                color = "RED"
                # Check if the distance is within the threshold
                if distance_Mdc <= 0.2:
                    print(
                        f"distance {distance_Mdc} MDC point is valid generating green Flag"
                    )
                    ValidMDCpoint = True
                    color = "GREEN"

                else:
                    ValidMDCpoint = False
                    # stop_motor()
                if ValidMDCpoint:
                    ang1, ang2 = calculate_varus_flexion_angles(
                        trackerY_unit, bon_X_unit, bon_Z_unit
                    )
                    print(f" Ang1{ang1} Ang2 {ang2}\n")
                    data = [int(ang1), int(ang2), 0]
                    # utils.SendDataToFrontEnd(data)

                    angle = 180 - angle_between_vectors(X_vector, X_vector_tracker)
                    # print(f'angle  ************** {angle}')
                    if angle <= 10:
                        # start_motor()
                        # data = f"{pointRegistered[0]},{pointRegistered[1]},{pointRegistered[2]},{condition}\n"
                        # self.point_queue.put(data)
                        print(f"data {data}")
                    else:
                        data = [
                            int(ang1),
                            int(ang2),
                            1,
                        ]  # Marking it as out of safe zone
                        print("Angles are outside the safe zone:}")
                        color = "RED"
                    var = {
                        "abbreviation": color,
                        "anteversion": int(ang1),
                        "inclination": int(ang2),
                        "data1": "...",
                        "data2": "...",
                        "data3": "...",
                    }
                    await send_tuple(websocket, var)

        except Exception as e:
            print(f"ERROR {e}")


async def unitibia_cut(marking, websocket):
    # if not USE_BURR:
    #     return;
    print('tst')
    base_dir = os.path.join(current_dir, "..", "..", "registration_data")
    # File paths and variable names
    files_to_load = {
        "hip_center": os.path.join(base_dir, "hip_center.pickle"),
        "femur_variables": os.path.join(base_dir, "femur_variables.pickle"),
        "tibia_variables_TC": os.path.join(base_dir, "tibia_variables_TC.pickle"),
        "tibia_variables": os.path.join(base_dir, "tibia_variables.pickle"),
    }
    # femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")
    femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    tibia_dict_tc = load_pickle_file(
        files_to_load["tibia_variables_TC"], "Tibia TC variables"
    )
    tibia_dict = load_pickle_file(files_to_load["tibia_variables"], "Tibia variables")

    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloudtibia.pickle"
    )
    try:
        with open(freePoint_knee_uni, "rb") as handle:
            points_cloud = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")

    while True:
        try:
            if page != "unitibia-cut.html":
                return
            detections_left, detections_right = await marking.handle_irq_average(n=10)
            print(f' detections_left {len(detections_left)} detections_right {len(detections_right)}')
            if not (len(detections_right) == 9 and len(detections_left)) == 9:
                await asyncio.sleep(0.5)
                continue
                    # Step 1: Sort by X-coordinate
            detections_right.sort(key=lambda x: x[0])
            detections_left.sort(key=lambda x: x[0])
            # Step 2: Extract first three elements
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            # Remove extracted elements
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right.sort(key=lambda x: x[1], reverse=True)
            detections_left.sort(key=lambda x: x[1], reverse=True)

            # Step 4: Extract next three elements
            next_three_right = detections_right[:3]
            next_three_left = detections_left[:3]

            # Remove extracted elements to ensure mutual exclusivity
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            # Step 5: Extract last three elements safely
            last_three_right = detections_right[-3:]
            last_three_left = detections_left[-3:]
            femure = TriangleTracker(
                first_three_right, first_three_left, rec_img_right, rec_img_left, mode='F'
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()
            print(f'femur_plane  {F_points}')
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]
            print(f'femur_plane {femur_plane}')
            Pointer_traker = TriangleTracker(
                next_three_right, next_three_left, rec_img_right, rec_img_left
            )
            Pointer_traker.find_depth()
            pointer_tip_gcs = Pointer_traker.getStylusPoint()
            print(f'pointer_tip_gcs {pointer_tip_gcs}')
            NewFC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            tibia = TriangleTracker(
                last_three_right, last_three_left, rec_img_right, rec_img_left
            )
            tibia.find_depth()
            T_Points = tibia.getLEDcordinates()
            print(f'T_Points {T_Points}')
            if T_Points[1][1] >= T_Points[0][1]:
                tibia_plane = [T_Points[2], T_Points[0], T_Points[1]]
            else:
                tibia_plane = [T_Points[2], T_Points[1], T_Points[0]]

            tibia_plane[2] = NewFC
            NewTC = utils.find_new_point_location(
                tibia_dict_tc["TC"]["Plane"], tibia_plane, tibia_dict_tc["TC"]["C"]
            )

            tibia_plane[2] = NewTC
            pointRegistered = utils.find_new_point_location(
                tibia_plane, tibia_dict_tc["TC"]["Plane"], pointer_tip_gcs
            )
            points_cloud = np.array(points_cloud)

            new_points = np.array(
                [
                    tibia_dict_tc['TC']['C'],
                    tibia_dict_tc['AL1']['C'],
                    tibia_dict_tc['AL2']['C'],
                    tibia_dict["MC"]["C"],
                    tibia_dict["MP"]["C"],
                    tibia_dict["MM"]["C"],
                    tibia_dict["LM"]["C"],
                ]
            )
            ANC = np.array(
                [
                    (tibia_dict["MM"]["C"][i] + tibia_dict["LM"]["C"][i]) / 2
                    for i in range(3)
                ]
            )
            # points_cloud = np.concatenate((points_cloud, new_points), axis=0)
            B = np.array(femur_plane[0])
            C = np.array(femur_plane[1])
            distance1 = utils.distance_3d(B, C)
            # v_am = np.subtract(B, C)
            # distance1 = np.linalg.norm(v_am)
            scale1 = 107 / distance1  # 107 mm is the reference distance
            cutting_points_scalling = 10 / scale1

            direction_vector = tibia_dict_tc["TC"]["C"] - ANC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling

            projection_point = tibia_dict["MC"]["C"] - scaled_unit_vector
            # projection_point = tibia_dict['MC']['C']
            a, b, c = direction_vector  # Normal vector
            x0, y0, z0 = projection_point  # Point on the plane
            # Calculate 'd' using the plane equation
            d = -(a * x0 + b * y0 + c * z0)
            # Concatenate the new points to the existing points_cloud
            points_cloud = np.concatenate((points_cloud, new_points), axis=0)
            hull = ConvexHull(points_cloud)
            moving_point = np.array(pointRegistered)  #
            moving_point_distance = np.dot(moving_point, np.array([a, b, c])) + d

            def point_in_hull(point, hull):
                new_points = np.vstack([hull.points, point])
                new_hull = ConvexHull(new_points)
                return np.array_equal(new_hull.vertices, hull.vertices)

            if point_in_hull(moving_point, hull):
                startmotor = True
                await send_tuple(websocket, ("GREEN", 11, 5.6, 7.3))
                if moving_point_distance >= 0:
                    print(f"moving_point_distance {moving_point_distance} startmotor")
                    start_motor()
                if moving_point_distance <= 0.1:
                    await send_tuple(websocket, ("YELLOW", 11, 5.6, 7.3))
                    startmotor = False
                    print(f"moving_point_distance {moving_point_distance} stopmotor")
                    stop_motor()

            else:
                await send_tuple(websocket, ("RED", 11, 5.6, 7.3))
                startmotor = False
                # if is_outside_convex_hull(moving_point, hull_points):
                #     print("Pointer is outside the convex hull")
                # else:
                # print("Pointer is inside or on the surface of the convex hull")
                print(f"moving_point_distance {moving_point_distance} stopmotor ")
                stop_motor()
            time.sleep(0.2)

        except Exception as e:
            print(f"ERROR {e}")


async def unitkr_screen_2(marking, websocket):
    try:
        utils.SendDataToFrontEnd(0.0)
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
    except:
        print("Error loading femur variables")
        return -1

    while True:
        try:
            if page != "unitkr-screen-2.html":
                return
            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                img_left_rectified, img_right_rectified = latest_frame
            else:
                return
            detections_right = utils.find_contours_Kmeans(img_right_rectified)
            detections_left = utils.find_contours_Kmeans(img_left_rectified)
            if len(detections_right) != 9 and len(detections_left) != 9:
                if page != "unitkr-screen-2.html":
                    return
                await asyncio.sleep(0.1)
                continue
            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            next_three_right = detections_right[:3]
            next_three_left = detections_left[:3]
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]
            detections_right = sorted(
                detections_right, key=lambda x: x[1], reverse=True
            )
            detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            femure = TriangleTracker(
                next_three_right,
                next_three_left,
                img_right_rectified,
                img_left_rectified,
                mode="F",
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()
            Pointer_traker = TriangleTracker(
                first_three_right,
                first_three_left,
                img_right_rectified,
                img_left_rectified,
            )
            Pointer_traker.find_depth()
            tip = Pointer_traker.getStylusPoint()

            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            B = np.array(femur_plane[1])
            C = np.array(femur_plane[0])
            distance1 = utils.distance_3d(B, C)
            scale1 = 107 / distance1  # 107 mm is the reference distance
            MDC = utils.find_new_point_location(
                femur_dict["MDC"]["Plane"], femur_plane, femur_dict["MDC"]["C"]
            )
            MPC = utils.find_new_point_location(
                femur_dict["MPC"]["Plane"], femur_plane, femur_dict["MPC"]["C"]
            )

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )

            TS1 = utils.find_new_point_location(
                femur_dict["TS1"]["Plane"], femur_plane, femur_dict["TS1"]["C"]
            )
            TS2 = utils.find_new_point_location(
                femur_dict["TS2"]["Plane"], femur_plane, femur_dict["TS2"]["C"]
            )

            distace_Mdc = utils.distance_2d(MDC, tip) * scale1
            distace_Mpc = utils.distance_2d(MPC, tip) * scale1
            distace_ts1 = utils.distance_2d(TS1, tip) * scale1
            distace_ts2 = utils.distance_3d(TS2, tip) * scale1
            distace_fc = utils.distance_3d(FC, tip) * scale1
            DISTANCE_CONSTANT = 0.5 / scale1
            print(f" MDC {MDC} {tip} distace_Mdc {distace_Mdc}  scale1 {scale1}   DISTANCE_CONSTANT {DISTANCE_CONSTANT}")

            def round_to_two_decimals(value):
                return round(value, 2)

            # Compare distances with the rounded constant
            rounded_distance_constant = round_to_two_decimals(DISTANCE_CONSTANT)

            if round_to_two_decimals(distace_Mdc) <= rounded_distance_constant:
                print(
                    f"distance {distace_Mdc} MDC point is valid generating green Flag"
                )
                utils.SendDataToFrontEnd(round_to_two_decimals(distace_Mdc))
            elif round_to_two_decimals(distace_Mpc) <= rounded_distance_constant:
                print(
                    f"distance {distace_Mpc} MPC point is valid generating green Flag"
                )
                utils.SendDataToFrontEnd(round_to_two_decimals(distace_Mpc))
            elif round_to_two_decimals(distace_ts1) <= rounded_distance_constant:
                print(
                    f"distance {distace_ts1} LDC point is valid generating green Flag"
                )
                utils.SendDataToFrontEnd(round_to_two_decimals(distace_ts1))
            elif round_to_two_decimals(distace_ts2) <= rounded_distance_constant:
                print(
                    f"distance {distace_ts2} LPC point is valid generating green Flag"
                )
                utils.SendDataToFrontEnd(round_to_two_decimals(distace_ts2))
            elif round_to_two_decimals(distace_fc) <= rounded_distance_constant:
                print(f"distance {distace_fc} FC point is valid generating green Flag")
                utils.SendDataToFrontEnd(round_to_two_decimals(distace_fc))
            else:
                print(f"invalid distance red Flag Generated")
                await asyncio.sleep(0.1)
                try:
                    await send_tuple(
                        websocket,
                        (f"{round_to_two_decimals(distace_Mdc)}", 0.2, 0.2, 0.2),
                    )
                except Exception as e:
                    print(f"⚠️ Error: {e}")
        except Exception as e:
            print(f"Error in {inspect.currentframe().f_code.co_name}: {e}")


async def AlignmentAnglesUni(marking, websocket):
    try:
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        with open(pickle_path_femur_hc, "rb") as handle:
            HC = np.array(pickle.load(handle))
        print("Femur HC variables loaded successfully")

        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
        with open(pickle_path_tibia_tc, "rb") as tibia_tc_handle:
            tibia_tc = pickle.load(tibia_tc_handle)
        pickle_path_tibia = os.path.join(base_dir, "tibia_variables.pickle")
        with open(pickle_path_tibia, "rb") as tibia_handle:
            tibia_dict = pickle.load(tibia_handle)
    except Exception as e:
        print(f"Error loading femur variables: {e}")
        return -1

    while True:
        try:
            if page != "uniinner-page6.html":
                return
            if websocket.client_state != WebSocketState.CONNECTED:
                print("WebSocket disconnected, terminating loop.")
                break

            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    img_left_rectified, img_right_rectified = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(img_right_rectified)
                detections_left = utils.find_contours_Kmeans(img_left_rectified)
                if len(detections_right) == 6 and len(detections_left) == 6:
                    break
                await asyncio.sleep(0.1)
                if page != "uniinner-page6.html":
                    return


            detections_right = sorted(
                detections_right, key=lambda detection: detection[0]
            )
            detections_left = sorted(
                detections_left, key=lambda detection: detection[0]
            )
            femure = TriangleTracker(
                detections_right[:3],
                detections_left[:3],
                img_right_rectified,
                img_left_rectified,
                mode="F",
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()

            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            tebia = TriangleTracker(
                detections_right[3:],
                detections_left[3:],
                img_right_rectified,
                img_left_rectified,
                mode="T",
            )
            tebia.find_depth()
            T_Points = tebia.getLEDcordinates()

            if T_Points[1][1] >= T_Points[0][1]:
                tibia_plane = [T_Points[2], T_Points[0], T_Points[1]]
            else:
                tibia_plane = [T_Points[2], T_Points[1], T_Points[0]]

            tibia_plane[2] = FC
            TC = utils.find_new_point_location(
                tibia_tc["TC"]["Plane"], tibia_plane, tibia_tc["TC"]["C"]
            )
            tibia_plane[2] = TC
            MM = utils.find_new_point_location(
                tibia_dict["MM"]["Plane"], tibia_plane, tibia_dict["MM"]["C"]
            )
            LM = utils.find_new_point_location(
                tibia_dict["LM"]["Plane"], tibia_plane, tibia_dict["LM"]["C"]
            )

            ANC = (np.array(MM) + np.array(LM)) / 2

            def vector2_signed_angle(A, B):
                AProjXZ = np.array([A[0], A[2]])
                BProjXZ = np.array([B[0], B[2]])
                angle = np.degrees(
                    np.arctan2(np.cross(AProjXZ, BProjXZ), np.dot(AProjXZ, BProjXZ))
                )
                return np.round(angle * 2) / 2

            def vector2_signed_angle_xy(A, B):
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])
                angle = np.degrees(
                    np.arctan2(
                        np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2],
                        np.dot(AProjXY, BProjXY),
                    )
                )
                return np.round(angle * 2) / 2

            vectorF = HC - FC
            vectorT = TC - ANC
            ang = vector2_signed_angle(vectorF, vectorT)
            ROM = vector2_signed_angle_xy(vectorF, vectorT)
            print(f"******* {ang}  {ROM}")
            try:
                await send_tuple(websocket, (abs(int(ROM)), abs(int(ang)), 0.2, 0.2))
            except Exception as e:
                print(f"⚠️ Error sending via websocket: {e}")
                break  # Break on send failure (likely closed connection)
            await asyncio.sleep(1)

        except Exception as e:
            print(f"Error in {inspect.currentframe().f_code.co_name}: {e}")
            break  # Optional: Break on unexpected errors (to avoid infinite loop on error)


async def Tebia_marking_tc(marking, websocket):
    framecount = 0
    variable_dict = {}
    point_reg = ["TC"]

    try:
        pickle_path_femur = os.path.join(
            current_dir, "..", "..", "registration_data", "femur_variables.pickle"
        )
        with open(pickle_path_femur, "rb") as handle:
            femur_dict = pickle.load(handle)
        print("✅ Femur variables loaded successfully")
    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading femur variables: {e}")
        return {}

    while framecount < len(point_reg):
        try:
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    break
                await asyncio.sleep(0.1)
                if page != "multiple-page.html":
                    return

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])

            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            detections_right = sorted(
                detections_right[3:], key=lambda x: x[1], reverse=True
            )
            detections_left = sorted(
                detections_left[3:], key=lambda x: x[1], reverse=True
            )

            next_three_right = sorted(detections_right[:3], key=lambda x: x[0])
            next_three_left = sorted(detections_left[:3], key=lambda x: x[0])
            last_three_right = sorted(detections_right[-3:], key=lambda x: x[0])
            last_three_left = sorted(detections_left[-3:], key=lambda x: x[0])

            Pointer_tracker = TriangleTracker(
                next_three_right, next_three_left, rec_img_right, rec_img_left
            )
            Pointer_tracker.find_depth()
            pointer_tip_gcs = Pointer_tracker.getStylusPoint()
            femur = TriangleTracker(
                first_three_right,
                first_three_left,
                rec_img_right,
                rec_img_left,
                mode="F",
            )
            femur.find_depth()
            F_points = femur.getLEDcordinates()
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )

            NewFC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            tibia = TriangleTracker(
                last_three_right, last_three_left, rec_img_right, rec_img_left, "T"
            )
            tibia.find_depth()
            T_Points = tibia.getLEDcordinates()
            tibia_plane = (
                [T_Points[2], T_Points[0], NewFC]
                if T_Points[1][1] >= T_Points[0][1]
                else [T_Points[2], T_Points[1], NewFC]
            )
            variable_memory = {"C": pointer_tip_gcs, "Plane": tibia_plane}
            variable_dict[point_reg[framecount]] = variable_memory
            try:
                await send_tuple(websocket, (f"{point_reg[framecount]}", 0.2, 0.2, 0.2))
                utils.play_notification_sound()
                print(f"✅ Point registered: {point_reg[framecount]}")
            except Exception as e:
                print(f"⚠️ Error: {e}")

            framecount += 1
            await asyncio.sleep(0.1)
        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.1)
    print(f"🎯 All points registered. Saving data... ")
    pickle_path_tibia = os.path.join(
        current_dir, "..", "..", "registration_data", "tibia_variables_TC.pickle"
    )
    os.makedirs(os.path.dirname(pickle_path_tibia), exist_ok=True)
    with open(pickle_path_tibia, "wb") as handle:
        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
    print("✅ Pickle dump successful ")
    print(variable_dict)
    return


async def UniTebia_marking_tc(marking, websocket):
    framecount = 0
    variable_dict = {}
    point_reg = ["TC", "AL1", "AL2"]
    try:
        pickle_path_femur = os.path.join(
            current_dir, "..", "..", "registration_data", "femur_variables.pickle"
        )
        with open(pickle_path_femur, "rb") as handle:
            femur_dict = pickle.load(handle)
        print("✅ Femur variables loaded successfully")
    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading femur variables: {e}")
        return {}

    while framecount < len(point_reg):
        try:
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                # Wait until at least 9 detections are available

                if len(detections_right) == 9 or len(detections_left) == 9:
                    break
                await asyncio.sleep(0.2)
                if page != "unimultiple-page.html":
                    return
            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])

            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            detections_right = sorted(
                detections_right[3:], key=lambda x: x[1], reverse=True
            )
            detections_left = sorted(
                detections_left[3:], key=lambda x: x[1], reverse=True
            )

            next_three_right = sorted(detections_right[:3], key=lambda x: x[0])
            next_three_left = sorted(detections_left[:3], key=lambda x: x[0])
            last_three_right = sorted(detections_right[-3:], key=lambda x: x[0])
            last_three_left = sorted(detections_left[-3:], key=lambda x: x[0])

            Pointer_traker = TriangleTracker(
                next_three_right, next_three_left, rec_img_right, rec_img_left
            )
            Pointer_traker.find_depth()
            pointer_tip_gcs = Pointer_traker.getStylusPoint()

            femure = TriangleTracker(
                first_three_right,
                first_three_left,
                rec_img_right,
                rec_img_left,
                mode="F",
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )

            try:
                pickle_path_femure = os.path.join(
                    current_dir,
                    "..",
                    "..",
                    "registration_data",
                    "femur_variables.pickle",
                )
                with open(pickle_path_femure, "rb") as handle:
                    femur_dict = pickle.load(handle)
            except:
                print("Error loading femur variables")

            NewFC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )

            tebia = TriangleTracker(
                last_three_right, last_three_left, rec_img_right, rec_img_left, mode="T"
            )
            tebia.find_depth()
            T_Points = tebia.getLEDcordinates()
            if T_Points[1][1] >= T_Points[0][1]:
                tibia_plane = [T_Points[2], T_Points[0], NewFC]
            else:
                tibia_plane = [T_Points[2], T_Points[1], NewFC]

            if framecount == 0:
                variable_memory = {"C": pointer_tip_gcs, "Plane": tibia_plane}
            else:
                pointRegistered = utils.find_new_point_location(
                    old_plane_points=tibia_plane,
                    new_plane_points=variable_dict["TC"]["Plane"],
                    old_marked_point=pointer_tip_gcs,
                )
                variable_memory = {
                    "C": pointRegistered,
                    "Plane": variable_dict["TC"]["Plane"],
                }
            variable_dict[point_reg[framecount]] = variable_memory

            try:
                await send_tuple(websocket, (f"{point_reg[framecount]}", 0.2, 0.2, 0.2))
                utils.play_notification_sound()
                print(f"✅ Point registered: {point_reg[framecount]}")
            except Exception as e:
                print(f"⚠️ Error: {e}")
            framecount += 1
            await asyncio.sleep(0.1)
        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.5)
    print("🎯 All points registered. Saving data...")
    pickle_path_tibia = os.path.join(
        current_dir, "..", "..", "registration_data", "tibia_variables_TC.pickle"
    )
    os.makedirs(os.path.dirname(pickle_path_tibia), exist_ok=True)
    with open(pickle_path_tibia, "wb") as handle:
        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
    print("✅ Pickle dump successful")
    return


async def FemureMarking(marking, websocket):
    global framecount, variable_dict
    framecount = 1
    try:
        # Load Transformation Plane (TP) variables
        pickle_path_hipCenter = os.path.join(
            current_dir, "..", "..", "registration_data", "hip_center.pickle"
        )
        with open(pickle_path_hipCenter, "rb") as handle:
            femur_HC = pickle.load(handle)
        print("✅ Femur HC variables loaded successfully")

    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    point_reg = ["HC", "FC", "ME", "LE", "FAP", "MDC", "LDC", "MPC", "LPC", "AC"]

    while framecount < len(point_reg):
        try:
            if page not in ["revmultiple-page2.html", "multiple-page2.html"]:
                return
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    await asyncio.sleep(0.1)
                    break
                else:
                    await asyncio.sleep(0.2)
                    if page not in ["revmultiple-page2.html", "multiple-page2.html"]:
                        return
                    continue
            if point_reg[framecount] in ["MPC", "LDC", "LPC", "MDC"]:
                detections_right = sorted(detections_right, key=lambda x: x[0])
                detections_left = sorted(detections_left, key=lambda x: x[0])
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]
                detections_right = sorted(
                    detections_right, key=lambda x: x[1], reverse=True
                )
                detections_left = sorted(
                    detections_left, key=lambda x: x[1], reverse=True
                )
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]
                #
                # first_three_right = sorted(first_three_right, key=lambda x: x[0])
                # first_three_left = sorted(first_three_left, key=lambda x: x[0])

            else:
                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])
                # Delete last three elements (in-place)
                del detections_right[-3:]
                del detections_left[-3:]

                detections_right.sort(key=lambda x: x[1], reverse=True)
                detections_left.sort(key=lambda x: x[1], reverse=True)
                first_three_right, first_three_left = (
                    detections_right[:3],
                    detections_left[:3],
                )
                next_three_right, next_three_left = (
                    detections_right[3:6],
                    detections_left[3:6],
                )

            # Process the detections and compute the 3D points
            Pointer_tracker = TriangleTracker(
                detections_right=first_three_right,
                detections_left=first_three_left,
                frame_right=rec_img_right,
                frame_left=rec_img_left,
            )
            Pointer_tracker.find_depth()
            p_ = Pointer_tracker.getLEDcordinates()
            pointer_tip_gcs = Pointer_tracker.getStylusPoint()

            Femur = TriangleTracker(
                detections_right=next_three_right,
                detections_left=next_three_left,
                frame_right=rec_img_right,
                frame_left=rec_img_left,
                mode="F",
            )
            Femur.find_depth()
            F_points = Femur.getLEDcordinates()
            print(f" point {point_reg[framecount]} F_points {F_points}")
            # Arrange femur plane points
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )
            if framecount == 1:
                x1, y1, z1 = F_points[2]
                point_A = (F_points[1] + F_points[0]) / 2.0
                x2, y2, z2 = point_A
                m, b = utils.find_line_equation(x1, y1, x2, y2)

                def d_min(x0, y0, m, b):
                    return abs(m * x0 - y0 + b) / math.sqrt(1 + m**2)

                x0, y0, z0 = pointer_tip_gcs
                distance = d_min(x0, y0, m, b)

                variable_dict[point_reg[framecount]] = {
                    "C": pointer_tip_gcs,
                    "Plane": femur_plane,
                }

            elif point_reg[framecount] == "FAP":
                pointRegistered = utils.find_new_point_location(
                    femur_plane, variable_dict["FC"]["Plane"], pointer_tip_gcs
                )
                p_points = Pointer_tracker.getLEDcordinates()
                variable_dict[point_reg[framecount]] = {
                    "C": (pointRegistered, p_points[2]),
                    "Plane": variable_dict["FC"]["Plane"],
                }
            else:
                pointRegistered = utils.find_new_point_location(
                    femur_plane, variable_dict["FC"]["Plane"], pointer_tip_gcs
                )
                variable_dict[point_reg[framecount]] = {
                    "C": pointRegistered,
                    "Plane": variable_dict["FC"]["Plane"],
                }
            try:
                await send_tuple(websocket, (f"{point_reg[framecount]}", 4.0, 5.6, 7.3))
            except Exception as e:
                print(f"⚠️ WebSocket send failed: {e}")
            utils.play_notification_sound()

            framecount += 1  # Move to the next point
            await asyncio.sleep(0.1)  # Delay

        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.1)

    print("🎯 All points registered. Saving data...")

    pickle_path_femure = os.path.join(
        current_dir, "..", "..", "registration_data", "femur_variables.pickle"
    )
    os.makedirs(os.path.dirname(pickle_path_femure), exist_ok=True)

    with open(pickle_path_femure, "wb") as handle:
        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

    print("✅ Pickle dump successful")

    direction_vector = np.array(femur_HC) - np.array(variable_dict["FC"]["C"])
    normal_vector = direction_vector / np.linalg.norm(direction_vector)

    A, B, C = normal_vector
    D = -np.dot(normal_vector, np.array(variable_dict["FC"]["C"]))

    def distance_from_point_to_plane(point, A, B, C, D):
        point = np.array(point)
        return abs(A * point[0] + B * point[1] + C * point[2] + D) / np.sqrt(
            A**2 + B**2 + C**2
        )

    distance_to_MDC = distance_from_point_to_plane(
        variable_dict["MDC"]["C"], A, B, C, D
    )
    distance_to_LDC = distance_from_point_to_plane(
        variable_dict["LDC"]["C"], A, B, C, D
    )

    femure_BoneCut_Point = "MDC" if distance_to_MDC > distance_to_LDC else "LDC"

    FemureCuttingPath = os.path.join(
        current_dir, "..", "..", "registration_data", "FemureCuttingPoint.txt"
    )
    with open(FemureCuttingPath, "wb") as handle:
        pickle.dump(femure_BoneCut_Point, handle, protocol=pickle.HIGHEST_PROTOCOL)

    framecount = 0
    variable_dict = {}


async def FemureMarkingUni(marking, websocket):
    global framecount, variable_dict
    framecount = 1
    try:
        # Load Transformation Plane (TP) variables
        pickle_path_hipCenter = os.path.join(
            current_dir, "..", "..", "registration_data", "hip_center.pickle"
        )
        with open(pickle_path_hipCenter, "rb") as handle:
            femur_HC = pickle.load(handle)
        print("✅ Femur HC variables loaded successfully")

    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    point_reg = ["HC", "FC", "TM", "TS1", "TS2", "MDC", "MPC", "ME"]

    while framecount < len(point_reg):
        if page != "unimultiple-page2.html":
            return
        try:
            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame
            else:
                return
            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)
            if len(detections_right) < 9 and len(detections_left) < 9:
                await asyncio.sleep(0.1)
                continue
            if point_reg[framecount] in ["TS1", "TS2", "MPC", "MDC"]:
                detections_right = sorted(detections_right, key=lambda x: x[0])
                detections_left = sorted(detections_left, key=lambda x: x[0])
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]
                detections_right = sorted(
                    detections_right, key=lambda x: x[1], reverse=True
                )
                detections_left = sorted(
                    detections_left, key=lambda x: x[1], reverse=True
                )
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]
            else:
                detections_right.sort(key=lambda x: x[0])
                detections_left.sort(key=lambda x: x[0])
                # Delete last three elements (in-place)
                del detections_right[-3:]
                del detections_left[-3:]

                detections_right.sort(key=lambda x: x[1], reverse=True)
                detections_left.sort(key=lambda x: x[1], reverse=True)
                first_three_right, first_three_left = (
                    detections_right[:3],
                    detections_left[:3],
                )
                next_three_right, next_three_left = (
                    detections_right[3:6],
                    detections_left[3:6],
                )

            Pointer_tracker = TriangleTracker(
                detections_right=first_three_right,
                detections_left=first_three_left,
                frame_right=rec_img_right,
                frame_left=rec_img_left,
            )
            Pointer_tracker.find_depth()
            pointer_tip_gcs = Pointer_tracker.getStylusPoint()

            Femur = TriangleTracker(
                detections_right=next_three_right,
                detections_left=next_three_left,
                frame_right=rec_img_right,
                frame_left=rec_img_left,
                mode="F",
            )
            Femur.find_depth()
            F_points = Femur.getLEDcordinates()

            print(f' F_points {F_points}')
            # Arrange femur plane points
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )
            if framecount == 1:
                variable_memory = {"C": pointer_tip_gcs, "Plane": femur_plane}
                variable_dict[point_reg[framecount]] = variable_memory
                print(f"pointRegistered {point_reg[framecount]} {pointer_tip_gcs}")
            else:
                pointRegistered = utils.find_new_point_location(
                    old_plane_points=femur_plane,
                    new_plane_points=variable_dict["FC"]["Plane"],
                    old_marked_point=pointer_tip_gcs,
                )
                variable_memory = {
                    "C": pointRegistered,
                    "Plane": variable_dict["FC"]["Plane"],
                }
                print(f"pointRegistered {point_reg[framecount]} {pointRegistered}")

            variable_dict[point_reg[framecount]] = variable_memory
            try:
                await send_tuple(websocket, (f"{point_reg[framecount]}", 4.0, 5.6, 7.3))
            except Exception as e:
                print(f"⚠️ WebSocket send failed: {e}")
            utils.play_notification_sound()
            framecount += 1  # Move to the next point
            await asyncio.sleep(0.1)  # Delay

        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.1)

    print("🎯 All points registered. Saving data...")

    pickle_path_femure = os.path.join(
        current_dir, "..", "..", "registration_data", "femur_variables.pickle"
    )
    os.makedirs(os.path.dirname(pickle_path_femure), exist_ok=True)

    with open(pickle_path_femure, "wb") as handle:
        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

    print("✅ Pickle dump successful")
    framecount = 0
    variable_dict = {}


def load_pickle_file(file_path, var_name):
    """Load a pickle file and handle exceptions."""
    try:
        with open(file_path, "rb") as handle:
            data = pickle.load(handle)
        # print(f"{var_name} loaded successfully")
        return data
    except FileNotFoundError:
        print(f"Error: File not found - {file_path}")
    except pickle.UnpicklingError:
        print(f"Error: Unable to unpickle data from - {file_path}")
    except Exception as e:
        print(f"Error loading {var_name}: {e}")
    return None


async def uniFree_point_collection_femur(marking, websocket):
    global framecount, variable_dict
    points_cloud = []
    framecount = 0
    base_dir = os.path.join(current_dir, "..", "..", "registration_data")
    # File paths and variable names
    files_to_load = {
        "hip_center": os.path.join(base_dir, "hip_center.pickle"),
        "femur_variables": os.path.join(base_dir, "femur_variables.pickle"),
        # "tibia_variables_TC": os.path.join(base_dir, 'tibia_variables_TC.pickle'),
        # "tibia_variables": os.path.join(base_dir, 'tibia_variables.pickle'),
    }
    femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")
    print(f"femur_dict {femur_dict}")
    camera_reference = np.array(
        [
            HC,
            femur_dict["FC"]["C"],
            femur_dict["TM"]["C"],
            femur_dict["TS1"]["C"],
            femur_dict["TS2"]["C"],
            femur_dict["MDC"]["C"],
            femur_dict["MPC"]["C"],
            femur_dict["ME"]["C"],
        ]
    )
    print(f' camera_reference  {camera_reference}')
    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
    obj_points_uniFemure = np.array(
        [
            [
                -102.9053543979592,
                -14.204959734693874,
                -8.730872840816328,
            ],  # Hip Center in STL model
            [-52.2903404, -3.131314, -11.5643253],  # FC
            [-52.2250595, -1.853195, -9.8895073],  # TM
            [-53.1750908, -1.504328, -11.4055157],  # TS1
            [-52.1881371, -3.095505, -10.8363781],  # TS2
            [-51.2940788, -3.5388589, -8.9086733],  # MDC
            [-52.9359016, -6.9525371, -8.3687782],  # MPC
            [-54.2269897, -3.982173, -6.5660419],  # ME
        ]
    )
    for i in obj_points_uniFemure:
        await send_tuple(websocket, i)
        await asyncio.sleep(1)

    # Calculate the transformation matrix based on the camera reference
    transformation_matrix = calculate_transformation_matrix_with_scaling(
        camera_reference, obj_points_uniFemure
    )
    while framecount < 250:
        try:
            if page != "uniFree_point_collection_femur.html":
                return
            while True:
                latest_frame = await marking.get_latest_frame()
                if not latest_frame:
                    return

                rec_img_left, rec_img_right = latest_frame
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)

                if len(detections_right) == 9 and len(detections_left) == 9:

                    break  # Exit loop when both have exactly 9 detections
                print(f"{len(detections_right)} {len(detections_left)} ")
                await asyncio.sleep(0.02)
                # if page != "uniFree_point_collection_femur.html":
                #     return

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            next_three_right = detections_right[:3]
            next_three_left = detections_left[:3]
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]
            detections_right = sorted(
                detections_right, key=lambda x: x[1], reverse=True
            )
            detections_left = sorted(
                detections_left, key=lambda x: x[1], reverse=True
            )
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]



            femure = TriangleTracker(
                next_three_right, next_three_left, rec_img_right, rec_img_left
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()

            # print(f'Pointer_traker {p_point}')
            Pointer_traker = TriangleTracker(
                first_three_right,
                first_three_left,
                rec_img_right,
                rec_img_left,
                mode="F",
            )
            Pointer_traker.find_depth()
            pointer_tip_gcs = Pointer_traker.getStylusPoint()
            print(f'F_points  {F_points} ')
            # Arrange femur plane points
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )
            pointRegistered = utils.find_new_point_location(
                old_plane_points=femur_plane,
                new_plane_points=femur_dict["FC"]["Plane"],
                old_marked_point=pointer_tip_gcs,
            )
            points_cloud.append(pointRegistered)
            data = f"{pointRegistered[0]},{pointRegistered[1]},{pointRegistered[2]}\n"
            # data = f"{pointRegistered[0]},{pointRegistered[1]},{pointRegistered[2]},{condition}\n"
            point = np.array(eval(data), dtype=np.float32)
            point = [
                point[0],
                -point[1],
                point[2],
            ]  # Adjust Z axis orientation if needed
            transformed_point = (
                np.dot(transformation_matrix[:3, :3], point)
                + transformation_matrix[:3, 3]
            )
            try:
                await send_tuple(websocket, transformed_point)
            except Exception as e:
                print(f"⚠️ WebSocket send failed: {e}")
            # utils.play_notification_sound()
            framecount += 1  # Move to the next point
            await asyncio.sleep(0.1)  # Delay
            print(f"framecount  {framecount} pointRegistered {pointRegistered} transformed_point {transformed_point}")
        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.1)

    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloud.pickle"
    )
    try:
        with open(freePoint_knee_uni, "wb") as handle:
            pickle.dump(
                np.array(points_cloud), handle, protocol=pickle.HIGHEST_PROTOCOL
            )
        print("Pickle dump successful")
    except:
        print("Error loading HC variables")
    framecount = 0
    variable_dict = {}
    try:
        await websocket.send_text("exit")
    except Exception as e:
        print(f"⚠️ WebSocket send failed: {e}")
    return


async def PrimaryKneeLandMarkVerification(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
    except Exception as e:
        print(f"Error loading femur or tibia variables: {e}")
        return -1
    while True:
        try:
            # Ensure frames are available in the queue
            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame
            else:
                return
            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)
            # if len(detections_right) == 9 and len(detections_left) == 9:
            #     await asyncio.sleep(0.1)
            #     break
            if page not in ["tkr-screen-2.html", "revtkr-screen-2.html"]:
                return

            if len(detections_right) == 9 and len(detections_left) == 9:
                detections_right = sorted(detections_right, key=lambda x: x[0])
                detections_left = sorted(detections_left, key=lambda x: x[0])
                next_three_right = detections_right[:3]
                next_three_left = detections_left[:3]
                detections_right = detections_right[3:]
                detections_left = detections_left[3:]
                detections_right = sorted(
                    detections_right, key=lambda x: x[1], reverse=True
                )
                detections_left = sorted(
                    detections_left, key=lambda x: x[1], reverse=True
                )
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]
                femure = TriangleTracker(
                    next_three_right, next_three_left, rec_img_right, rec_img_left, "F"
                )
                femure.find_depth()
                F_points = femure.getLEDcordinates()
                Pointer_traker = TriangleTracker(
                    first_three_right,
                    first_three_left,
                    rec_img_right,
                    rec_img_left,
                )
                Pointer_traker.find_depth()
                tip = Pointer_traker.getStylusPoint()

                if F_points[1][1] >= F_points[0][1]:
                    femur_plane = [F_points[2], F_points[0], F_points[1]]
                else:
                    femur_plane = [F_points[2], F_points[1], F_points[0]]

                B = np.array(femur_plane[1])
                C = np.array(femur_plane[2])
                distance1 = utils.distance_3d(B, C)
                scale1 = 107 / distance1  # 107 mm is the reference distance
                MDC = utils.find_new_point_location(
                    femur_dict["MDC"]["Plane"], femur_plane, femur_dict["MDC"]["C"]
                )
                MPC = utils.find_new_point_location(
                    femur_dict["MPC"]["Plane"], femur_plane, femur_dict["MPC"]["C"]
                )
                LDC = utils.find_new_point_location(
                    femur_dict["LDC"]["Plane"], femur_plane, femur_dict["LDC"]["C"]
                )
                LPC = utils.find_new_point_location(
                    femur_dict["LPC"]["Plane"], femur_plane, femur_dict["LPC"]["C"]
                )
                FC = utils.find_new_point_location(
                    femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
                )

                distace_Mdc = utils.distance_3d(MDC, tip) / scale1
                distace_Mpc = utils.distance_3d(MPC, tip) / scale1
                distace_ldc = utils.distance_3d(LDC, tip) / scale1
                distace_lpc = utils.distance_3d(LPC, tip) / scale1
                distace_fc = utils.distance_3d(FC, tip) / scale1
                DISTANCE_CONSTANT = 0.5 / scale1
                print(f" MDC {MDC} {tip} distace_Mdc{distace_Mdc}  scale1 {scale1}")

                def round_to_two_decimals(value):
                    return round(value, 2)

                # Compare distances with the rounded constant
                rounded_distance_constant = round_to_two_decimals(DISTANCE_CONSTANT)
                if round_to_two_decimals(distace_Mdc) <= rounded_distance_constant:
                    print(
                        f"distance {distace_Mdc} MDC point is valid generating green Flag"
                    )
                    await send_tuple(
                        websocket, (round_to_two_decimals(distace_Mdc), 0, 0, 0)
                    )
                elif round_to_two_decimals(distace_Mpc) <= rounded_distance_constant:
                    print(
                        f"distance {distace_Mpc} MPC point is valid generating green Flag"
                    )
                    await send_tuple(
                        websocket, (round_to_two_decimals(distace_Mpc), 0, 0, 0)
                    )
                elif round_to_two_decimals(distace_ldc) <= rounded_distance_constant:
                    print(
                        f"distance {distace_ldc} LDC point is valid generating green Flag"
                    )
                    await send_tuple(
                        websocket, (round_to_two_decimals(distace_ldc), 0, 0, 0)
                    )
                elif round_to_two_decimals(distace_lpc) <= rounded_distance_constant:
                    print(
                        f"distance {distace_lpc} LPC point is valid generating green Flag"
                    )
                    await send_tuple(
                        websocket, (round_to_two_decimals(distace_lpc), 0, 0, 0)
                    )
                elif round_to_two_decimals(distace_fc) <= rounded_distance_constant:
                    print(
                        f"distance {distace_fc} FC point is valid generating green Flag"
                    )
                    await send_tuple(
                        websocket, (round_to_two_decimals(distace_fc), 0, 0, 0)
                    )
                else:
                    print(f"invalid distance red Flag Generated")
                    await send_tuple(
                        websocket, (round_to_two_decimals(distace_Mdc), 0, 0, 0)
                    )

            await asyncio.sleep(0.02)
        except Exception as err:
            print(f"{__file__} e {err}")
            return


async def ml_size_acquisition(marking, websocket):
    global framecount, variable_dict
    framecount = 0
    try:
        # Load Transformation Plane (TP) variables
        pickle_path_hipCenter = os.path.join(
            current_dir, "..", "..", "registration_data", "femur_variables.pickle"
        )
        with open(pickle_path_hipCenter, "rb") as handle:
            femur_dict = pickle.load(handle)
        print("✅ Femur HC variables loaded successfully")

    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    point_reg = ["M", "L"]

    while framecount < len(point_reg):
        try:
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9  and len(detections_left) == 9:
                    break
                await asyncio.sleep(0.2)

            # Sort detections
            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            detections_right.sort(key=lambda x: x[1], reverse=True)
            detections_left.sort(key=lambda x: x[1], reverse=True)
            first_three_right, first_three_left = (
                detections_right[:3],
                detections_left[:3],
            )
            next_three_right, next_three_left = (
                detections_right[3:6],
                detections_left[3:6],
            )

            # Process the detections and compute the 3D points
            Pointer_tracker = TriangleTracker(
                tuple(first_three_right),
                tuple(first_three_left),
                rec_img_right,
                rec_img_left,
            )
            Pointer_tracker.find_depth()
            pointer_tip_gcs = Pointer_tracker.getStylusPoint()

            Femur = TriangleTracker(
                tuple(next_three_right),
                tuple(next_three_left),
                rec_img_right,
                rec_img_left,
            )
            Femur.find_depth()
            F_points = Femur.getLEDcordinates()

            # Arrange femur plane points
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )

            pointRegistered = utils.find_new_point_location(
                femur_plane, femur_dict["FC"]["Plane"], pointer_tip_gcs
            )
            variable_dict[point_reg[framecount]] = {
                "C": pointRegistered,
                "Plane": femur_dict["FC"]["Plane"],
            }
            utils.play_notification_sound()

            framecount += 1  # Move to the next point
            await asyncio.sleep(1)  # Delay

        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.5)

    print("🎯 All points registered. Saving data...")

    pickle_path_femure = os.path.join(
        current_dir, "..", "..", "registration_data", "femure_ML_Size_variables.pickle"
    )
    os.makedirs(os.path.dirname(pickle_path_femure), exist_ok=True)

    with open(pickle_path_femure, "wb") as handle:
        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

    print("✅ Pickle dump successful")
    try:
        await send_tuple(websocket, ("exit", 4.0, 5.6, 7.3))
    except Exception as e:
        print(f"⚠️ WebSocket send failed: {e}")


async def revtibia_im_canal_ream(marking, websocket):
    global framecount, variable_dict
    framecount = 0

    try:
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Load Transformation Plane (TP) variables
        pickle_path_hipCenter = os.path.join(
            current_dir, "..", "..", "registration_data", "femur_variables.pickle"
        )
        with open(pickle_path_hipCenter, "rb") as handle:
            femur_dict = pickle.load(handle)
        print("✅ Femur HC variables loaded successfully")

        pickle_path_femure = os.path.join(
            current_dir,
            "..",
            "..",
            "registration_data",
            "femure_ML_Size_variables.pickle",
        )

        pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
        with open(pickle_path_tibia_tc, "rb") as tibia_tc_handle:
            tibia_tc = pickle.load(tibia_tc_handle)
        pickle_path_tibia = os.path.join(base_dir, "tibia_variables.pickle")
        with open(pickle_path_tibia, "rb") as tibia_handle:
            tibia_dict = pickle.load(tibia_handle)

        with open(pickle_path_femure, "rb") as handle:
            femure_size_dict = pickle.load(handle)
        print("✅ ML Sizevariables loaded successfully")

    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    while True:
        try:
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 6 and len(detections_left) == 6:
                    await asyncio.sleep(0.2)
                    break
                await asyncio.sleep(0.1)

            # Sort detections
            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right, first_three_left = (
                detections_right[:3],
                detections_left[:3],
            )
            next_three_right, next_three_left = (
                detections_right[:-3],
                detections_left[:-3],
            )

            # Process the detections and compute the 3D points
            Femure = TriangleTracker(
                tuple(first_three_right),
                tuple(first_three_left),
                rec_img_right,
                rec_img_left,
            )
            Femure.find_depth()
            F_points = Femure.getLEDcordinates()
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )
            Tibia = TriangleTracker(
                tuple(next_three_right),
                tuple(next_three_left),
                rec_img_right,
                rec_img_left,
            )
            Tibia.find_depth()
            T_points = Tibia.getLEDcordinates()
            tibia_plane = (
                [T_points[2], T_points[0], T_points[1]]
                if T_points[1][1] >= T_points[0][1]
                else [T_points[2], T_points[1], T_points[0]]
            )
            # pointA = utils.find_new_point_location(femur_plane, variable_dict['M']['Plane'],
            #                                        variable_dict['C'])
            # pointB = utils.find_new_point_location(femur_plane, variable_dict['M']['Plane'],
            #                                        variable_dict['C'])

            NewFC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            tibia_plane[2] = NewFC
            NewTC = utils.find_new_point_location(
                tibia_tc["TC"]["Plane"], tibia_plane, tibia_tc["TC"]["C"]
            )
            tibia_plane[2] = NewTC
            MM = utils.find_new_point_location(
                tibia_dict["MM"]["Plane"], tibia_plane, tibia_dict["MM"]["C"]
            )
            LM = utils.find_new_point_location(
                tibia_dict["LM"]["Plane"], tibia_plane, tibia_dict["LM"]["C"]
            )

            ANC = (np.array(MM) + np.array(LM)) / 2

            B = np.array(femur_plane[1])
            C = np.array(femur_plane[2])
            mid = (B + C) / 2
            # distance1 = utils.distance_3d(mid, femur_plane[0])
            # scale1 = 107 / distance1  # 107 mm is the reference distance
            # distance2 = utils.distance_3d(pointA, pointB)
            # femure_size = abs(distance2) * scale1

            vector1 = np.array(ANC) - np.array(NewTC)
            vector2 = np.array(mid) - np.array(femur_plane[0])

            # Compute dot product
            dot_product = np.dot(vector1, vector2)

            # Compute norms (magnitudes)
            norm_v1 = np.linalg.norm(vector1)
            norm_v2 = np.linalg.norm(vector2)

            # Compute angle in radians
            angle_radians = np.arccos(dot_product / (norm_v1 * norm_v2))

            # Convert to degrees
            angle_degrees = np.degrees(angle_radians)
            if angle_degrees <= 3:
                message = "Noconflict_message"
            else:
                message = "conflict_message"
            await send_tuple(websocket, (message, 2, 2, 2))
            await asyncio.sleep(1)  # Delay
            print(f" angle_degrees {angle_degrees}  message{message}")
        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.5)


async def revfemur_im_canal_ream(marking, websocket):
    global framecount, variable_dict
    framecount = 0

    try:

        pickle_path_hipCenter = os.path.join(
            current_dir, "..", "..", "registration_data", "femur_variables.pickle"
        )
        with open(pickle_path_hipCenter, "rb") as handle:
            femur_dict = pickle.load(handle)
        print("✅ Femur HC variables loaded successfully")

        pickle_path_femur_hc = os.path.join(current_dir,"..", "..", "registration_data", "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")
        except:
            print("Error loading femur variables")

    except (FileNotFoundError, pickle.PickleError) as e:
        print(f"❌ Error loading TP variables: {e}")
        return {}

    while True:
        try:
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 6 and len(detections_left) == 6:
                    break
                await asyncio.sleep(0.2)

            # Sort detections
            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right, first_three_left = (
                detections_right[:3],
                detections_left[:3],
            )
            next_three_right, next_three_left = (
                detections_right[:-3],
                detections_left[:-3],
            )

            # Process the detections and compute the 3D points
            Femure = TriangleTracker(
                first_three_right,
                first_three_left,
                rec_img_right,
                rec_img_left,
                mode='F'
            )
            Femure.find_depth()
            F_points = Femure.getLEDcordinates()
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )

            # pointA = utils.find_new_point_location(femur_plane, variable_dict['M']['Plane'],
            #                                        variable_dict['C'])
            # pointB = utils.find_new_point_location(femur_plane, variable_dict['M']['Plane'],
            #                                        variable_dict['C'])

            NewFC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )


            B = np.array(femur_plane[1])
            C = np.array(femur_plane[2])
            mid = (B + C) / 2
            # distance1 = utils.distance_3d(mid, femur_plane[0])
            # scale1 = 107 / distance1  # 107 mm is the reference distance
            # distance2 = utils.distance_3d(pointA, pointB)
            # femure_size = abs(distance2) * scale1

            vector1 = np.array(HC) - np.array(NewFC)
            vector2 = np.array(mid) - np.array(femur_plane[0])

            # Compute dot product
            dot_product = np.dot(vector1, vector2)

            # Compute norms (magnitudes)
            norm_v1 = np.linalg.norm(vector1)
            norm_v2 = np.linalg.norm(vector2)


            # Compute angle in radians
            angle_radians = np.arccos(dot_product / (norm_v1 * norm_v2))

            # Convert to degrees
            angle_degrees = np.degrees(angle_radians)
            if angle_degrees <= 3:
                message = "Noconflict_message"
            else:
                message = "conflict_message"
            await send_tuple(websocket, (message, 2, 2, 2))
            await asyncio.sleep(1)  # Delay
            print(f" angle_degrees {angle_degrees}  message{message}")
        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.5)

async def revrevisionKneeTibiaCut(marking, websocket):
    def custom_sort(points):
        # Step 1: Find the point with the highest y-value
        highest_y_point = max(points, key=lambda p: p[1])  # Use max() for highest y

        # Step 2: Remove it from the list and sort the remaining points based on x
        remaining_points = [p for p in points if p != highest_y_point]
        sorted_remaining = sorted(remaining_points, key=lambda p: p[0])

        # Step 3: Return sorted list in order: highest_y_point, then x-sorted points
        return [highest_y_point] + sorted_remaining

    base_dir = os.path.join(current_dir, "..", "..", "registration_data")
    # File paths and variable names
    files_to_load = {
        "hip_center": os.path.join(base_dir, "hip_center.pickle"),
        "femur_variables": os.path.join(base_dir, "femur_variables.pickle"),
        "tibia_variables_TC": os.path.join(base_dir, "tibia_variables_TC.pickle"),
        "tibia_variables": os.path.join(base_dir, "tibia_variables.pickle"),
    }
    # femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")
    femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    tibia_dict_tc = load_pickle_file(
        files_to_load["tibia_variables_TC"], "Tibia TC variables"
    )
    tibia_dict = load_pickle_file(files_to_load["tibia_variables"], "Tibia variables")
    # Construct the correct path to the registration_data folder (up one level)
    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloudtibia.pickle"
    )
    try:
        with open(freePoint_knee_uni, "rb") as handle:
            points_cloud = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")

    camera_reference = np.array(
        [
            tibia_dict_tc["TC"]["C"],
            tibia_dict["MC"]["C"],
            tibia_dict["MM"]["C"],
            tibia_dict["LM"]["C"],
        ]
    )
    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
    obj_points_Tibia = np.array(
        [
            [
                -64.6756134,
                -2.9603553,
                -17.7387486,
            ],  # TC [-60.84293912 -17.69269143   2.21923788]
            [-64.2572708, -4.0632067, -14.2518415],  # MC
            [-0.7417173, -8.5226688, -13.8344755],  # MM
            [-0.2158346, -9.4181175, -22.6858425],  # LM
        ]
    )
    transformation_matrix = calculate_transformation_matrix_with_scaling(
        camera_reference, obj_points_Tibia
    )

    points_cloud = np.array(points_cloud)

    new_points = np.array(
        [
            tibia_dict_tc["TC"]["C"],
            tibia_dict["MC"]["C"],
            tibia_dict["LC"]["C"],
            # tibia_dict['MM']['C'],
            # tibia_dict['LM']['C']
        ]
    )

    while True:
        try:
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    await asyncio.sleep(0.2)
                    break
                await asyncio.sleep(0.1)
            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            last_three_right = detections_right[-3:]
            last_three_left = detections_left[-3:]

            femure = TriangleTracker(
                first_three_right,
               first_three_left,
                rec_img_right,
                rec_img_left,
                mode='F'
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            next_three_right = custom_sort(detections_right[3:6])
            next_three_left = custom_sort(detections_left[3:6])

            # Initialize the tracker with sorted points
            Pointer_traker = TriangleTracker(
                next_three_right,
                next_three_left,
                rec_img_right,
                rec_img_left,
            )
            Pointer_traker.find_depth()
            pointer_tip_gcs = Pointer_traker.getStylusPoint()
            NewFC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            tibia = TriangleTracker(
                last_three_right,
                last_three_left,
                rec_img_right,
                rec_img_left,
                mode='T'
            )
            tibia.find_depth()
            T_Points = tibia.getLEDcordinates()
            if T_Points[1][1] >= T_Points[0][1]:
                tibia_plane = [T_Points[2], T_Points[0], T_Points[1]]
            else:
                tibia_plane = [T_Points[2], T_Points[1], T_Points[0]]

            tibia_plane[2] = NewFC
            NewTC = utils.find_new_point_location(
                tibia_dict_tc["TC"]["Plane"], tibia_plane, tibia_dict_tc["TC"]["C"]
            )

            tibia_plane[2] = NewTC
            pointRegistered = utils.find_new_point_location(
                tibia_plane, tibia_dict_tc["TC"]["Plane"], pointer_tip_gcs
            )

            ANC = np.array(
                [
                    (tibia_dict["MM"]["C"][i] + tibia_dict["LM"]["C"][i]) / 2
                    for i in range(3)
                ]
            )
            # points_cloud = np.concatenate((points_cloud, new_points), axis=0)
            B = np.array(femur_plane[1])
            C = np.array(femur_plane[2])
            # distance1 = utils.distance_3d(B, C)
            v_am = np.subtract(B, C)
            distance1 = np.linalg.norm(v_am)
            scale1 = 107 / distance1  # 107 mm is the reference distance
            cutting_points_scalling = 10 / scale1

            direction_vector = tibia_dict_tc["TC"]["C"] - ANC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling

            projection_point = tibia_dict["MC"]["C"] - scaled_unit_vector
            # projection_point = tibia_dict['MC']['C']
            a, b, c = direction_vector  # Normal vector
            x0, y0, z0 = projection_point  # Point on the plane
            # Calculate 'd' using the plane equation
            d = -(a * x0 + b * y0 + c * z0)
            # Concatenate the new points to the existing points_cloud
            points_cloud = np.concatenate((points_cloud, new_points), axis=0)
            hull = ConvexHull(points_cloud)
            moving_point = np.array(pointRegistered)  #
            moving_point_distance = np.dot(moving_point, np.array([a, b, c])) + d

            def point_in_hull(point, hull):
                new_points = np.vstack([hull.points, point])
                new_hull = ConvexHull(new_points)
                return np.array_equal(new_hull.vertices, hull.vertices)

            data = f"{moving_point[0]},{-moving_point[1]},{moving_point[2]}\n"

            transformed_point = (
                np.dot(transformation_matrix[:3, :3], eval(data))
                + transformation_matrix[:3, 3]
            )
            if point_in_hull(moving_point, hull):
                startmotor = True
                if moving_point_distance >= 0:
                    print(f"moving_point_distance {moving_point_distance} startmotor")

                    ui_point = np.append(transformed_point, 0)
                if moving_point_distance <= 0.1:
                    # await send_tuple(websocket, ('YELLOW', 11, 5.6, 7.3))
                    ui_point = np.append(transformed_point, 1)
                    startmotor = False
                    print(f"moving_point_distance {moving_point_distance} stopmotor")
                await send_tuple(websocket, ui_point)
            else:
                # await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
                startmotor = False
                ui_point = np.append(transformed_point, 3)
                await send_tuple(websocket, ui_point)
                # if is_outside_convex_hull(moving_point, hull_points):
                #     print("Pointer is outside the convex hull")
                # else:
                # print("Pointer is inside or on the surface of the convex hull")
                print(f"moving_point_distance {moving_point_distance} stopmotor")
                time.sleep(0.1)
        except Exception as e:
            await asyncio.sleep(0.2)
            print(f"⚠️ WebSocket send failed: {e}")


async def revverificationTibiaCut(marking, websocket):
    try:
        await send_tuple(websocket, ("tibia-inner-page3", 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")

            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_tc = pickle.load(tibia_handle)
            # Tibia TC variables
            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_dict = pickle.load(tibia_handle)
                print(f" tibia_dict {tibia_dict}")
        except:
            print("Error loading femur variables")
        # omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    omega = utils.GetplanningInput(filename="tibia_input1")
    while True:
        try:
            if page != "revverification-proximal-tibia-cut.html":
                return
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    print(f"R {len(detections_right)}  L {len(detections_left)}")
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)
                    print(f"R {len(detections_right)}  L {len(detections_left)}")
                    continue

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])

            # Extract elements in groups of 3
            detection_groups = {
                "F": (detections_right[:3], detections_left[:3]),  # Robot
                "V": (detections_right[3:6], detections_left[3:6]),  # Femur
                "T": (detections_right[6:9], detections_left[6:9]),  # Verification Tool
                # "T": (detections_right[9:12], detections_left[9:12]),  # Tibia
            }

            # Store the results
            tracked_points = {}

            # Iterate over detection groups and process each one
            for label, (right, left) in detection_groups.items():
                tracker = (
                    TriangleTracker(right, left, rec_img_right, rec_img_left)
                    if label == "V"  # No label passed for Robot & Verification Tool
                    else TriangleTracker(
                        right, left, rec_img_right, rec_img_left, label
                    )
                    # Label passed for Femur & Tibia
                )
                tracker.find_depth()
                tracked_points[label] = tracker.getLEDcordinates()
            # Extract individual results
            # R_points = tracked_points["R"]
            F_points = tracked_points["F"]
            Tool_points = tracked_points["V"]
            T_points = tracked_points["T"]
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]
            if T_points[1][1] >= T_points[0][1]:
                tebia_Plane = [T_points[2], T_points[0], T_points[1]]
            else:
                tebia_Plane = [T_points[2], T_points[1], T_points[0]]

            B = np.array(tebia_Plane[1])
            C = np.array(tebia_Plane[0])
            distance1 = utils.distance_3d(B, C)
            scale = 107 / distance1  # 107 mm is the reference distance

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            tebia_Plane[2] = FC
            TC = utils.find_new_point_location(
                tibia_tc["TC"]["Plane"], tebia_Plane, tibia_tc["TC"]["C"]
            )

            tebia_Plane[2] = TC
            # print(f'  tebia_Plane  {tebia_Plane}')
            MC = utils.find_new_point_location(
                tibia_dict["MC"]["Plane"], tebia_Plane, tibia_dict["MC"]["C"]
            )
            LC = utils.find_new_point_location(
                tibia_dict["LC"]["Plane"], tebia_Plane, tibia_dict["LC"]["C"]
            )
            MM = utils.find_new_point_location(
                tibia_dict["MM"]["Plane"], tebia_Plane, tibia_dict["MM"]["C"]
            )
            LM = utils.find_new_point_location(
                tibia_dict["LM"]["Plane"], tebia_Plane, tibia_dict["LM"]["C"]
            )
            ANC = (np.array(MM) + np.array(LM)) / 2

            Mid_Tool_points = (np.array(Tool_points[1]) + np.array(Tool_points[2])) / 2

            # Step 2: y vector calculation
            toolTracker_y = Mid_Tool_points - Tool_points[0]

            # Step 3: x vector calculation
            toolracker_x = np.array(Tool_points[1]) - np.array(Tool_points[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            toolrackernorm_x = np.linalg.norm(toolracker_x)

            toolrackernorm_y = np.linalg.norm(toolTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            toolracker_z = np.cross(toolTracker_y, toolracker_x)

            # Step 6: FCS vectors
            x_FCS = ANC - TC
            z_FCS = LC - MC

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            def vector3_signed_angle(tracker, MC, LC):
                # print(f'*' * 100)
                # print(f'tracker {tracker}  ME {MC}  LE {LC}')
                # print(f'*' * 100)

                # Step 1: Compute midpoint between point 2 and point 1
                mid_point = (tracker[2] + tracker[1]) / 2.0

                # Step 2: Define LCS axes
                LCS_x = tracker[2] - mid_point
                LCS_y = mid_point - tracker[0]
                LCS_z = np.cross(LCS_x, LCS_y)

                # Normalize the axes
                LCS_x /= np.linalg.norm(LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)
                LCS_z /= np.linalg.norm(LCS_z)

                # Re-orthogonalize LCS_y (optional but safer)
                LCS_y = np.cross(LCS_z, LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)

                # Step 3: Build rotation matrix
                R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

                # Step 4: Translate ME and LE to LCS origin
                MC_translated = MC - mid_point
                LC_translated = LC - mid_point

                # Step 5: Apply rotation (World to Local)
                MC_local = R.T @ MC_translated
                LC_local = R.T @ LC_translated

                # Step 6: Compute MC→LC vector in LCS
                V_local = LC_local - MC_local

                # Step 7: Compute angle in LCS XZ plane
                angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
                angle_deg = int(np.degrees(angle_rad))
                if angle_deg == 0:
                    return angle_deg
                elif angle_deg < 0:
                    angle_deg += 90
                else:
                    angle_deg -= 90

                return angle_deg

            # --------------------------
            # Final angle calculation
            try:
                print(f'toolrackernorm_y  {toolrackernorm_y}')
                print(f'x_FCS  {x_FCS}')
                flexion = vector2_signed_angle_xy(toolTracker_y, x_FCS)
                varus = vector3_signed_angle(tracker=Tool_points, MC=MC, LC=LC)

                print(f"flexion  {flexion} varus {varus}")

                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error {e}")

            # Given data

            cutting_points_scalling = 50 / scale
            cutting_points = 9/ scale
            # # Compute direction vectors
            direction_vector = ANC - TC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points
            projection_point = TC + scaled_unit_vector
            print(f'projection_point  {projection_point} ')
            direction_vector2 = toolTracker_y.copy()
            unit_vector2 = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector2 = unit_vector2 * cutting_points_scalling
            projection_point2 = TC +  scaled_unit_vector2 #
            print(f'projection_point2  {projection_point2} ')
            # Compute plane normal using cross product
            normal_vector = np.cross(direction_vector, scaled_unit_vector)

            # Compute plane equation: Ax + By + Cz + D = 0
            A, B, C = normal_vector
            D = -np.dot(normal_vector, projection_point)

            # Compute distance from projection_point2 to the plane
            x1, y1, z1 = projection_point2
            distance_mm = abs(A * x1 + B * y1 + C * z1 + D) / np.linalg.norm(
                normal_vector
            )

            print(
                f"Distance between projection_point2 and the plane in mm: {distance_mm:.2f} mm"
            )

            await send_tuple(
                websocket, ("rev_tibia_verify", flexion, varus, f'{distance_mm:.2f}')
            )
            await asyncio.sleep(0.1)
        except Exception as e:
            await asyncio.sleep(0.2)
            print(f"Error {e}")

async def revverificationdistalFemure(marking, websocket):
    try:
        await send_tuple(websocket, ("tibia-inner-page3", 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")

            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_tc = pickle.load(tibia_handle)
            # Tibia TC variables
            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_dict = pickle.load(tibia_handle)
                print(f" tibia_dict {tibia_dict}")
        except:
            print("Error loading femur variables")
        # omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    omega = utils.GetplanningInput(filename="tibia_input1")
    while True:
        try:
            if page != "revverification-proximal-tibia-cut.html":
                return
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    print(f"R {len(detections_right)}  L {len(detections_left)}")
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)
                    print(f"R {len(detections_right)}  L {len(detections_left)}")
                    continue

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])

            # Extract elements in groups of 3
            detection_groups = {
                "F": (detections_right[:3], detections_left[:3]),  # Robot
                "V": (detections_right[3:6], detections_left[3:6]),  # Femur
                # "T": (detections_right[6:9], detections_left[6:9]),  # Verification Tool
                # "T": (detections_right[9:12], detections_left[9:12]),  # Tibia
            }

            # Store the results
            tracked_points = {}

            # Iterate over detection groups and process each one
            for label, (right, left) in detection_groups.items():
                tracker = (
                    TriangleTracker(right, left, rec_img_right, rec_img_left)
                    if label == "V"  # No label passed for Robot & Verification Tool
                    else TriangleTracker(
                        right, left, rec_img_right, rec_img_left, label
                    )
                    # Label passed for Femur & Tibia
                )
                tracker.find_depth()
                tracked_points[label] = tracker.getLEDcordinates()
            # Extract individual results
            # R_points = tracked_points["R"]
            F_points = tracked_points["F"]
            Tool_points = tracked_points["V"]
            # T_points = tracked_points["T"]
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            B = np.array(femur_plane[1])
            C = np.array(femur_plane[0])
            distance1 = utils.distance_3d(B, C)
            scale = 107 / distance1  # 107 mm is the reference distance

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )


            Mid_Tool_points = (np.array(Tool_points[1]) + np.array(Tool_points[2])) / 2

            # Step 2: y vector calculation
            toolTracker_y = Mid_Tool_points - Tool_points[0]

            # Step 3: x vector calculation
            toolracker_x = np.array(Tool_points[1]) - np.array(Tool_points[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            toolrackernorm_x = np.linalg.norm(toolracker_x)

            toolrackernorm_y = np.linalg.norm(toolTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            toolracker_z = np.cross(toolTracker_y, toolracker_x)

            x_FCS = FC - HC
            z_FCS = LE - ME

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            def vector3_signed_angle(tracker, ME, LE):
                # print(f'*' * 100)
                # print(f'tracker {tracker}  ME {MC}  LE {LC}')
                # print(f'*' * 100)

                # Step 1: Compute midpoint between point 2 and point 1
                mid_point = (tracker[2] + tracker[1]) / 2.0

                # Step 2: Define LCS axes
                LCS_x = tracker[2] - mid_point
                LCS_y = mid_point - tracker[0]
                LCS_z = np.cross(LCS_x, LCS_y)

                # Normalize the axes
                LCS_x /= np.linalg.norm(LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)
                LCS_z /= np.linalg.norm(LCS_z)

                # Re-orthogonalize LCS_y (optional but safer)
                LCS_y = np.cross(LCS_z, LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)

                # Step 3: Build rotation matrix
                R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

                # Step 4: Translate ME and LE to LCS origin
                MC_translated = ME - mid_point
                LC_translated = LE - mid_point

                # Step 5: Apply rotation (World to Local)
                ME_local = R.T @ MC_translated
                LE_local = R.T @ LC_translated

                # Step 6: Compute MC→LC vector in LCS
                V_local = LE_local - ME_local

                # Step 7: Compute angle in LCS XZ plane
                angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
                angle_deg = int(np.degrees(angle_rad))
                if angle_deg == 0:
                    return angle_deg
                elif angle_deg < 0:
                    angle_deg += 90
                else:
                    angle_deg -= 90

                return angle_deg

            # --------------------------
            # Final angle calculation
            try:
                print(f'toolrackernorm_y  {toolrackernorm_y}')
                print(f'x_FCS  {x_FCS}')
                flexion = vector2_signed_angle_xy(toolTracker_y, x_FCS)
                varus = vector3_signed_angle(tracker=Tool_points, ME=ME, LE=LE)

                print(f"flexion  {flexion} varus {varus}")

                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error {e}")

            # Given data

            cutting_points_scalling = 50 / scale
            cutting_points = 9/ scale
            # # Compute direction vectors
            direction_vector = x_FCS.copy()
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points
            projection_point = FC + scaled_unit_vector
            print(f'projection_point  {projection_point} ')
            direction_vector2 = toolTracker_y.copy()
            unit_vector2 = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector2 = unit_vector2 * cutting_points_scalling
            projection_point2 = FC +  scaled_unit_vector2 #
            print(f'projection_point2  {projection_point2} ')
            # Compute plane normal using cross product
            normal_vector = np.cross(direction_vector, scaled_unit_vector)

            # Compute plane equation: Ax + By + Cz + D = 0
            A, B, C = normal_vector
            D = -np.dot(normal_vector, projection_point)

            # Compute distance from projection_point2 to the plane
            x1, y1, z1 = projection_point2
            distance_mm = abs(A * x1 + B * y1 + C * z1 + D) / np.linalg.norm(
                normal_vector
            )

            print(
                f"Distance between projection_point2 and the plane in mm: {distance_mm:.2f} mm"
            )

            await send_tuple(
                websocket, ("rev_femur_verify", flexion, varus, f'{distance_mm:.2f}')
            )
            await asyncio.sleep(0.1)
        except Exception as e:
            await asyncio.sleep(0.2)
            print(f"Error {e}")



async def revDistalFemurCut(marking, websocket):
    global condition
    if not USE_BURR:
        return;


    # from scipy.spatial import ConvexHull
    def load_pickle_file(file_path, var_name):
        """Load a pickle file and handle exceptions."""
        try:
            with open(file_path, "rb") as handle:
                data = pickle.load(handle)
            # print(f"{var_name} loaded successfully")
            return data
        except FileNotFoundError:
            print(f"Error: File not found - {file_path}")
        except pickle.UnpicklingError:
            print(f"Error: Unable to unpickle data from - {file_path}")
        except Exception as e:
            print(f"Error loading {var_name}: {e}")
        return None
        # Base directory

    base_dir = os.path.join(current_dir, "..", "..", "registration_data")

    # File paths and variable names
    files_to_load = {
        "hip_center": os.path.join(base_dir, "hip_center.pickle"),
        # "femur_variables": os.path.join(base_dir, 'femur_variables.pickle'),
        # "tibia_variables_TC": os.path.join(base_dir, 'tibia_variables_TC.pickle'),
    }
    # femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")

    # Construct the correct path to the registration_data folder (up one level)
    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloud.pickle"
    )
    try:
        with open(freePoint_knee_uni, "rb") as handle:
            points_cloud = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")
    # Construct the correct path to the registration_data folder (up one level)
    pickle_path_hipCenter = os.path.join(
        current_dir, "..", "..", "registration_data", "femur_variables.pickle"
    )
    try:
        with open(pickle_path_hipCenter, "rb") as handle:
            femure_dict = pickle.load(handle)
        print("Femur HC variables loaded successfully")
    except:
        print("Error loading HC variables")

    points_cloud = np.array(points_cloud)
    new_points = np.array(
        [
            femure_dict['FC']['C'],
            femure_dict['LPC']['C'],
            femure_dict['MPC']['C'],
            femure_dict["MDC"]["C"],
            femure_dict["MPC"]["C"],
            femure_dict["ME"]["C"],
            femure_dict["LE"]["C"],
        ]
    )
    points_cloud = np.concatenate((points_cloud, new_points), axis=0)

    camera_reference = np.array(
        [
            femure_dict["FC"]["C"],
            femure_dict["MDC"]["C"],
            femure_dict["MPC"]["C"],
            femure_dict["ME"]["C"],
        ]
    )
    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
    obj_points_Femure = np.array(
        [
            [-52.2903404, -3.131314, -11.5643253],  # FC
            [-51.2940788, -3.5388589, -8.9086733],  # MDC
            [-52.9359016, -6.9525371, -8.3687782],  # MPC
            [-54.2269897, -3.982173, -6.5660419],  # ME
        ]
    )
    transformation_matrix = calculate_transformation_matrix_with_scaling(
        camera_reference, obj_points_Femure
    )

    startmotor = False
    while True:
        try:
            if page != "revrevision-knee-burrFemur.html":
                return
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    await asyncio.sleep(0.2)
                    break
                elif len(detections_right) == 6 and len(detections_left) == 6:
                    stop_motor()
                    await asyncio.sleep(0.2)
                else:

                    stop_motor()
                    await asyncio.sleep(0.2)
                    await websocket.send_text('no detection')

                if page != "revrevision-knee-burrFemur.html":
                    # stop_motor()
                    return
            # Step 1: Sort by X-coordinate
            detections_right.sort(key=lambda x: x[0])
            detections_left.sort(key=lambda x: x[0])
            # Step 2: Extract first three elements
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            # Remove extracted elements
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right.sort(key=lambda x: x[1], reverse=True)
            detections_left.sort(key=lambda x: x[1], reverse=True)

            # Step 4: Extract next three elements
            next_three_right = detections_right[:3]
            next_three_left = detections_left[:3]

            # Remove extracted elements to ensure mutual exclusivity
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            # Step 5: Extract last three elements safely
            last_three_right = detections_right[-3:]
            last_three_left = detections_left[-3:]
            femure = TriangleTracker(
                first_three_right,
                first_three_left,
                rec_img_right,
                rec_img_left,
                mode="F",
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = np.array(
                    [F_points[2], F_points[0], F_points[1]]
                )  # Proper order of points
            else:
                femur_plane = np.array(
                    [F_points[2], F_points[1], F_points[0]]
                )  # Proper order of points
            Pointer_traker = TriangleTracker(
                next_three_right, next_three_left, rec_img_right, rec_img_left
            )
            Pointer_traker.find_depth()
            P_point = Pointer_traker.getLEDcordinates()
            pointer_tip_gcs = Pointer_traker.getStylusPoint()
            direction_vector = HC - femure_dict["FC"]["C"]
            tip_point = calculate_contact_point(
                led1=P_point[0],
                led2=P_point[1],
                led3=P_point[2],
                surface_normal=direction_vector,
                point=pointer_tip_gcs,
                radius=6.28,
            )
            #
            pointRegistered = utils.find_new_point_location(
                old_plane_points=femur_plane,
                new_plane_points=femure_dict["FC"]["Plane"],
                old_marked_point=pointer_tip_gcs,
            )

            a, b, c = direction_vector  # Normal vector

            B = np.array(femur_plane[2])
            C = np.array(femur_plane[1])
            distance1 = utils.distance_3d(B, C)
            scale1 = 107 / distance1  # 107 mm is the reference distance
            cutting_points_scalling = 15 / scale1

            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling
            projection_point = femure_dict["MDC"]["C"] + scaled_unit_vector

            x0, y0, z0 = projection_point  # Point on the plane
            # Calculate 'd' using the plane equation
            d = -(a * x0 + b * y0 + c * z0)
            moving_point = np.array(
                pointRegistered
            )  # Replace with your actual moving point
            # Concatenate the projection point to the points_cloud
            points_cloud = np.concatenate((points_cloud, HC[np.newaxis, :]), axis=0)
            hull = ConvexHull(points_cloud)
            moving_point_distance = np.dot(moving_point, np.array([a, b, c])) + d

            data = f"{moving_point[0]},{-moving_point[1]},{moving_point[2]}\n"

            transformed_point = (
                    np.dot(transformation_matrix[:3, :3], eval(data))
                    + transformation_matrix[:3, 3]
            )


            def point_in_hull(point, hull):
                new_points = np.vstack([hull.points, point])
                new_hull = ConvexHull(new_points)
                return np.array_equal(new_hull.vertices, hull.vertices)

            if point_in_hull(moving_point, hull):
                startmotor = True
                if moving_point_distance <= 0:
                    condition = 1
                    print(f"moving_point_distance {moving_point_distance} start motor")
                    ui_point = np.append(transformed_point, 0)
                    start_motor()


                if moving_point_distance >= 0.1:
                    startmotor = False
                    print(f"moving_point_distance {moving_point_distance} stop motor")
                    condition = 0
                    stop_motor()
                    ui_point = np.append(transformed_point, 1)
                await send_tuple(websocket, ui_point)
            else:
                condition = 2
                startmotor = False
                # if is_outside_convex_hull(moving_point, hull_points):
                #     print("Pointer is outside the convex hull")
                # else:
                # print("Pointer is inside or on the surface of the convex hull")
                ui_point = np.append(transformed_point, 3)
                await send_tuple(websocket, ui_point)
                print(f"moving_point_distance {moving_point_distance} stop motor")
                stop_motor()
                await asyncio.sleep(0.1)
        except Exception as e:
            await asyncio.sleep(0.2)
            print(f"Error {e}")

async def revkneeFree_point_collection_femur(marking, websocket):
    global framecount, variable_dict
    points_cloud = []
    framecount = 0
    base_dir = os.path.join(current_dir, "..", "..", "registration_data")
    # File paths and variable names
    files_to_load = {
        "hip_center": os.path.join(base_dir, "hip_center.pickle"),
        "femur_variables": os.path.join(base_dir, "femur_variables.pickle"),
        # "tibia_variables_TC": os.path.join(base_dir, 'tibia_variables_TC.pickle'),
        # "tibia_variables": os.path.join(base_dir, 'tibia_variables.pickle'),
    }
    femur_dict = load_pickle_file(files_to_load["femur_variables"], "Femur variables")
    HC = load_pickle_file(files_to_load["hip_center"], "Femur HC variables")
    print(f"femur_dict {femur_dict}")
    camera_reference = np.array(
        [
            HC,
            femur_dict["FC"]["C"],
            femur_dict["MDC"]["C"],
            femur_dict["MPC"]["C"],
            femur_dict["ME"]["C"],
        ]
    )
    print(f' camera_reference  {camera_reference}')
    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
    obj_points_uniFemure = np.array(
        [
            [
                -102.9053543979592,
                -14.204959734693874,
                -8.730872840816328,
            ],  # Hip Center in STL model
            [-52.2903404, -3.131314, -11.5643253],  # FC
            [-51.2940788, -3.5388589, -8.9086733],  # MDC
            [-52.9359016, -6.9525371, -8.3687782],  # MPC
            [-54.2269897, -3.982173, -6.5660419],  # ME
        ]
    )
    # for i in obj_points_uniFemure:
    #     await send_tuple(websocket, i)
    #     await asyncio.sleep(1)

    # Calculate the transformation matrix based on the camera reference
    transformation_matrix = calculate_transformation_matrix_with_scaling(
        camera_reference, obj_points_uniFemure
    )
    while framecount < 500:
        try:
            if page != "revFree_point_collection_femur.html":
                return
            while True:
                latest_frame = await marking.get_latest_frame()
                if not latest_frame:
                    return

                rec_img_left, rec_img_right = latest_frame
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)

                if len(detections_right) == 9 and len(detections_left) == 9:
                    break  # Exit loop when both have exactly 9 detections
                print(f"{len(detections_right)} {len(detections_left)} ")
                await asyncio.sleep(0.02)


            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            next_three_right = detections_right[:3]
            next_three_left = detections_left[:3]
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]
            detections_right = sorted(
                detections_right, key=lambda x: x[1], reverse=True
            )
            detections_left = sorted(
                detections_left, key=lambda x: x[1], reverse=True
            )
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            femure = TriangleTracker(
                next_three_right, next_three_left, rec_img_right, rec_img_left
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()

            # print(f'Pointer_traker {p_point}')
            Pointer_traker = TriangleTracker(
                first_three_right,
                first_three_left,
                rec_img_right,
                rec_img_left,
                mode="F",
            )
            Pointer_traker.find_depth()
            pointer_tip_gcs = Pointer_traker.getStylusPoint()
            print(f'F_points  {F_points} ')
            # Arrange femur plane points
            femur_plane = (
                [F_points[2], F_points[0], F_points[1]]
                if F_points[1][1] >= F_points[0][1]
                else [F_points[2], F_points[1], F_points[0]]
            )
            pointRegistered = utils.find_new_point_location(
                old_plane_points=femur_plane,
                new_plane_points=femur_dict["FC"]["Plane"],
                old_marked_point=pointer_tip_gcs,
            )
            points_cloud.append(pointRegistered)
            data = f"{pointRegistered[0]},{pointRegistered[1]},{pointRegistered[2]}\n"
            # data = f"{pointRegistered[0]},{pointRegistered[1]},{pointRegistered[2]},{condition}\n"
            point = np.array(eval(data), dtype=np.float32)
            point = [
                point[0],
                -point[1],
                point[2],
            ]  # Adjust Z axis orientation if needed
            transformed_point = (
                    np.dot(transformation_matrix[:3, :3], point)
                    + transformation_matrix[:3, 3]
            )
            try:
                await send_tuple(websocket, transformed_point)
            except Exception as e:
                print(f"⚠️ WebSocket send failed: {e}")
            # utils.play_notification_sound()
            framecount += 1  # Move to the next point
            await asyncio.sleep(0.1)  # Delay
            print(f"framecount  {framecount} pointRegistered {pointRegistered} transformed_point {transformed_point}")
        except Exception as e:
            print(f"❌ Unexpected error in FemureMarking: {e}")
            await asyncio.sleep(0.1)

    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloud.pickle"
    )
    try:
        with open(freePoint_knee_uni, "wb") as handle:
            pickle.dump(
                np.array(points_cloud), handle, protocol=pickle.HIGHEST_PROTOCOL
            )
        print("Pickle dump successful")
    except:
        print("Error loading HC variables")
    framecount = 0
    variable_dict = {}
    try:
        # await websocket.send("exit")
        await send_tuple(websocket,tuple(('exit',0,0,0)))
    except Exception as e:
        print(f"⚠️ WebSocket send failed: {e}")
    utils.play_notification_sound()
    return


async def revFree_point_collectionTibia(marking, websocket):
    framecount = 0
    variable_dict = {}

    # Base directory
    base_dir = os.path.join(current_dir, "..", "..", "registration_data")

    files_to_load = {
        # "hip_center": os.path.join(base_dir, 'hip_center.pickle'),
        "femur_variables": os.path.join(base_dir, "femur_variables.pickle"),
        "tibia_variables": os.path.join(base_dir, "tibia_variables.pickle"),
        "tibia_variables_TC": os.path.join(base_dir, "tibia_variables_TC.pickle"),
    }

    femur_dict = load_pickle_file(
        files_to_load["femur_variables"], "femur variables variables"
    )
    tibia_dict_tc = load_pickle_file(
        files_to_load["tibia_variables_TC"], "Tibia TC variables"
    )
    tibia_dict = load_pickle_file(files_to_load["tibia_variables"], "Tibia variables")

    stFrameInfo = MV_FRAME_OUT_INFO_EX()
    memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
    # print(f'tibia_dict_tc {tibia_dict_tc}')
    camera_reference = np.array(
        [
            tibia_dict_tc["TC"]["C"],
            tibia_dict["MC"]["C"],
            tibia_dict["MM"]["C"],
            tibia_dict["LM"]["C"],
        ]
    )
    print(f'camera_reference {camera_reference}')
    camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
    obj_points_uniTibia = np.array(
        [
            [
                -64.6756134,
                -2.9603553,
                -17.7387486,
            ],  # TC [-60.84293912 -17.69269143   2.21923788]
            [-64.2572708, -4.0632067, -14.2518415],  # MC
            [-0.7417173, -8.5226688, -13.8344755],  # MM
            [-0.2158346, -9.4181175, -22.6858425],  # LM
        ]
    )
    # # obj_points_uniTibia[:, [1, 2]] = obj_points_uniTibia[:, [2, 1]]
    # for i in obj_points_uniTibia:
    #     await send_tuple(websocket, i)
    transformation_matrix = calculate_transformation_matrix_with_scaling(
        camera_reference, obj_points_uniTibia
    )

    points_cloud = []
    while framecount < 500:
        if page != "revFree_point_collectionTibia.html":
            return
        try:
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                # Wait until at least 9 detections are available

                if len(detections_right) == 9 and len(detections_left) == 9:
                    await asyncio.sleep(0.1)
                    break
                else:
                    try:
                        await asyncio.sleep(0.1)
                        await websocket.send_text("null")
                    except Exception as e:
                        print(f"⚠️ WebSocket send failed: {e}")
                    # if page != "uniFree_point_collectionTibia.html":
                    #     return
            # print(f' {len(detections_right)}   {len(detections_left)}')
            detections_right.sort(key=lambda x: x[0])
            detections_left.sort(key=lambda x: x[0])
            # Step 2: Extract first three elements
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]

            # Remove extracted elements
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right.sort(key=lambda x: x[1], reverse=True)
            detections_left.sort(key=lambda x: x[1], reverse=True)

            # Step 4: Extract next three elements
            next_three_right = detections_right[:3]
            next_three_left = detections_left[:3]

            # Remove extracted elements to ensure mutual exclusivity
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            # Step 5: Extract last three elements safely
            last_three_right = detections_right[-3:]
            last_three_left = detections_left[-3:]

            femure = TriangleTracker(
                first_three_right, first_three_left, rec_img_right, rec_img_left, mode='F'
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()
            print(f'femur_plane  {F_points}')
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]
            Pointer_traker = TriangleTracker(
                next_three_right, next_three_left, rec_img_right, rec_img_left
            )
            Pointer_traker.find_depth()
            pointer_tip_gcs = Pointer_traker.getStylusPoint()

            tibia = TriangleTracker(
                last_three_right, last_three_left, rec_img_right, rec_img_left
            )
            tibia.find_depth()
            T_points = tibia.getLEDcordinates()
            print(f'T_points  {T_points}')
            NewFC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            if T_points[1][1] >= T_points[0][1]:
                tibia_plane = [T_points[2], T_points[0], NewFC]
            else:
                tibia_plane = [T_points[2], T_points[1], NewFC]
            TC = utils.find_new_point_location(
                tibia_dict_tc["TC"]["Plane"], tibia_plane, tibia_dict_tc["TC"]["C"]
            )
            tibia_plane[2] = TC
            pointRegistered = utils.find_new_point_location(
                old_plane_points=tibia_plane,
                new_plane_points=tibia_dict_tc["TC"]["Plane"],
                old_marked_point=pointer_tip_gcs,
            )
            points_cloud.append(pointRegistered)

            data = f"{pointRegistered[0]},{-pointRegistered[1]},{pointRegistered[2]}\n"

            transformed_point = (
                    np.dot(transformation_matrix[:3, :3], eval(data))
                    + transformation_matrix[:3, 3]
            )

            try:
                await send_tuple(websocket, transformed_point)
            except Exception as e:
                print(f"⚠️ WebSocket send failed: {e}")

            framecount += 1  # Move to the next point
            await asyncio.sleep(0.1)  # Delay
        except Exception as e:
            print(f"Error {e}")
    utils.play_notification_sound()
    print("All points registered.")

    freePoint_knee_uni = os.path.join(
        current_dir, "..", "..", "registration_data", "PointCloudtibia.pickle"
    )
    try:
        with open(freePoint_knee_uni, "wb") as handle:
            pickle.dump(
                np.array(points_cloud), handle, protocol=pickle.HIGHEST_PROTOCOL
            )
        print("Pickle dump successful")
    except:
        print("Error loading HC variables")

    try:
        await websocket.send_text("exit")

    except Exception as e:
        print(f"⚠️ WebSocket send failed: {e}")


async def revfinal_cup_position(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        Hip_threePoint_path = os.path.join(
            current_dir, "..", "..", "registration_data", "hip_three_variables.pickle"
        )
        with open(Hip_threePoint_path, "rb") as handle:
            hip_dict = pickle.load(handle)
    except Exception as e:
        print(f"Error loading femur or tibia variables: {e}")
        return -1
    while True:
        try:
            # Ensure frames are available in the queue
            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame
            else:
                return
            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)
            if not (len(detections_right) == 6 and len(detections_left) == 6):
                await asyncio.sleep(0.1)
                continue

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            next_three_right = detections_right[3:]
            next_three_left = detections_left[3:]
            femure = TriangleTracker(
                tuple(next_three_right),
                tuple(next_three_left),
                rec_img_right,
                rec_img_left,
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()
            Pointer_traker = TriangleTracker(
                tuple(first_three_right),
                tuple(first_three_left),
                rec_img_right,
                rec_img_left,
            )
            Pointer_traker.find_depth()
            Remure_tracker = Pointer_traker.getLEDcordinates()

            if F_points[1][1] >= F_points[0][1]:
                Tracker_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                Tracker_plane = [F_points[2], F_points[1], F_points[0]]

            ASL = utils.find_new_point_location(
                old_plane_points=hip_dict["ASL"]["Plane"],
                new_plane_points=tuple(Tracker_plane),
                old_marked_point=hip_dict["ASL"]["C"],
            )

            ASR = utils.find_new_point_location(
                old_plane_points=hip_dict["ASR"]["Plane"],
                new_plane_points=tuple(Tracker_plane),
                old_marked_point=hip_dict["ASR"]["C"],
            )

            PSC = utils.find_new_point_location(
                old_plane_points=hip_dict["PSC"]["Plane"],
                new_plane_points=tuple(Tracker_plane),
                old_marked_point=hip_dict["PSC"]["C"],
            )
            X_axis, Y_axis, Z_axis = utils.compute_local_coordinate_system(
                ASL, ASR, PSC
            )
            X_axisRh, Y_axisRh, Z_axisRh = utils.compute_local_coordinate_system(
                Remure_tracker[0], Remure_tracker[1], Remure_tracker[2]
            )
            ANTERVERSION_angle, INCLINATION_angle = (
                utils.calculate_ANTERVERSION_INCLINATION_angles(
                    Y_axisRh, X_axis, Z_axis
                )
            )

            await send_tuple(
                websocket, ("final-cup-position-1", int(ANTERVERSION_angle), 5.0, 7.3)
            )
            await send_tuple(
                websocket, ("final-cup-position-2", int(INCLINATION_angle), 0.2, 7.3)
            )
            await asyncio.sleep(0.02)
        except Exception as err:
            print(f"{__file__} e {err}")
            return


async def AlignmentAngles_Knee(marking, websocket):
    try:
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")

        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)

        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        with open(pickle_path_femur_hc, "rb") as handle:
            HC = np.array(pickle.load(handle))
        print("Femur HC variables loaded successfully")

        # Tibia variables
        pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
        with open(pickle_path_tibia_tc, "rb") as tibia_tc_handle:
            tibia_tc = pickle.load(tibia_tc_handle)

        pickle_path_tibia = os.path.join(base_dir, "tibia_variables.pickle")
        with open(pickle_path_tibia, "rb") as tibia_handle:
            tibia_dict = pickle.load(tibia_handle)

    except Exception as e:
        print(f"Error loading femur or tibia variables: {e}")
        return -1

    result_dict = {angle: {"left": 0, "right": 0} for angle in range(-10, 150, 10)}
    while True:
        try:

            # self.frame_queue.put((rec_img_left, rec_img_right))
            latest_frame = await marking.get_latest_frame()
            if latest_frame:
                rec_img_left, rec_img_right = latest_frame
            else:
                return
            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)
            if not (len(detections_right) == 6 and len(detections_left) == 6):
                await asyncio.sleep(0.1)
                continue

            detections_right = sorted(
                detections_right, key=lambda detection: detection[0]
            )
            detections_left = sorted(
                detections_left, key=lambda detection: detection[0]
            )
            femure = TriangleTracker(
                detections_right[:3],
                detections_left[:3],
                rec_img_right,
                rec_img_left,
                mode="F",
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()

            # print(f' F_points {F_points}')

            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )
            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            tebia = TriangleTracker(
                detections_right[3:],
                detections_left[3:],
                rec_img_right,
                rec_img_left,
                mode="T",
            )
            tebia.find_depth()
            T_Points = tebia.getLEDcordinates()

            if T_Points[1][1] >= T_Points[0][1]:
                tibia_plane = [T_Points[2], T_Points[0], T_Points[1]]
            else:
                tibia_plane = [T_Points[2], T_Points[1], T_Points[0]]
            print(f"tibia_plane   {tibia_plane}   FC {FC}")
            tibia_plane[2] = FC
            TC = utils.find_new_point_location(
                tibia_tc["TC"]["Plane"], tibia_plane, tibia_tc["TC"]["C"]
            )
            tibia_plane[2] = TC
            MC = utils.find_new_point_location(
                tibia_dict["MC"]["Plane"], tibia_plane, tibia_dict["MC"]["C"]
            )
            LC = utils.find_new_point_location(
                tibia_dict["LC"]["Plane"], tibia_plane, tibia_dict["LC"]["C"]
            )
            MM = utils.find_new_point_location(
                tibia_dict["MM"]["Plane"], tibia_plane, tibia_dict["MM"]["C"]
            )
            LM = utils.find_new_point_location(
                tibia_dict["LM"]["Plane"], tibia_plane, tibia_dict["LM"]["C"]
            )
            print(f"tibia_plane 2  {tibia_plane}   LM {LM}")
            ANC = (np.array(MM) + np.array(LM)) / 2

            print(f"ANC{ANC}  TC {TC}")

            def vector2_signed_angle(A, B):
                """Compute the signed angle between vectors A and B projected onto the XZ plane."""
                # Normalize the vectors
                A_norm = A / np.linalg.norm(A)
                B_norm = B / np.linalg.norm(B)

                # Compute cross and dot products
                cross = np.cross(A_norm, B_norm)
                dot = np.dot(A_norm, B_norm)

                # Use arctan2 for a signed angle
                angle = np.degrees(
                    np.arctan2(cross[1], dot)
                )  # Using Y-component for sign

                return abs(angle)

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on the XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate the signed angle
                angle = np.degrees(
                    np.arctan2(
                        np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2],
                        # Z-component of 3D cross product
                        np.dot(AProjXY, BProjXY),
                    )
                )

                # Round to the nearest multiple of 0.5
                angle_rounded = -np.round(angle * 2) / 2
                print(f"angle_rounded   angle_rounded  angle_rounded {angle_rounded}")
                # angle_rounded = angle_rounded - 360 if angle_rounded < 360 else angle_rounded + 180
                return angle_rounded

                # return angle_rounded

            vectorF = HC - FC
            vectorT = TC - ANC
            alignment = vector2_signed_angle(vectorF, vectorT)
            ROM = vector2_signed_angle_xy(vectorF, vectorT)

            # print(f'ang {alignment}    ROM {ROM}')
            # utils.SendAnglesToFrontEnd(ROM)
            # utils.SetangletoFrontEnd(file='ROM2', data=alignment)

            B = np.array(femur_plane[1])
            C = np.array(femur_plane[2])
            mid = (B + C) / 2
            distance1 = utils.distance_2d(mid, femur_plane[0])
            scale1 = 107 / distance1  # 107 mm is the reference distance
            if -10 <= ROM <= 120:  # Corrected condition
                rom_key = int(ROM)
                if rom_key % 10 == 0:
                    # Compute distances
                    d1 = utils.distance_2d(ME, MC) * scale1
                    d2 = utils.distance_2d(LE, LC) * scale1

                    # Update result_dict
                    result_dict[rom_key] = {"left": -d2, "right": d1}

                    # Send updated data to the frontend
                result_dict = {
                    key: value
                    for key, value in result_dict.items()
                    if value["left"] != 0 and value["right"] != 0
                }

            ###################################################################################################
            # data = {0: {'left': -10, 'right': 10}, 10: {'left': -10, 'right': 10}, 20: {'left': -9, 'right': 9},
            #         30: {'left': -10, 'right': 10},
            #         40: {'left': -10, 'right': 10}, 50: {'left': -10, 'right': 10}, 60: {'left': -9, 'right': 9},
            #         70: {'left': -10, 'right': 10}, 80: {'left': -11, 'right': 11}, 90: {'left': -10, 'right': 10},
            #         100: {'left': -10, 'right': 10}, 110: {'left': 0, 'right': 0}, 120: {'left': 0, 'right': 0}}
            # result_dict = {key: value for key, value in data.items() if
            #                value['left'] != 0 and value['right'] != 0}
            ###################################################################################################
            print(f"*" * 100)
            print(f"{result_dict}")
            print(f"*" * 100)
            parent_dict = {
                "Graph_data": {
                    "filtered_results": {
                        key: value
                        for key, value in result_dict.items()
                        if value["left"] != 0 and value["right"] != 0
                    }
                },
                "angles": {"Alignement": int(alignment), "Anteversion": int(ROM)},
            }
            print(f"parent_dict {parent_dict}")
            await websocket.send_json(parent_dict)
            await asyncio.sleep(0.2)
        except Exception as err:
            print(f"{__file__} e {err}")
            return


async def primary_kneeTibiaCut(marking, websocket):
    print("Entered in Tebia procecc")
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)

        try:
            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_tc = pickle.load(tibia_handle)
                print(f" tibia_tc {tibia_tc}")
            # Tibia TC variables
            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_dict = pickle.load(tibia_handle)
                print(f" tibia_dict {tibia_dict}")

        except:
            print("Error loading femur variables")

        omega = utils.GetplanningInput(filename="tibia_input1")
    except Exception as e:
        print(f"Error {e}")
    initial_data = GetRoboticArmInitialData()
    if USE_ROBOT:
        arm = XArmAPI(arm_ip)
        arm.connect()
    while True:
        try:

            while True:
                if page != "femur-inner-page2.html":
                    return
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeanns(rec_img_right)
                detections_left = utils.find_contours_Kmeanns(rec_img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:

                    await asyncio.sleep(0.2)
                    continue

            # detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
            # detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            three_right = detections_right[0:3]
            three_left = detections_left[0:3]
            # three_right = sorted(three_right, key=lambda x: x[0])
            # three_left = sorted(three_left, key=lambda x: x[0])

            Pointer = TriangleTracker(
                three_right, three_left, rec_img_right, rec_img_left
            )
            Pointer.find_depth()
            R_points = Pointer.getLEDcordinates()

            if R_points[1][1] >= R_points[0][1]:
                tracker_plane = [R_points[2], R_points[0], R_points[1]]
            else:
                tracker_plane = [R_points[2], R_points[1], R_points[0]]
            # detections_right = detections_right[3:]
            # detections_left = detections_left[3:]

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right = detections_right[3:6]
            first_three_left = detections_left[3:6]
            next_three_right = detections_right[-3:]
            next_three_left = detections_left[-3:]
            femure = TriangleTracker(
                first_three_right, first_three_left, rec_img_right, rec_img_left, "F"
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()

            Tebia = TriangleTracker(
                next_three_right, next_three_left, rec_img_right, rec_img_left, "T"
            )
            Tebia.find_depth()
            T_points = Tebia.getLEDcordinates()
            if T_points[1][1] >= T_points[0][1]:
                tebia_Plane = [T_points[2], T_points[0], T_points[1]]
            else:
                tebia_Plane = [T_points[2], T_points[1], T_points[0]]
            # print(f'  tebia_Plane original  {tebia_Plane}')
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            new_origin = utils.find_new_point_location(
                old_plane_points=initial_data["robot_tracker_plane"],
                new_plane_points=tracker_plane,
                old_marked_point=initial_data["old_mark_point"],
            )

            # Step 1: Compute centroid
            centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
            translation_vector = new_origin - centroid

            # Step 3: Translate the plane
            Newtranslated_plane = (
                initial_data["robot_tracker_plane"] + translation_vector
            )

            robot_calib_data = os.path.join(
                current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
            )
            led_points, robot_points = load_data(file_path=robot_calib_data)

            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=initial_data["translated_plane"],
                        new_plane_points=Newtranslated_plane,
                        old_marked_point=led,
                    )
                    for led in led_points
                ]
            )
            # Swap Y and Z back to match coordinate system
            transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

            updated_model = LinearRegression()
            updated_model.fit(transformed_led_points, robot_points)
            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )

            tebia_Plane[2] = FC
            TC = utils.find_new_point_location(
                tibia_tc["TC"]["Plane"], tebia_Plane, tibia_tc["TC"]["C"]
            )

            tebia_Plane[2] = TC
            # print(f'  tebia_Plane  {tebia_Plane}')
            MC = utils.find_new_point_location(
                tibia_dict["MC"]["Plane"], tebia_Plane, tibia_dict["MC"]["C"]
            )
            LC = utils.find_new_point_location(
                tibia_dict["LC"]["Plane"], tebia_Plane, tibia_dict["LC"]["C"]
            )
            MM = utils.find_new_point_location(
                tibia_dict["MM"]["Plane"], tebia_Plane, tibia_dict["MM"]["C"]
            )
            LM = utils.find_new_point_location(
                tibia_dict["LM"]["Plane"], tebia_Plane, tibia_dict["LM"]["C"]
            )
            ANC = (np.array(MM) + np.array(LM)) / 2

            # print(f'MC {MC} LC {LC}')
            B = np.array(femur_plane[1])
            C = np.array(femur_plane[2])
            mid = (B + C) / 2
            distance1 = utils.distance_3d(mid, femur_plane[0])
            scale1 = 107 / distance1  # 107 mm is the reference distance
            cutting_points_scalling = 30 / scale1
            direction_vector =  TC - ANC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling

            # Calculate the projection point
            #
            # TC = np.array([570.24474024 ,489.94027216, 323.00401793])
            projection_point = TC - scaled_unit_vector
            print(f'projection_point {projection_point} ANC  {ANC}  MM {MM} LM {LM}  TC {TC}')
            Mid_point_RoboTracker = (
                np.array(tracker_plane[1]) + np.array(tracker_plane[2])
            ) / 2
            RoboTracker_y = Mid_point_RoboTracker - tracker_plane[0]
            x_TCS = ANC - TC
            print(f'TC {TC}  omega {omega}')
            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            def vector3_signed_angle(tracker, MC, LC):
                # print(f'*' * 100)
                # print(f'tracker {tracker}  ME {MC}  LE {LC}')
                # print(f'*' * 100)

                # Step 1: Compute midpoint between point 2 and point 1
                mid_point = (tracker[2] + tracker[1]) / 2.0

                # Step 2: Define LCS axes
                LCS_x = tracker[2] - mid_point
                LCS_y = mid_point - tracker[0]
                LCS_z = np.cross(LCS_x, LCS_y)

                # Normalize the axes
                LCS_x /= np.linalg.norm(LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)
                LCS_z /= np.linalg.norm(LCS_z)

                # Re-orthogonalize LCS_y (optional but safer)
                LCS_y = np.cross(LCS_z, LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)

                # Step 3: Build rotation matrix
                R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

                # Step 4: Translate ME and LE to LCS origin
                MC_translated = MC - mid_point
                LC_translated = LC - mid_point

                # Step 5: Apply rotation (World to Local)
                MC_local = R.T @ MC_translated
                LC_local = R.T @ LC_translated

                # Step 6: Compute MC→LC vector in LCS
                V_local = LC_local - MC_local

                # Step 7: Compute angle in LCS XZ plane
                angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
                angle_deg = int(np.degrees(angle_rad))
                if angle_deg == 0:
                    return angle_deg
                elif angle_deg < 0:
                    angle_deg += 90
                else:
                    angle_deg -= 90

                return angle_deg

            try:
                flexion = abs(vector2_signed_angle_xy(RoboTracker_y, x_TCS)) - 90
                # print(f'flexion {flexion}')

                varus = vector3_signed_angle(tracker=tracker_plane, MC=MC, LC=LC)
                # print(f'varus {varus}')

                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error {e}")
                time.sleep(2)
            # projection_point=np.array([569.58733012, 493.52064494, 324.65579388])
            projection_point[2], projection_point[1] = (
                projection_point[1],
                projection_point[2],
            )
            projection_point = np.array(projection_point).reshape(1, -1)
            # point1 = point1 - old_origin_optical + new_origin
            predicted_dest = updated_model.predict(projection_point)

            print(
                f" projection_point {projection_point} predicted_dest  {predicted_dest} varus ,flexion {varus} {flexion}"
            )
            if arm.connected and USE_ROBOT:
                arm.motion_enable(enable=True)  # Enable motion
                arm.set_mode(0)  # Set to position control mode
                arm.set_state(0)  # Set to ready state
                # code = 0
                code = arm.set_position(
                    x=predicted_dest[0][0],
                    y=predicted_dest[0][1],
                    z=predicted_dest[0][2],
                    roll=90,
                    pitch=flexion,
                    yaw=varus,
                    speed=25,
                    mvacc=50,
                    wait=True,
                )
                if code == 0:
                    print("Successfully moved to the desired position!")
                    await asyncio.sleep(1)
                else:
                    print(f"Failed to move the arm. Error code: {code}")
            await asyncio.sleep(1)
            # return
        except Exception as e:
            print(f"Error {e}")


async def DistalFemureCutVerification(marking, websocket):
    try:
        await send_tuple(websocket, ("femur-inner-page5", 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")
        except:
            print("Error loading femur variables")
        omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    # omega = utils.GetplanningInput(filename="femure_input1")
    while True:
        try:
            if page != "femur-inner-page5.html":
                return
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 12 and len(detections_left) == 12:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)
                    if page != "femur-distal-femur-cut.html":
                        return
                    print(f"R {len(detections_right)}  L {len(detections_left)}")
                    continue

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])

            # Extract elements in groups of 3
            detection_groups = {
                # "R": (detections_right[:3], detections_left[:3]),  # Robot
                "F": (detections_right[3:6], detections_left[3:6]),  # Femur
                "V": (detections_right[6:9], detections_left[6:9]),  # Verification Tool
                # "T": (detections_right[9:12], detections_left[9:12]),  # Tibia
            }

            # Store the results
            tracked_points = {}

            # Iterate over detection groups and process each one
            for label, (right, left) in detection_groups.items():
                tracker = (
                    TriangleTracker(right, left, rec_img_right, rec_img_left)
                    if label == "V"  # No label passed for Robot & Verification Tool
                    else TriangleTracker(
                        right, left, rec_img_right, rec_img_left, label
                    )
                    # Label passed for Femur & Tibia
                )
                tracker.find_depth()
                tracked_points[label] = tracker.getLEDcordinates()
            # Extract individual results
            # R_points = tracked_points["R"]
            F_points = tracked_points["F"]
            Tool_points = tracked_points["V"]
            # T_points = tracked_points["T"]
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            B = np.array(femur_plane[1])
            C = np.array(femur_plane[0])
            # mid = (B + C) / 2
            distance1 = utils.distance_3d(B, C)
            scale = 107 / distance1  # 107 mm is the reference distance

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )

            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )

            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            Mid_Tool_points = (np.array(Tool_points[1]) + np.array(Tool_points[2])) / 2

            # Step 2: y vector calculation
            toolTracker_y = Mid_Tool_points - Tool_points[0]

            # Step 3: x vector calculation
            toolracker_x = np.array(Tool_points[1]) - np.array(Tool_points[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            toolrackernorm_x = np.linalg.norm(toolracker_x)

            toolrackernorm_y = np.linalg.norm(toolTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            toolracker_z = np.cross(toolTracker_y, toolracker_x)

            # Step 6: FCS vectors
            x_FCS = FC - HC
            z_FCS = LE - ME

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            def vector3_signed_angle(tracker, ME, LE):

                # Step 1: Compute midpoint between point 2 and point 1
                mid_point = (tracker[2] + tracker[1]) / 2.0

                # Step 2: Define LCS axes
                LCS_x = tracker[2] - mid_point
                LCS_y = mid_point - tracker[0]
                LCS_z = np.cross(LCS_x, LCS_y)

                # Normalize the axes
                LCS_x /= np.linalg.norm(LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)
                LCS_z /= np.linalg.norm(LCS_z)

                # Re-orthogonalize LCS_y (optional but safer)
                LCS_y = np.cross(LCS_z, LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)

                # Step 3: Build rotation matrix
                R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

                # Step 4: Translate ME and LE to LCS origin
                ME_translated = ME - mid_point
                LE_translated = LE - mid_point

                # Step 5: Apply rotation (World to Local)
                ME_local = R.T @ ME_translated
                LE_local = R.T @ LE_translated

                # Step 6: Compute ME→LE vector in LCS
                V_local = LE_local - ME_local

                # Step 7: Compute angle in LCS XZ plane
                angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
                angle_deg = int(np.degrees(angle_rad))
                if angle_deg == 0:
                    return angle_deg
                elif angle_deg < 0:
                    angle_deg += 90
                else:
                    angle_deg -= 90

                return angle_deg

            # --------------------------
            # Final angle calculation
            try:
                flexion = vector2_signed_angle_xy(toolracker_y, x_FCS)
                varus = vector3_signed_angle(tracker=Tool_points, ME=ME, LE=LE)

                print(f"flexion  {flexion} varus {varus}")

                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error {e}")

            # Given data
            cutting_points_scalling = 50 / scale
            cutting_points = omega / scale

            # Compute direction vectors
            direction_vector = HC - FC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points
            projection_point = LDC + scaled_unit_vector

            direction_vector2 = Mid_Tool_points - Tool_points[0]
            projection_point2 = cutting_points_scalling + direction_vector2

            # Compute plane normal using cross product
            normal_vector = np.cross(direction_vector, scaled_unit_vector)

            # Compute plane equation: Ax + By + Cz + D = 0
            A, B, C = normal_vector
            D = -np.dot(normal_vector, projection_point)

            # Compute distance from projection_point2 to the plane
            x1, y1, z1 = projection_point2
            distance_mm = abs(A * x1 + B * y1 + C * z1 + D) / np.linalg.norm(
                normal_vector
            )

            print(
                f"Distance between projection_point2 and the plane in mm: {distance_mm:.2f} mm"
            )
            await send_tuple(
                websocket, ("femur-inner-page5", flexion, varus, distance_mm)
            )
            await asyncio.sleep(0.1)
        except Exception as e:
            print(f"Error {e}")


async def TibiaCutVerification(marking, websocket):
    try:
        await send_tuple(websocket, ("tibia-inner-page3", 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")

            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables_TC.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_tc = pickle.load(tibia_handle)
            # Tibia TC variables
            pickle_path_tibia_tc = os.path.join(base_dir, "tibia_variables.pickle")
            with open(pickle_path_tibia_tc, "rb") as tibia_handle:
                tibia_dict = pickle.load(tibia_handle)
                print(f" tibia_dict {tibia_dict}")
        except:
            print("Error loading femur variables")
        # omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    omega = utils.GetplanningInput(filename="tibia_input1")
    while True:
        try:
            if page != "tibia-inner-page3.html":
                return
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 12 and len(detections_left) == 12:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)
                    if page != "tibia-inner-page3.html":
                        return
                    print(f"R {len(detections_right)}  L {len(detections_left)}")
                    continue

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])

            # Extract elements in groups of 3
            detection_groups = {
                # "R": (detections_right[:3], detections_left[:3]),  # Robot
                "F": (detections_right[3:6], detections_left[3:6]),  # Femur
                "V": (detections_right[6:9], detections_left[6:9]),  # Verification Tool
                "T": (detections_right[9:12], detections_left[9:12]),  # Tibia
            }

            # Store the results
            tracked_points = {}

            # Iterate over detection groups and process each one
            for label, (right, left) in detection_groups.items():
                tracker = (
                    TriangleTracker(right, left, rec_img_right, rec_img_left)
                    if label == "V"  # No label passed for Robot & Verification Tool
                    else TriangleTracker(
                        right, left, rec_img_right, rec_img_left, label
                    )
                    # Label passed for Femur & Tibia
                )
                tracker.find_depth()
                tracked_points[label] = tracker.getLEDcordinates()
            # Extract individual results
            # R_points = tracked_points["R"]
            F_points = tracked_points["F"]
            Tool_points = tracked_points["V"]
            T_points = tracked_points["T"]
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]
            if T_points[1][1] >= T_points[0][1]:
                tebia_Plane = [T_points[2], T_points[0], T_points[1]]
            else:
                tebia_Plane = [T_points[2], T_points[1], T_points[0]]
            print(f'test')
            B = np.array(tebia_Plane[1])
            C = np.array(tebia_Plane[0])
            distance1 = utils.distance_3d(B, C)
            scale = 107 / distance1  # 107 mm is the reference distance

            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            tebia_Plane[2] = FC
            TC = utils.find_new_point_location(
                tibia_tc["TC"]["Plane"], tebia_Plane, tibia_tc["TC"]["C"]
            )

            tebia_Plane[2] = TC
            # print(f'  tebia_Plane  {tebia_Plane}')
            MC = utils.find_new_point_location(
                tibia_dict["MC"]["Plane"], tebia_Plane, tibia_dict["MC"]["C"]
            )
            LC = utils.find_new_point_location(
                tibia_dict["LC"]["Plane"], tebia_Plane, tibia_dict["LC"]["C"]
            )
            MM = utils.find_new_point_location(
                tibia_dict["MM"]["Plane"], tebia_Plane, tibia_dict["MM"]["C"]
            )
            LM = utils.find_new_point_location(
                tibia_dict["LM"]["Plane"], tebia_Plane, tibia_dict["LM"]["C"]
            )
            ANC = (np.array(MM) + np.array(LM)) / 2

            Mid_Tool_points = (np.array(Tool_points[1]) + np.array(Tool_points[2])) / 2

            # Step 2: y vector calculation
            toolTracker_y = Mid_Tool_points - Tool_points[0]

            # Step 3: x vector calculation
            toolracker_x = np.array(Tool_points[1]) - np.array(Tool_points[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            toolrackernorm_x = np.linalg.norm(toolracker_x)

            toolrackernorm_y = np.linalg.norm(toolTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            toolracker_z = np.cross(toolTracker_y, toolracker_x)

            # Step 6: FCS vectors
            x_FCS = ANC - TC
            z_FCS = LC - MC

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            def vector3_signed_angle(tracker, MC, LC):
                # print(f'*' * 100)
                # print(f'tracker {tracker}  ME {MC}  LE {LC}')
                # print(f'*' * 100)

                # Step 1: Compute midpoint between point 2 and point 1
                mid_point = (tracker[2] + tracker[1]) / 2.0

                # Step 2: Define LCS axes
                LCS_x = tracker[2] - mid_point
                LCS_y = mid_point - tracker[0]
                LCS_z = np.cross(LCS_x, LCS_y)

                # Normalize the axes
                LCS_x /= np.linalg.norm(LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)
                LCS_z /= np.linalg.norm(LCS_z)

                # Re-orthogonalize LCS_y (optional but safer)
                LCS_y = np.cross(LCS_z, LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)

                # Step 3: Build rotation matrix
                R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

                # Step 4: Translate ME and LE to LCS origin
                MC_translated = MC - mid_point
                LC_translated = LC - mid_point

                # Step 5: Apply rotation (World to Local)
                MC_local = R.T @ MC_translated
                LC_local = R.T @ LC_translated

                # Step 6: Compute MC→LC vector in LCS
                V_local = LC_local - MC_local

                # Step 7: Compute angle in LCS XZ plane
                angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
                angle_deg = int(np.degrees(angle_rad))
                if angle_deg == 0:
                    return angle_deg
                elif angle_deg < 0:
                    angle_deg += 90
                else:
                    angle_deg -= 90

                return angle_deg

            # --------------------------
            # Final angle calculation
            try:
                flexion = vector2_signed_angle_xy(toolrackernorm_y, x_FCS)
                varus = vector3_signed_angle(tracker=Tool_points, MC=MC, LC=LC)

                print(f"flexion  {flexion} varus {varus}")

                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error {e}")

            # Given data
            cutting_points_scalling = 50 / scale
            cutting_points = omega / scale
            #
            # # Compute direction vectors
            direction_vector = ANC - TC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points
            projection_point = TC + scaled_unit_vector

            direction_vector2 = Mid_Tool_points - Tool_points[0]
            projection_point2 = cutting_points_scalling + direction_vector2

            # Compute plane normal using cross product
            normal_vector = np.cross(direction_vector, scaled_unit_vector)

            # Compute plane equation: Ax + By + Cz + D = 0
            A, B, C = normal_vector
            D = -np.dot(normal_vector, projection_point)

            # Compute distance from projection_point2 to the plane
            x1, y1, z1 = projection_point2
            distance_mm = abs(A * x1 + B * y1 + C * z1 + D) / np.linalg.norm(
                normal_vector
            )

            print(
                f"Distance between projection_point2 and the plane in mm: {distance_mm:.2f} mm"
            )

            await send_tuple(
                websocket, ("tibia-inner-page3", flexion, varus, distance_mm)
            )
            await asyncio.sleep(0.1)
        except Exception as e:
            print(f"Error {e}")

async def primary_robot_position(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")
        except:
            print("Error loading femur variables")

    except Exception as e:
        print(f"Error {e}")
    if USE_ROBOT:
        arm = XArmAPI(arm_ip)
        arm.connect()
    while True:
        try:
            if page != "robot_position.html":
                return
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)
                    if page != "robot_position.html":
                        return
                    print(f"R {len(detections_right)}  L {len(detections_left)}")
                    continue
            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            robot_detection_right = detections_right[:3]
            robot_detection_left = detections_left[:3]

            robot = TriangleTracker(
                robot_detection_right, robot_detection_left, rec_img_right, rec_img_left
            )
            robot.find_depth()
            R_points = robot.getLEDcordinates()
            if R_points[1][1] >= R_points[0][1]:
                tracker_plane = [R_points[2], R_points[0], R_points[1]]
            else:
                tracker_plane = [R_points[2], R_points[1], R_points[0]]

            centroid = utils.calculate_centroid(tracker_plane)

            if (centroid[0] > HC[0]):
                print("Move  robot Right-->")
            else:
                print("Move  robot Left-->")





            # femure_detection_right = detections_right[3:6]
            # femure_detection_left = detections_left[3:6]
            #
            # femure = TriangleTracker(
            #     femure_detection_right,
            #     femure_detection_left,
            #     rec_img_right,
            #     rec_img_left,
            #     "F",
            # )
            # femure.find_depth()
            # F_points = femure.getLEDcordinates()
            # if F_points[1][1] >= F_points[0][1]:
            #     femur_plane = [F_points[2], F_points[0], F_points[1]]
            # else:
            #     femur_plane = [F_points[2], F_points[1], F_points[0]]


        except Exception as e:
            print(f"Error {e}")
async def primary_kneeDistalFemureCut(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")
        except:
            print("Error loading femur variables")
        # omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    initial_data = GetRoboticArmInitialData()
    if USE_ROBOT:
        arm = XArmAPI(arm_ip)
        arm.connect()
    omega = utils.GetplanningInput(filename="femure_input1")
    while True:
        try:
            if page != "femur-distal-femur-cut.html":
                return
            while True:
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)
                    if page != "femur-distal-femur-cut.html":
                        return
                    print(f"R {len(detections_right)}  L {len(detections_left)}")
                    continue

            # detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
            # detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            robot_detection_right = detections_right[:3]
            robot_detection_left = detections_left[:3]

            robot = TriangleTracker(
                robot_detection_right, robot_detection_left, rec_img_right, rec_img_left
            )
            robot.find_depth()
            R_points = robot.getLEDcordinates()
            # print(f'R_points   {R_points}')
            if R_points[1][1] >= R_points[0][1]:
                tracker_plane = [R_points[2], R_points[0], R_points[1]]
            else:
                tracker_plane = [R_points[2], R_points[1], R_points[0]]
            femure_detection_right = detections_right[3:6]
            femure_detection_left = detections_left[3:6]

            femure = TriangleTracker(
                femure_detection_right,
                femure_detection_left,
                rec_img_right,
                rec_img_left,
                "F",
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()
            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]
            new_origin = utils.find_new_point_location(
                old_plane_points=initial_data["robot_tracker_plane"],
                new_plane_points=tuple(tracker_plane),
                old_marked_point=initial_data["old_mark_point"],
            )

            # Step 1: Compute centroid
            centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
            # R_points
            # Step 2: Compute translation vector
            translation_vector = new_origin - centroid

            # Step 3: Translate the plane
            Newtranslated_plane = (
                initial_data["robot_tracker_plane"] + translation_vector
            )

            print(
                f" translation_vector {translation_vector} Newtranslated_plane{Newtranslated_plane}"
            )

            robot_calib_data = os.path.join(
                current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
            )
            led_points, robot_points = load_data(file_path=robot_calib_data)
            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=initial_data["translated_plane"],
                        new_plane_points=tuple(Newtranslated_plane),
                        old_marked_point=led,
                    )
                    for led in led_points
                ]
            )

            # Swap Y and Z back to match coordinate system
            transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

            updated_model = LinearRegression()
            updated_model.fit(transformed_led_points, robot_points)
            print(f"femur_plane {femur_plane}")
            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            LDC = utils.find_new_point_location(
                femur_dict["LDC"]["Plane"], femur_plane, femur_dict["LDC"]["C"]
            )
            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )

            M1 = (FC[1] - HC[1]) / (FC[0] - HC[0])  # DFC_slope represnets M1
            M2 = -1 / M1  # DFC_target slope represnets M2

            B = np.array(femur_plane[1])
            C = np.array(femur_plane[0])
            mid = (B + C) / 2
            distance1 = np.linalg.norm(mid - femur_plane[0])
            scale1 = 107 / distance1  # 107 mm is the reference distance

            print(f"*" * 100)
            print(f"omega {omega}")
            print(f"*" * 100)
            cutting_points_scalling = omega / scale1
            direction_vector = HC - FC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling
            # print(f'scaled_unit_vector {scaled_unit_vector}')
            # Calculate the projection point
            print(f"LDC {LDC}")
            projection_point = LDC + scaled_unit_vector
            # print(f' projection_point {projection_point}')

            Mid_point_RoboTracker = (
                np.array(tracker_plane[1]) + np.array(tracker_plane[2])
            ) / 2

            # Step 2: y vector calculation
            RoboTracker_y = Mid_point_RoboTracker - tracker_plane[0]

            # Step 3: x vector calculation
            RoboTracker_x = np.array(tracker_plane[1]) - np.array(tracker_plane[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            RoboTrackernorm_x = np.linalg.norm(RoboTracker_x)

            RoboTrackernorm_y = np.linalg.norm(RoboTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            RoboTracker_z = np.cross(RoboTracker_y, RoboTracker_x)

            # Step 6: FCS vectors
            x_FCS = FC - HC
            z_FCS = LE - ME

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            def vector3_signed_angle(tracker, ME, LE):

                # Step 1: Compute midpoint between point 2 and point 1
                mid_point = (tracker[2] + tracker[1]) / 2.0

                # Step 2: Define LCS axes
                LCS_x = tracker[2] - mid_point
                LCS_y = mid_point - tracker[0]
                LCS_z = np.cross(LCS_x, LCS_y)

                # Normalize the axes
                LCS_x /= np.linalg.norm(LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)
                LCS_z /= np.linalg.norm(LCS_z)

                # Re-orthogonalize LCS_y (optional but safer)
                LCS_y = np.cross(LCS_z, LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)

                # Step 3: Build rotation matrix
                R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

                # Step 4: Translate ME and LE to LCS origin
                ME_translated = ME - mid_point
                LE_translated = LE - mid_point

                # Step 5: Apply rotation (World to Local)
                ME_local = R.T @ ME_translated
                LE_local = R.T @ LE_translated

                # Step 6: Compute ME→LE vector in LCS
                V_local = LE_local - ME_local

                # Step 7: Compute angle in LCS XZ plane
                angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
                angle_deg = int(np.degrees(angle_rad))
                if angle_deg == 0:
                    return angle_deg
                elif angle_deg < 0:
                    angle_deg += 90
                else:
                    angle_deg -= 90

                return angle_deg

            # --------------------------
            # Final angle calculation
            try:
                flexion = vector2_signed_angle_xy(RoboTracker_y, x_FCS)
                # print(f'flexion {flexion}')

                varus = vector3_signed_angle(tracker=tracker_plane, ME=ME, LE=LE)
                # print(f'varus {varus}')

                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error {e}")
                time.sleep(2)
            projection_point[2], projection_point[1] = (
                projection_point[1],
                projection_point[2],
            )
            projection_point = np.array(projection_point).reshape(1, -1)
            # point1 = point1 - old_origin_optical + new_origin
            predicted_dest = updated_model.predict(projection_point)

            print(
                f" projection_point {projection_point} predicted_dest  {predicted_dest} varus ,flexion {varus} {flexion}"
            )
            if arm.connected and USE_ROBOT:

                arm.motion_enable(enable=True)  # Enable motion
                arm.set_mode(0)  # Set to position control mode
                arm.set_state(0)  # Set to ready state
                adjustedAngle = utils.GetplanningInput(filename="femure_input2")
                # code = 0
                code = arm.set_position(
                    x=predicted_dest[0][0],
                    y=predicted_dest[0][1],
                    z=predicted_dest[0][2],
                    roll=90 - varus,
                    pitch=flexion,
                    yaw=adjustedAngle,
                    speed=ROBOT_SPEED,
                    mvacc=50,
                    wait=True,
                )
                if code == 0:
                    print("Successfully moved to the desired position!")
                    await asyncio.sleep(1)
                else:
                    print(f"Failed to move the arm. Error code: {code}")
                await asyncio.sleep(1)
            return
        except Exception as e:
            print(f"Error {e}")

async def primary_kneeAnteriorCut(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")
        except:
            print("Error loading femur variables")
        omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    initial_data = GetRoboticArmInitialData()
    if USE_ROBOT:
        arm = XArmAPI(arm_ip)
        arm.connect()
    while True:
        try:
            while True:
                if page != "femur-anterior-resection.html":
                    return
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)

                    continue

            # detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
            # detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            three_right = detections_right[0:3]
            three_left = detections_left[0:3]
            three_right = sorted(three_right, key=lambda x: x[0])
            three_left = sorted(three_left, key=lambda x: x[0])
            Pointer = TriangleTracker(
                three_right, three_left, rec_img_right, rec_img_left
            )
            Pointer.find_depth()
            R_points = Pointer.getLEDcordinates()

            if R_points[1][1] >= R_points[0][1]:
                tracker_plane = [R_points[2], R_points[0], R_points[1]]
            else:
                tracker_plane = [R_points[2], R_points[1], R_points[0]]
            # print(f'robotTracker   {tracker_plane}')
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            # next_three_right = detections_right[3:6]
            # next_three_left = detections_left[3:6]
            femure = TriangleTracker(
                first_three_right, first_three_left, rec_img_right, rec_img_left, "F"
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()
            print(f'F_points {F_points}')

            # Tebia = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
            #                           rec_img_left)
            # Tebia.find_depth()
            # pointer_Leds = Tebia.getLEDcordinates()
            # print(f'Tebia   {pointer_Leds}')

            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            # tracker_plane = [R_points[0], R_points[1], R_points[2]]

            new_origin = utils.find_new_point_location(
                old_plane_points=initial_data["robot_tracker_plane"],
                new_plane_points=tuple(tracker_plane),
                old_marked_point=initial_data["old_mark_point"],
            )

            # Step 1: Compute centroid
            centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
            # R_points
            # Step 2: Compute translation vector
            translation_vector = new_origin - centroid

            # Step 3: Translate the plane
            Newtranslated_plane = (
                initial_data["robot_tracker_plane"] + translation_vector
            )

            robot_calib_data = os.path.join(
                current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
            )
            led_points, robot_points = load_data(file_path=robot_calib_data)
            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=initial_data["translated_plane"],
                        new_plane_points=tuple(Newtranslated_plane),
                        old_marked_point=led,
                    )
                    for led in led_points
                ]
            )

            # Swap Y and Z back to match coordinate system
            transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

            updated_model = LinearRegression()
            updated_model.fit(transformed_led_points, robot_points)
            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            LDC = utils.find_new_point_location(
                femur_dict["LDC"]["Plane"], femur_plane, femur_dict["LDC"]["C"]
            )
            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )
            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )
            AC = utils.find_new_point_location(
                femur_dict["AC"]["Plane"], femur_plane, femur_dict["AC"]["C"]
            )

            M1 = (FC[1] - HC[1]) / (FC[0] - HC[0])  # DFC_slope represnets M1
            M2 = -1 / M1  # DFC_target slope represnets M2

            B = np.array(femur_plane[1])
            C = np.array(femur_plane[2])
            mid = (B + C) / 2
            distance1 = np.linalg.norm(mid - femur_plane[0])
            scale1 = 107 / distance1  # 107 mm is the reference distance
            cutting_points_scalling = omega / scale1
            direction_vector = HC - FC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling
            # print(f'scaled_unit_vector {scaled_unit_vector}')
            # Calculate the projection point
            projection_point = LDC + scaled_unit_vector
            # print(f' projection_point {projection_point}')

            Mid_point_RoboTracker = (
                np.array(tracker_plane[1]) + np.array(tracker_plane[2])
            ) / 2

            # Step 2: y vector calculation
            RoboTracker_y = Mid_point_RoboTracker - tracker_plane[0]

            # Step 3: x vector calculation
            RoboTracker_x = np.array(tracker_plane[1]) - np.array(tracker_plane[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            RoboTrackernorm_x = np.linalg.norm(RoboTracker_x)

            RoboTrackernorm_y = np.linalg.norm(RoboTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            RoboTracker_z = np.cross(RoboTracker_y, RoboTracker_x)

            # Step 6: FCS vectors
            x_FCS = FC - HC
            z_FCS = LE - ME

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            def vector3_signed_angle(tracker, ME, LE):
                print(f"*" * 100)
                print(f"tracker {tracker}  ME {ME}  LE {LE}")
                print(f"*" * 100)

                # Step 1: Compute midpoint between point 2 and point 1
                mid_point = (tracker[2] + tracker[1]) / 2.0

                # Step 2: Define LCS axes
                LCS_x = tracker[2] - mid_point
                LCS_y = mid_point - tracker[0]
                LCS_z = np.cross(LCS_x, LCS_y)

                # Normalize the axes
                LCS_x /= np.linalg.norm(LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)
                LCS_z /= np.linalg.norm(LCS_z)

                # Re-orthogonalize LCS_y (optional but safer)
                LCS_y = np.cross(LCS_z, LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)

                # Step 3: Build rotation matrix
                R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

                # Step 4: Translate ME and LE to LCS origin
                ME_translated = ME - mid_point
                LE_translated = LE - mid_point

                # Step 5: Apply rotation (World to Local)
                ME_local = R.T @ ME_translated
                LE_local = R.T @ LE_translated

                # Step 6: Compute ME→LE vector in LCS
                V_local = LE_local - ME_local

                # Step 7: Compute angle in LCS XZ plane
                angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
                angle_deg = int(np.degrees(angle_rad))
                if angle_deg == 0:
                    return angle_deg
                elif angle_deg < 0:
                    angle_deg += 90
                else:
                    angle_deg -= 90

                return angle_deg

            # --------------------------
            # Final angle calculation
            try:
                flexion = vector2_signed_angle_xy(RoboTracker_y, x_FCS)
                # print(f'flexion {flexion}')

                varus = vector3_signed_angle(tracker=tracker_plane, ME=ME, LE=LE)
                # print(f'varus {varus}')

                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error {e}")
                time.sleep(2)
            AC[2], AC[1] = AC[1], AC[2]
            print(f'AC {AC}')
            projection_point = np.array(AC).reshape(1, -1)
            # point1 = point1 - old_origin_optical + new_origin
            predicted_dest = updated_model.predict(projection_point)

            print(
                f" projection_point {projection_point} predicted_dest  {predicted_dest} varus ,flexion {varus} {flexion}"
            )
            if arm.connected and USE_ROBOT:

                arm.motion_enable(enable=True)  # Enable motion
                arm.set_mode(0)  # Set to position control mode
                arm.set_state(0)  # Set to ready state
                code = arm.set_position(
                    x=predicted_dest[0][0],
                    y=predicted_dest[0][1],
                    z=predicted_dest[0][2],
                    roll=90,
                    pitch=90 + flexion,
                    yaw=varus,
                    speed=ROBOT_SPEED,
                    mvacc=50,
                    wait=True,
                )
                if code == 0:
                    print("Successfully moved to the desired position!")
                    await asyncio.sleep(1)
                else:
                    print(f"Failed to move the arm. Error code: {code}")
                await asyncio.sleep(1)
            return
        except Exception as e:
            print(f"Error {e}")


# break


async def primary_kneefemur_posterior(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")
        except:
            print("Error loading femur variables")
        # omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    initial_data = GetRoboticArmInitialData()
    if USE_ROBOT:
        arm = XArmAPI(arm_ip)
        arm.connect()
    while True:
        try:
            while True:
                if page != "femur-posterior-resection.html":

                    return
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)
                    print(f'R {len(detections_right)}    L{len(detections_left)}')
                    continue

            # detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
            # detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            three_right = detections_right[0:3]
            three_left = detections_left[0:3]
            three_right = sorted(three_right, key=lambda x: x[0])
            three_left = sorted(three_left, key=lambda x: x[0])
            Pointer = TriangleTracker(
                three_right, three_left, rec_img_right, rec_img_left
            )
            Pointer.find_depth()
            R_points = Pointer.getLEDcordinates()

            if R_points[1][1] >= R_points[0][1]:
                tracker_plane = [R_points[2], R_points[0], R_points[1]]
            else:
                tracker_plane = [R_points[2], R_points[1], R_points[0]]
            # print(f'robotTracker   {tracker_plane}')
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            next_three_right = detections_right[3:6]
            next_three_left = detections_left[3:6]
            femure = TriangleTracker(
                first_three_right, first_three_left, rec_img_right, rec_img_left, "F"
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()
            # print(f'F_points {F_points}')

            # Tebia = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
            #                           rec_img_left)
            # Tebia.find_depth()
            # pointer_Leds = Tebia.getLEDcordinates()
            # print(f'Tebia   {pointer_Leds}')

            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            # tracker_plane = [R_points[0], R_points[1], R_points[2]]

            new_origin = utils.find_new_point_location(
                old_plane_points=initial_data["robot_tracker_plane"],
                new_plane_points=tuple(tracker_plane),
                old_marked_point=initial_data["old_mark_point"],
            )

            # Step 1: Compute centroid
            centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
            # R_points
            # Step 2: Compute translation vector
            translation_vector = new_origin - centroid

            # Step 3: Translate the plane
            Newtranslated_plane = (
                initial_data["robot_tracker_plane"] + translation_vector
            )

            robot_calib_data = os.path.join(
                current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
            )
            led_points, robot_points = load_data(file_path=robot_calib_data)
            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=initial_data["translated_plane"],
                        new_plane_points=tuple(Newtranslated_plane),
                        old_marked_point=led,
                    )
                    for led in led_points
                ]
            )

            # Swap Y and Z back to match coordinate system
            transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

            updated_model = LinearRegression()
            updated_model.fit(transformed_led_points, robot_points)
            LPC = utils.find_new_point_location(
                femur_dict["LPC"]["Plane"], femur_plane, femur_dict["LPC"]["C"]
            )
            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            AC = utils.find_new_point_location(
                femur_dict["AC"]["Plane"], femur_plane, femur_dict["AC"]["C"]
            )
            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )
            Mid_point_RoboTracker = (
                np.array(tracker_plane[1]) + np.array(tracker_plane[2])
            ) / 2

            # Step 2: y vector calculation
            RoboTracker_y = Mid_point_RoboTracker - tracker_plane[0]

            # Step 3: x vector calculation
            RoboTracker_x = np.array(tracker_plane[1]) - np.array(tracker_plane[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            RoboTrackernorm_x = np.linalg.norm(RoboTracker_x)

            RoboTrackernorm_y = np.linalg.norm(RoboTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            RoboTracker_z = np.cross(RoboTracker_y, RoboTracker_x)

            # Step 6: FCS vectors
            x_FCS = FC - HC
            z_FCS = ME - LE
            y_FCS = np.cross(z_FCS, x_FCS)


            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            def vector3_signed_angle(tracker, ME, LE):
                print(f"*" * 100)
                print(f"tracker {tracker}  ME {ME}  LE {LE}")
                print(f"*" * 100)

                # Step 1: Compute midpoint between point 2 and point 1
                mid_point = (tracker[2] + tracker[1]) / 2.0

                # Step 2: Define LCS axes
                LCS_x = tracker[2] - mid_point
                LCS_y = mid_point - tracker[0]
                LCS_z = np.cross(LCS_x, LCS_y)

                # Normalize the axes
                LCS_x /= np.linalg.norm(LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)
                LCS_z /= np.linalg.norm(LCS_z)

                # Re-orthogonalize LCS_y (optional but safer)
                LCS_y = np.cross(LCS_z, LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)

                # Step 3: Build rotation matrix
                R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

                # Step 4: Translate ME and LE to LCS origin
                ME_translated = ME - mid_point
                LE_translated = LE - mid_point

                # Step 5: Apply rotation (World to Local)
                ME_local = R.T @ ME_translated
                LE_local = R.T @ LE_translated

                # Step 6: Compute ME→LE vector in LCS
                V_local = LE_local - ME_local

                # Step 7: Compute angle in LCS XZ plane
                angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
                angle_deg = int(np.degrees(angle_rad))
                if angle_deg == 0:
                    return angle_deg
                elif angle_deg < 0:
                    angle_deg += 90
                else:
                    angle_deg -= 90

                return angle_deg

            try:
                flexion = vector2_signed_angle_xy(RoboTracker_y, x_FCS)
                # print(f'flexion {flexion}')

                varus = vector3_signed_angle(tracker=tracker_plane, ME=ME, LE=LE)
                # print(f'varus {varus}')

                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error {e}")

            AC[2], AC[1] = AC[1], AC[2]
            point_a = np.array(AC).reshape(1, -1)
            point_a_dest = updated_model.predict(point_a)
            LPC[2], LPC[1] = LPC[1], LPC[2]
            point_b = np.array(LPC).reshape(1, -1)
            point_b_dest = updated_model.predict(point_b)

            femuresize = utils.distance_3d(
                np.array(point_a_dest.flatten()), np.array(point_b_dest.flatten())
            )
            result = get_size(femuresize)
            print(
                f"******************** femuresize {femuresize} get_size {result} ********************"
            )

            fsize = get_offset(result)
            print(f"Offset for {result}: {fsize}")
            # Swap second and third elements before conversion
            FC[1], FC[2] = FC[2], FC[1]
            HC[1], HC[2] = HC[2], HC[1]
            ME[1], ME[2] = ME[2], ME[1]
            LE[1], LE[2] = LE[2], LE[1]

            # Convert to NumPy arrays after swapping
            FC = np.array(FC).reshape(1, -1)
            HC = np.array(HC).reshape(1, -1)
            ME = np.array(ME).reshape(1, -1)
            LE = np.array(LE).reshape(1, -1)

            # Model predictions
            x_FCS_robot = (
                updated_model.predict(FC).flatten()
                - updated_model.predict(HC).flatten()
            )
            z_FCS_robot = (
                updated_model.predict(LE).flatten()
                - updated_model.predict(ME).flatten()
            )

            x_unit = x_FCS_robot / np.linalg.norm(x_FCS_robot)
            z_unit = z_FCS_robot / np.linalg.norm(z_FCS_robot)
            y_unit = np.cross(z_unit, x_unit)


            # point_A_diretion_Y = np.array([569.46904489, 493.31720926, 327.33136856])
            # point_B_diretion_Y = np.array([570.65097859, 492.53589885, 328.79015981])
            #
            #
            # B = np.array(femur_plane[1])
            # C = np.array(femur_plane[2])
            # mid = (B + C) / 2
            # distance1 = np.linalg.norm(mid - femur_plane[0])
            # scale1 = 107 / distance1  # 107 mm is the reference distance
            # cutting_points_scalling = fsize / scale1
            # direction_vector = point_A_diretion_Y - point_B_diretion_Y
            # unit_vector = direction_vector / np.linalg.norm(direction_vector)
            # scaled_unit_vector = unit_vector * cutting_points_scalling
            # # print(f'scaled_unit_vector {scaled_unit_vector}')
            AC[2], AC[1] = AC[1], AC[2]
            #
            # projection_point = AC - scaled_unit_vector
            #
            # projection_point[2], projection_point[1] = (
            #     projection_point[1],
            #     projection_point[2],
            # )
            # projection_point = np.array(projection_point).reshape(1, -1)
            # # point1 = point1 - old_origin_optical + new_origin
            # predicted_dest = updated_model.predict(projection_point)
            #
            # new_point=[predicted_dest[0][0],predicted_dest[0][1],predicted_dest[0][2]]

            offset = y_unit * fsize

            # Calculate the new target point
            new_point = point_b_dest.flatten() + 20
            print("new_point:", new_point)
            print("AC:", AC)
            print("offset:", offset)

            if arm.connected and USE_ROBOT:

                arm.motion_enable(enable=True)  # Enable motion
                arm.set_mode(0)  # Set to position control mode
                arm.set_state(0)  # Set to ready state
                code = 0
                code = arm.set_position(
                    x=new_point[0],
                    y=new_point[1],
                    z=new_point[2],
                    roll=90,
                    pitch=flexion + 90,
                    yaw=varus,
                    speed=ROBOT_SPEED,
                    mvacc=60,
                    wait=True,
                )
                if code == 0:
                    print(f"Successfully moved to the desired position! {new_point}")
                else:
                    print(f"Failed to move the arm. Error code: {code}")
                await asyncio.sleep(1)
            return
        except Exception as e:
            print(f"Error {e}")
        return


async def primary_kneeAnteriorChamferCut(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC 1 variables loaded successfully")
        except:
            print("Error loading femur variables")
        omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    initial_data = GetRoboticArmInitialData()
    if USE_ROBOT:
        arm = XArmAPI(arm_ip)
        arm.connect()
    while True:
        try:
            while True:
                if page != "femur-anterior-chamfer-resection.html":
                    return
                latest_frame = await marking.get_latest_frame()
                print(f"test")
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                    print(f"test1")
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:
                    print(f" {len(detections_right)}  {len(detections_left)}")
                    await asyncio.sleep(0.2)
                    continue
            print(f" {len(detections_right)}  {len(detections_left)}")
            # detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
            # detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            three_right = detections_right[0:3]
            three_left = detections_left[0:3]
            three_right = sorted(three_right, key=lambda x: x[0])
            three_left = sorted(three_left, key=lambda x: x[0])
            Pointer = TriangleTracker(
                three_right, three_left, rec_img_right, rec_img_left
            )
            Pointer.find_depth()
            R_points = Pointer.getLEDcordinates()

            if R_points[1][1] >= R_points[0][1]:
                tracker_plane = [R_points[2], R_points[0], R_points[1]]
            else:
                tracker_plane = [R_points[2], R_points[1], R_points[0]]
            print(f"robotTracker   {tracker_plane}")
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            next_three_right = detections_right[3:6]
            next_three_left = detections_left[3:6]
            femure = TriangleTracker(
                first_three_right, first_three_left, rec_img_right, rec_img_left, "F"
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()
            print(f"F_points {F_points}")

            # Tebia = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
            #                           rec_img_left)
            # Tebia.find_depth()
            # pointer_Leds = Tebia.getLEDcordinates()
            # print(f'Tebia   {pointer_Leds}')

            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            # tracker_plane = [R_points[0], R_points[1], R_points[2]]

            new_origin = utils.find_new_point_location(
                old_plane_points=initial_data["robot_tracker_plane"],
                new_plane_points=tuple(tracker_plane),
                old_marked_point=initial_data["old_mark_point"],
            )

            # Step 1: Compute centroid
            centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
            # R_points
            # Step 2: Compute translation vector
            translation_vector = new_origin - centroid

            # Step 3: Translate the plane
            Newtranslated_plane = (
                initial_data["robot_tracker_plane"] + translation_vector
            )

            robot_calib_data = os.path.join(
                current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
            )
            led_points, robot_points = load_data(file_path=robot_calib_data)
            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=initial_data["translated_plane"],
                        new_plane_points=tuple(Newtranslated_plane),
                        old_marked_point=led,
                    )
                    for led in led_points
                ]
            )

            # Swap Y and Z back to match coordinate system
            transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

            updated_model = LinearRegression()
            updated_model.fit(transformed_led_points, robot_points)
            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            LDC = utils.find_new_point_location(
                femur_dict["LDC"]["Plane"], femur_plane, femur_dict["LDC"]["C"]
            )
            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )

            M1 = (FC[1] - HC[1]) / (FC[0] - HC[0])  # DFC_slope represnets M1
            M2 = -1 / M1  # DFC_target slope represnets M2

            B = np.array(femur_plane[1])
            C = np.array(femur_plane[0])
            mid = (B + C) / 2
            distance1 = np.linalg.norm(mid - femur_plane[0])
            scale1 = 107 / distance1  # 107 mm is the reference distance
            cutting_points_scalling = omega / scale1
            direction_vector = HC - FC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling
            # print(f'scaled_unit_vector {scaled_unit_vector}')
            # Calculate the projection point
            # projection_point = LDC + scaled_unit_vector
            # print(f' projection_point {projection_point}')

            Mid_point_RoboTracker = (
                np.array(tracker_plane[1]) + np.array(tracker_plane[2])
            ) / 2

            # Step 2: y vector calculation
            RoboTracker_y = Mid_point_RoboTracker - tracker_plane[0]

            # Step 3: x vector calculation
            RoboTracker_x = np.array(tracker_plane[1]) - np.array(tracker_plane[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            RoboTrackernorm_x = np.linalg.norm(RoboTracker_x)

            RoboTrackernorm_y = np.linalg.norm(RoboTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            RoboTracker_z = np.cross(RoboTracker_y, RoboTracker_x)

            # Step 6: FCS vectors
            x_FCS = FC - HC
            z_FCS = LE - ME

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            def vector3_signed_angle(tracker, ME, LE):
                print(f"*" * 100)
                print(f"tracker {tracker}  ME {ME}  LE {LE}")
                print(f"*" * 100)

                # Step 1: Compute midpoint between point 2 and point 1
                mid_point = (tracker[2] + tracker[1]) / 2.0

                # Step 2: Define LCS axes
                LCS_x = tracker[2] - mid_point
                LCS_y = mid_point - tracker[0]
                LCS_z = np.cross(LCS_x, LCS_y)

                # Normalize the axes
                LCS_x /= np.linalg.norm(LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)
                LCS_z /= np.linalg.norm(LCS_z)

                # Re-orthogonalize LCS_y (optional but safer)
                LCS_y = np.cross(LCS_z, LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)

                # Step 3: Build rotation matrix
                R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

                # Step 4: Translate ME and LE to LCS origin
                ME_translated = ME - mid_point
                LE_translated = LE - mid_point

                # Step 5: Apply rotation (World to Local)
                ME_local = R.T @ ME_translated
                LE_local = R.T @ LE_translated

                # Step 6: Compute ME→LE vector in LCS
                V_local = LE_local - ME_local

                # Step 7: Compute angle in LCS XZ plane
                angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
                angle_deg = int(np.degrees(angle_rad))
                if angle_deg == 0:
                    return angle_deg
                elif angle_deg < 0:
                    angle_deg += 90
                else:
                    angle_deg -= 90

                return angle_deg

            # --------------------------
            # Final angle calculation
            try:
                flexion = vector2_signed_angle_xy(RoboTracker_y, x_FCS)
                # print(f'flexion {flexion}')

                varus = vector3_signed_angle(tracker=tracker_plane, ME=ME, LE=LE)
                # print(f'varus {varus}')

                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error {e}")
                # time.sleep(2)
            LDC[2], LDC[1] = LDC[1], LDC[2]
            projection_point = np.array(LDC).reshape(1, -1)
            # point1 = point1 - old_origin_optical + new_origin
            predicted_dest = updated_model.predict(projection_point)

            print(
                f" projection_point {projection_point} predicted_dest  {predicted_dest} varus ,flexion {varus} {flexion}"
            )
            if arm.connected and USE_ROBOT:

                arm.motion_enable(enable=True)  # Enable motion
                arm.set_mode(0)  # Set to position control mode
                arm.set_state(0)  # Set to ready state
                code = 0
                code = arm.set_position(
                    x=predicted_dest[0][0],
                    y=predicted_dest[0][1],
                    z=predicted_dest[0][2],
                    roll=90,
                    pitch=135 + flexion,
                    yaw=varus,
                    speed=ROBOT_SPEED,
                    mvacc=50,
                    wait=True,
                )
                if code == 0:
                    print("Successfully moved to the desired position!")
                    await asyncio.sleep(1)
                else:
                    print(f"Failed to move the arm. Error code: {code}")
                await asyncio.sleep(1)
            return
        except Exception as e:
            print(f"Error {e}")


async def primary_kneefemur_posterior_chamfer(marking, websocket):
    try:
        await send_tuple(websocket, (0, 0, 0, 0))
        base_dir = os.path.join(current_dir, "..", "..", "registration_data")
        # Femur variables
        pickle_path_femur = os.path.join(base_dir, "femur_variables.pickle")
        with open(pickle_path_femur, "rb") as femur_handle:
            femur_dict = pickle.load(femur_handle)
        print(f" femur_dict {femur_dict}")
        pickle_path_femur_hc = os.path.join(base_dir, "hip_center.pickle")
        try:
            with open(pickle_path_femur_hc, "rb") as handle:
                HC = np.array(pickle.load(handle))
            print("Femur HC variables loaded successfully")
        except:
            print("Error loading femur variables")
        omega = 9  #
    except Exception as e:
        print(f"Error {e}")
    initial_data = GetRoboticArmInitialData()
    if USE_ROBOT:
        arm = XArmAPI(arm_ip)
        arm.connect()
    while True:
        try:
            while True:
                if page != "femur-posterior-chamfer-resection.html":
                    return
                latest_frame = await marking.get_latest_frame()
                if latest_frame:
                    rec_img_left, rec_img_right = latest_frame
                else:
                    return
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 9 and len(detections_left) == 9:
                    print("Detections completed!")
                    await asyncio.sleep(0.1)
                    break  # Terminate loop
                else:
                    await asyncio.sleep(0.2)
                    continue

            # detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
            # detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            three_right = detections_right[0:3]
            three_left = detections_left[0:3]
            three_right = sorted(three_right, key=lambda x: x[0])
            three_left = sorted(three_left, key=lambda x: x[0])
            Pointer = TriangleTracker(
                three_right, three_left, rec_img_right, rec_img_left
            )
            Pointer.find_depth()
            R_points = Pointer.getLEDcordinates()

            if R_points[1][1] >= R_points[0][1]:
                tracker_plane = [R_points[2], R_points[0], R_points[1]]
            else:
                tracker_plane = [R_points[2], R_points[1], R_points[0]]
            # print(f'robotTracker   {tracker_plane}')
            detections_right = detections_right[3:]
            detections_left = detections_left[3:]

            detections_right = sorted(detections_right, key=lambda x: x[0])
            detections_left = sorted(detections_left, key=lambda x: x[0])
            first_three_right = detections_right[:3]
            first_three_left = detections_left[:3]
            next_three_right = detections_right[3:6]
            next_three_left = detections_left[3:6]
            femure = TriangleTracker(
                first_three_right, first_three_left, rec_img_right, rec_img_left, "F"
            )
            femure.find_depth()
            F_points = femure.getLEDcordinates()
            # print(f'F_points {F_points}')

            # Tebia = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
            #                           rec_img_left)
            # Tebia.find_depth()
            # pointer_Leds = Tebia.getLEDcordinates()
            # print(f'Tebia   {pointer_Leds}')

            if F_points[1][1] >= F_points[0][1]:
                femur_plane = [F_points[2], F_points[0], F_points[1]]
            else:
                femur_plane = [F_points[2], F_points[1], F_points[0]]

            # tracker_plane = [R_points[0], R_points[1], R_points[2]]

            new_origin = utils.find_new_point_location(
                old_plane_points=initial_data["robot_tracker_plane"],
                new_plane_points=tuple(tracker_plane),
                old_marked_point=initial_data["old_mark_point"],
            )

            # Step 1: Compute centroid
            centroid = np.mean(initial_data["robot_tracker_plane"], axis=0)
            # R_points
            # Step 2: Compute translation vector
            translation_vector = new_origin - centroid

            # Step 3: Translate the plane
            Newtranslated_plane = (
                initial_data["robot_tracker_plane"] + translation_vector
            )

            robot_calib_data = os.path.join(
                current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
            )
            led_points, robot_points = load_data(file_path=robot_calib_data)
            transformed_led_points = np.array(
                [
                    utils.find_new_point_location(
                        old_plane_points=initial_data["translated_plane"],
                        new_plane_points=tuple(Newtranslated_plane),
                        old_marked_point=led,
                    )
                    for led in led_points
                ]
            )

            # Swap Y and Z back to match coordinate system
            transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]

            updated_model = LinearRegression()
            updated_model.fit(transformed_led_points, robot_points)
            FC = utils.find_new_point_location(
                femur_dict["FC"]["Plane"], femur_plane, femur_dict["FC"]["C"]
            )
            LDC = utils.find_new_point_location(
                femur_dict["LDC"]["Plane"], femur_plane, femur_dict["LDC"]["C"]
            )
            LE = utils.find_new_point_location(
                femur_dict["LE"]["Plane"], femur_plane, femur_dict["LE"]["C"]
            )
            ME = utils.find_new_point_location(
                femur_dict["ME"]["Plane"], femur_plane, femur_dict["ME"]["C"]
            )

            M1 = (FC[1] - HC[1]) / (FC[0] - HC[0])  # DFC_slope represnets M1
            M2 = -1 / M1  # DFC_target slope represnets M2

            B = np.array(femur_plane[1])
            C = np.array(femur_plane[2])
            mid = (B + C) / 2
            distance1 = np.linalg.norm(mid - femur_plane[0])
            scale1 = 107 / distance1  # 107 mm is the reference distance
            cutting_points_scalling = omega / scale1
            direction_vector = HC - FC
            unit_vector = direction_vector / np.linalg.norm(direction_vector)
            scaled_unit_vector = unit_vector * cutting_points_scalling
            # print(f'scaled_unit_vector {scaled_unit_vector}')
            # Calculate the projection point
            # projection_point = LDC + scaled_unit_vector
            # print(f' projection_point {projection_point}')

            Mid_point_RoboTracker = (
                np.array(tracker_plane[1]) + np.array(tracker_plane[2])
            ) / 2

            # Step 2: y vector calculation
            RoboTracker_y = Mid_point_RoboTracker - tracker_plane[0]

            # Step 3: x vector calculation
            RoboTracker_x = np.array(tracker_plane[1]) - np.array(tracker_plane[2])

            # Step 4: Norms (just for debug, we won't use them for cross product)
            RoboTrackernorm_x = np.linalg.norm(RoboTracker_x)

            RoboTrackernorm_y = np.linalg.norm(RoboTracker_y)

            # Step 5: z vector (cross product of y and x vectors)
            RoboTracker_z = np.cross(RoboTracker_y, RoboTracker_x)

            # Step 6: FCS vectors
            x_FCS = FC - HC
            z_FCS = LE - ME

            def vector2_signed_angle_xy(A, B):
                # Create 2D projections on XY plane
                AProjXY = np.array([A[0], A[1]])
                BProjXY = np.array([B[0], B[1]])

                # Calculate signed angle
                cross_z = np.cross(np.append(AProjXY, 0), np.append(BProjXY, 0))[2]
                dot = np.dot(AProjXY, BProjXY)

                angle = np.degrees(np.arctan2(cross_z, dot))
                angle_rounded = np.round(angle * 2) / 2

                return angle_rounded

            def vector3_signed_angle(tracker, ME, LE):
                print(f"*" * 100)
                print(f"tracker {tracker}  ME {ME}  LE {LE}")
                print(f"*" * 100)

                # Step 1: Compute midpoint between point 2 and point 1
                mid_point = (tracker[2] + tracker[1]) / 2.0

                # Step 2: Define LCS axes
                LCS_x = tracker[2] - mid_point
                LCS_y = mid_point - tracker[0]
                LCS_z = np.cross(LCS_x, LCS_y)

                # Normalize the axes
                LCS_x /= np.linalg.norm(LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)
                LCS_z /= np.linalg.norm(LCS_z)

                # Re-orthogonalize LCS_y (optional but safer)
                LCS_y = np.cross(LCS_z, LCS_x)
                LCS_y /= np.linalg.norm(LCS_y)

                # Step 3: Build rotation matrix
                R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

                # Step 4: Translate ME and LE to LCS origin
                ME_translated = ME - mid_point
                LE_translated = LE - mid_point

                # Step 5: Apply rotation (World to Local)
                ME_local = R.T @ ME_translated
                LE_local = R.T @ LE_translated

                # Step 6: Compute ME→LE vector in LCS
                V_local = LE_local - ME_local

                # Step 7: Compute angle in LCS XZ plane
                angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
                angle_deg = int(np.degrees(angle_rad))
                if angle_deg == 0:
                    return angle_deg
                elif angle_deg < 0:
                    angle_deg += 90
                else:
                    angle_deg -= 90

                return angle_deg

            # --------------------------
            # Final angle calculation
            try:
                flexion = vector2_signed_angle_xy(RoboTracker_y, x_FCS)
                # print(f'flexion {flexion}')

                varus = vector3_signed_angle(tracker=tracker_plane, ME=ME, LE=LE)
                # print(f'varus {varus}')

                await asyncio.sleep(1)
            except Exception as e:
                print(f"Error {e}")
                time.sleep(2)
            LDC[2], LDC[1] = LDC[1], LDC[2]
            projection_point = np.array(LDC).reshape(1, -1)
            # point1 = point1 - old_origin_optical + new_origin
            predicted_dest = updated_model.predict(projection_point)

            print(
                f" projection_point {projection_point} predicted_dest  {predicted_dest} varus ,flexion {varus} {flexion}"
            )
            if arm.connected and USE_ROBOT:

                arm.motion_enable(enable=True)  # Enable motion
                arm.set_mode(0)  # Set to position control mode
                arm.set_state(0)  # Set to ready state
                code = arm.set_position(
                    x=predicted_dest[0][0],
                    y=predicted_dest[0][1],
                    z=predicted_dest[0][2],
                    roll=90,
                    pitch=45 + flexion,
                    yaw=varus,
                    speed=ROBOT_SPEED,
                    mvacc=50,
                    wait=True,
                )
                if code == 0:
                    print("Successfully moved to the desired position!")
                    await asyncio.sleep(1)
                else:
                    print(f"Failed to move the arm. Error code: {code}")
                await asyncio.sleep(1)
            return
        except Exception as e:
            print(f"Error {e}")


async def planning_screen(websocket):
    while True:
        try:
            data = await websocket.receive_text()  # Receive WebSocket data as text
            data = json.loads(data)  # Parse JSON

            input_id = data.get("inputId")
            input_value = data.get("inputValue")

            SetplanningInputs(filename=str(input_id), data=str(input_value))
            print(f"Received ID: {input_id}, Value: {input_value}")
            await asyncio.sleep(0.1)

        except json.JSONDecodeError:
            print("Error: Received invalid JSON")
        except Exception as e:
            print(f"WebSocket error: {e}")
            return


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket handler to process different pipelines based on the page."""
    await websocket.accept()

    # if marking.clients:
    #     old_client = marking.clients.pop()
    #     await old_client.close()
    #     print("Previous client disconnected")
    marking.clients.add(websocket)
    global page  # Track which page the client is on
    results = None
    try:
        while True:

            # Check if the client has sent a message indicating the page
            message = await websocket.receive_text()

            try:
                data = json.loads(message)
                if "file" in data:
                    async with page_lock:  # Ensure thread safety
                        page = data["file"]
                    print(f"Client is on {page}")
                else:
                    continue  # Skip further processing until we have a fra
            except json.JSONDecodeError:
                print("Invalid JSON received")

            if not marking.frame_queue.empty():
                if page == "pelvis-registration-2.html":
                    await hip_pelvis_registration_2(marking, websocket)
                elif page == "handle-position.html":
                    await hip_handle_position(marking, websocket)
                elif page == "final-cup-position.html":
                    await hip_final_cup_position(marking, websocket)
                elif page == "hipRingPointCollection.html":
                    await hipCollectRingPointCloudMarkingPipeline(marking, websocket)
                elif page == "hipFreePointCollection.html":
                    await hipFreePointCollection(marking, websocket, rev=None)
                elif page == "revhipFreePointCollection.html":
                    await hipFreePointCollection(marking, websocket, rev="rev")
                elif page == "register-acetabulum.html":
                    await send_tuple(websocket, ("STEP0", 1, 3, 0))
                    await register_acetabulum(marking, websocket)
                elif page == "mid-axial-body-plane.html":
                    await send_tuple(websocket, ("NOTE0", 1, 3, 0))
                    await hipmidAxialBodyPlane(marking, websocket)
                elif page == "revmid-axial-body-plane.html":
                    await send_tuple(websocket, ("NOTE0", 1, 3, 0))
                    await hipmidAxialBodyPlane(marking, websocket)
                elif page == "revregister-acetabulum.html":
                    await rev_register_acetabulum(marking, websocket)
                elif page == "acetabulum-component-placement.html":
                    await send_tuple(websocket, ("STEP0", 1, 3, 0))
                    await register_acetabulum(marking, websocket, "postoperation")
                elif page == "system-setup.html":
                    await pointer_femur_pipeline(marking, websocket)
                elif page == "femur-registration.html":
                    await hip_tp_variable_Marking(marking, websocket, point=None)
                elif page == "TP2_marking.html":
                    await hip_tp_variable_Marking(marking, websocket, point="TP2")
                elif page == "revTP2_marking.html":
                    await hip_tp_variable_Marking(marking, websocket, point="TP2")
                elif page == "revfinal-cup-position.html":
                    await revfinal_cup_position(marking, websocket)
                #################################### Primary Knee #############################################
                elif page == "multiple-page2.html":
                    await pivoting_procedure(marking, websocket)
                    await FemureMarking(marking, websocket)
                elif page == "multiple-page.html":
                    await Tebia_marking_tc(marking, websocket)
                    await Tebia_marking(marking, websocket)
                elif page == "tkr-screen-2.html":
                    await PrimaryKneeLandMarkVerification(marking, websocket)
                elif page == "inner-page6.html":
                    await AlignmentAngles_Knee(marking, websocket)
                elif (
                        page == "tkr-screen-3.html"
                        or page == "revguidance-proximal-tibia-cut.html"
                        or page == "revguidance-distal-femur-cut.html"
                ):
                    await planning_screen(websocket)
                elif page == "robot_position.html":
                    await primary_robot_position(marking, websocket)
                elif page == "femur-distal-femur-cut.html":
                    await primary_kneeDistalFemureCut(marking, websocket)
                elif page == "femur-inner-page5.html":
                    await DistalFemureCutVerification(marking, websocket)
                elif page == "femur-inner-page2.html":
                    await primary_kneeTibiaCut(marking, websocket)
                elif page == "tibia-inner-page3.html":
                    await TibiaCutVerification(marking, websocket)
                elif page == "femur-anterior-resection.html":
                    await primary_kneeAnteriorCut(marking, websocket)
                elif page == "femur-anterior-chamfer-resection.html":
                    await primary_kneeAnteriorChamferCut(marking, websocket)
                elif page == "femur-posterior-resection.html":
                    await primary_kneefemur_posterior(marking, websocket)
                elif page == "femur-posterior-chamfer-resection.html":
                    await primary_kneefemur_posterior_chamfer(marking, websocket)
                # #################################### Revision Knee #############################################
                elif page == "revmultiple-page2.html":
                    await pivoting_procedure(marking, websocket)
                    await FemureMarking(marking, websocket)
                elif page == "revmultiple-page.html":
                    await Tebia_marking_tc(marking, websocket)
                    await Tebia_marking(marking, websocket)
                elif page == "revtkr-screen-2.html":
                    await PrimaryKneeLandMarkVerification(marking, websocket)
                elif (
                    page == "revinner-page6.html"
                    or page == "revgraph-screen.html"
                    or page == "femur-graph-screen.html"
                ):
                    await AlignmentAngles_Knee(marking, websocket)
                elif page == "revml-size-acquisition.html":
                    await ml_size_acquisition(marking, websocket)
                elif page == "revFree_point_collection_femur.html":
                    await revkneeFree_point_collection_femur(marking, websocket)
                elif page == "revFree_point_collectionTibia.html":
                    await revFree_point_collectionTibia(marking, websocket)
                elif page == "revtibia-im-canal-ream.html":
                    await revtibia_im_canal_ream(marking, websocket)
                elif page == "revfemur-im-canal-ream.html":
                    await revfemur_im_canal_ream(marking, websocket)
                elif page == "revrevision-knee-burr.html":
                    await revrevisionKneeTibiaCut(marking, websocket)
                elif page == "revverification-proximal-tibia-cut.html":
                    await revverificationTibiaCut(marking, websocket)
                elif page == "revverification-distal-femur-cut.html":
                    await revverificationdistalFemure(marking, websocket)
                elif page == "revrevision-knee-burrFemur.html":
                    await revDistalFemurCut(marking, websocket)
                #################################### Uni Knee #############################################
                elif page == "unimultiple-page2.html":
                    await pivoting_procedure(marking, websocket)
                    await FemureMarkingUni(marking, websocket)
                elif page == "uniFree_point_collection_femur.html":
                    await uniFree_point_collection_femur(marking, websocket)
                elif page == "unimultiple-page.html":
                    await UniTebia_marking_tc(marking, websocket)
                    await UniTebia_marking(marking, websocket)
                elif page == "uniFree_point_collectionTibia.html":
                    await uniFree_point_collectionTibia(marking, websocket)
                elif page == "unifemur-distal-femur-cut.html":
                    await uniKneeDistalFemureCut(marking, websocket)
                elif page == "uniinner-page6.html":
                    await AlignmentAnglesUni(marking, websocket)
                elif page == "unitkr-screen-2.html":
                    await unitkr_screen_2(marking, websocket)
                elif page == "uniaccp_femure_cut.html":
                    await accp_femure_cut(marking, websocket)
                elif page == "unitibia-cut.html":
                    await unitibia_cut(marking, websocket)
                elif page == "video":
                    await stream_video(marking, websocket)

            await asyncio.sleep(0.1)

    except WebSocketDisconnect:
        print("Client disconnected")
    except RuntimeError as e:
        print(f"RuntimeError: {e}")
    finally:
        # if websocket in marking.clients:
        marking.clients.discard(websocket)
