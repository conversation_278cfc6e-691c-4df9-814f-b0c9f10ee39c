const imagesByColor = {
    yellow: [
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/YELLOW/TIBIA_PARTIAL_KNEE_PER_1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/YELLOW/TIBIA_PARTIAL_KNEE_PER_2.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/YELLOW/TIBIA_PARTIAL_KNEE_PER_3.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/YELLOW/TIBIA_PARTIAL_KNEE_PER_4.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/YELLOW/TIBIA_PARTIAL_KNEE_PER_5.jpg',
    ],
    yellow1: [
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/YELLOW/TIBIA_PARTIAL_KNEE_TOP_1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/YELLOW/TIBIA_PARTIAL_KNEE_TOP_2.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/YELLOW/TIBIA_PARTIAL_KNEE_TOP_3.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/YELLOW/TIBIA_PARTIAL_KNEE_TOP_4.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/YELLOW/TIBIA_PARTIAL_KNEE_TOP_5.jpg',
    ],
	
    red: [
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/RED/TIBIA_PARTIAL_KNEE_PER_1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/RED/TIBIA_PARTIAL_KNEE_PER_2.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/RED/TIBIA_PARTIAL_KNEE_PER_3.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/RED/TIBIA_PARTIAL_KNEE_PER_4.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/RED/TIBIA_PARTIAL_KNEE_PER_5.jpg',
    ],
    red1: [
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/RED/TIBIA_PARTIAL_KNEE_TOP_1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/RED/TIBIA_PARTIAL_KNEE_TOP_2.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/RED/TIBIA_PARTIAL_KNEE_TOP_3.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/RED/TIBIA_PARTIAL_KNEE_TOP_4.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/RED/TIBIA_PARTIAL_KNEE_TOP_5.jpg',
    ],
	
    green: [
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/GREEN/TIBIA_PARTIAL_KNEE_PER_1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/GREEN/TIBIA_PARTIAL_KNEE_PER_2.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/GREEN/TIBIA_PARTIAL_KNEE_PER_3.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/GREEN/TIBIA_PARTIAL_KNEE_PER_4.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/GREEN/TIBIA_PARTIAL_KNEE_PER_5.jpg',
    ],
    green1: [
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/GREEN/TIBIA_PARTIAL_KNEE_TOP_1.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/GREEN/TIBIA_PARTIAL_KNEE_TOP_2.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/GREEN/TIBIA_PARTIAL_KNEE_TOP_3.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/GREEN/TIBIA_PARTIAL_KNEE_TOP_4.jpg',
        '../static/images/NEWLAYERGUIKNEE/UNIKNEE/TIBIA/GREEN/TIBIA_PARTIAL_KNEE_TOP_5.jpg',
    ]
};

function startImageAnimation(color) {
    const images = imagesByColor[color];
    let index = 0;
    const imgElement = document.getElementById(`animated-image${color.charAt(0).toUpperCase() + color.slice(1)}`);

    setInterval(() => {
        imgElement.src = images[index];
        index = (index + 1) % images.length; // Loop back to the start
    }, 1000); // Change image every 1 second
}

const colors = ['yellow', 'red', 'green'];

colors.forEach(color => {
    startImageAnimation(color);
    startImageAnimation(`${color}1`); // Start animation for color1 as well
});
