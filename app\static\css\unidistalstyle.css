body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
    background-color: #f0f0f0;
}

.animation-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 426;  /* Adjust this width as necessary */
    height: 260; /* Adjust this height as necessary */
    overflow: hidden; /* Hide any overflow */
    margin: 0 auto;  /* Center the container */
    position: relative; /* Ensure positioning for child elements */
}

#animated-image {
    width: 426%;  /* Set the desired width */
    height: 260%; /* Set the desired height */
    /*object-fit: cover; Maintain aspect ratio and cover the area */
}

.sprite {
    width: 100%; /* 5 images, so width is 5 times one image */
    height: 100%;
    background-size: 100% 100%; /* Adjust based on your image size */
    animation: play 1s steps(4) infinite; /* Adjust to play through images */
}

/* Combine background images for each color */
.sprite.yellow {
    background-image: url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_2.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_3.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_4.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_5.jpg');
}

.sprite.yellow1 {
    background-image: url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_1-1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_2-1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_3-1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_4-1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/YELLOW/PG_7_ME_Yellow_5-1.jpg');
}

.sprite.red {
    background-image: url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_2.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_3.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_4.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_5.jpg');
}

.sprite.red1 {
    background-image: url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_1-1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_2-1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_3-1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_4-1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/RED/PG_7_ME_Red_5-1.jpg');
}

.sprite.green {
    background-image: url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_2.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_3.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_4.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_5.jpg');
}


.sprite.green1 {
    background-image: url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_1-1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_2-1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_3-1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_4-1.jpg'), 
                      url('../images/NEWLAYERGUIKNEE/UNIKNEE/FEMUR/GREEN/PG_7_ME_Green_5-1.jpg');
}

@keyframes play {
    0%, 20% { background-position: 0%; }
    40% { background-position: -100%; }
    60% { background-position: -200%; }
    80% { background-position: -300%; }
    100% { background-position: -400%; }
}
