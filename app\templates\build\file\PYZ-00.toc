('D:\\SpineSurgery\\pythonProject\\app\\templates\\build\\file\\PYZ-00.pyz',
 [('PyQt5',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('_compat_pickle', 'D:\\anaconda3\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\anaconda3\\Lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\anaconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\anaconda3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime', 'D:\\anaconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\anaconda3\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\anaconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\anaconda3\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\anaconda3\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\anaconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\anaconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\anaconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('contextlib', 'D:\\anaconda3\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\anaconda3\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\anaconda3\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\anaconda3\\Lib\\csv.py', 'PYMODULE'),
  ('dataclasses', 'D:\\anaconda3\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\anaconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\anaconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\anaconda3\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\anaconda3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\anaconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\anaconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\anaconda3\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\anaconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'D:\\anaconda3\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\anaconda3\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\anaconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\anaconda3\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\anaconda3\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\anaconda3\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\anaconda3\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\anaconda3\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\anaconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\anaconda3\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\anaconda3\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\anaconda3\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\anaconda3\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\anaconda3\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\anaconda3\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\anaconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\anaconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'D:\\anaconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\anaconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\anaconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\anaconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('importlib', 'D:\\anaconda3\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\anaconda3\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\anaconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\anaconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\anaconda3\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\anaconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\anaconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\anaconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\anaconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\anaconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\anaconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\anaconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\anaconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\anaconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\anaconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\anaconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\anaconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\anaconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\anaconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\anaconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\anaconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\anaconda3\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\anaconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\anaconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('logging', 'D:\\anaconda3\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\anaconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'D:\\anaconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\anaconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\anaconda3\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\anaconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\anaconda3\\Lib\\pkgutil.py', 'PYMODULE'),
  ('pprint', 'D:\\anaconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\anaconda3\\Lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'D:\\anaconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\anaconda3\\Lib\\random.py', 'PYMODULE'),
  ('selectors', 'D:\\anaconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'D:\\anaconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\anaconda3\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\anaconda3\\Lib\\socket.py', 'PYMODULE'),
  ('statistics', 'D:\\anaconda3\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\anaconda3\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\anaconda3\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\anaconda3\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'D:\\anaconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\anaconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\anaconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\anaconda3\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\anaconda3\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\anaconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\anaconda3\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\anaconda3\\Lib\\typing.py', 'PYMODULE'),
  ('urllib', 'D:\\anaconda3\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\anaconda3\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('zipfile', 'D:\\anaconda3\\Lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\anaconda3\\Lib\\zipimport.py', 'PYMODULE')])
