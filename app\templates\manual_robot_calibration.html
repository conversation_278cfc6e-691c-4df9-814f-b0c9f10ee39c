<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Robot Calibration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .status-box {
            width: 100%;
            min-height: 60px;
            border: 1px solid #ccc;
            padding: 10px;
            margin: 20px 0;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .connected {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .button-container {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 5px;
            border: none;
        }
        #startCalibration {
            background-color: #007bff;
            color: white;
        }
        #capturePoint {
            background-color: #28a745;
            color: white;
            display: none;
        }
        #finishCalibration {
            background-color: #dc3545;
            color: white;
            display: none;
        }
        .instructions {
            background-color: #e2f3f8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .position-indicator {
            display: flex;
            margin: 20px 0;
            width: 100%;
            justify-content: space-between;
        }
        .position {
            width: 22%;
            text-align: center;
            padding: 10px 0;
            border-radius: 5px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
        }
        .position.active {
            background-color: #d4edda;
            border-color: #c3e6cb;
            font-weight: bold;
        }
        .position.completed {
            background-color: #cce5ff;
            border-color: #b8daff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Manual Robot Calibration</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Click "Start Calibration" to begin</li>
                <li>Manually move the robot to Position 1 (Front)</li>
                <li>When the robot is stationary, click "Capture Position"</li>
                <li>Repeat for all 4 positions as instructed</li>
            </ol>
            <p><strong>Note:</strong> Ensure the LED on the robot's TCP is visible to both cameras at all times.</p>
        </div>
        
        <div class="position-indicator">
            <div class="position" id="pos1">Position 1<br>(Front)</div>
            <div class="position" id="pos2">Position 2<br>(Left)</div>
            <div class="position" id="pos3">Position 3<br>(Right)</div>
            <div class="position" id="pos4">Position 4<br>(Top)</div>
        </div>
        
        <div class="button-container">
            <button id="startCalibration">Start Calibration</button>
            <button id="capturePoint">Capture Position</button>
            <button id="finishCalibration">Finish Early</button>
        </div>
        
        <div class="status-box" id="status">Ready to start calibration</div>
    </div>

    <script>
        let socket;
        let pointsCollected = 0;
        const totalPositions = 4;
        
        document.getElementById('startCalibration').addEventListener('click', function() {
            connectWebSocket();
            this.disabled = true;
            document.getElementById('capturePoint').style.display = 'block';
            document.getElementById('finishCalibration').style.display = 'block';
            document.getElementById('pos1').classList.add('active');
        });
        
        document.getElementById('capturePoint').addEventListener('click', function() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({"action": "capture"}));
                document.getElementById('status').textContent = 'Capturing position...';
                this.disabled = true;
                setTimeout(() => {
                    this.disabled = false;
                }, 2000);
            }
        });
        
        document.getElementById('finishCalibration').addEventListener('click', function() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({"action": "finish"}));
                document.getElementById('status').textContent = 'Finishing calibration...';
                this.disabled = true;
                document.getElementById('capturePoint').disabled = true;
            }
        });
        
        function updatePositionIndicator(position) {
            // Mark current position as completed
            if (position > 0) {
                document.getElementById(`pos${position}`).classList.remove('active');
                document.getElementById(`pos${position}`).classList.add('completed');
            }
            
            // Mark next position as active
            if (position < totalPositions) {
                document.getElementById(`pos${position + 1}`).classList.add('active');
            }
        }
        
        function connectWebSocket() {
            document.getElementById('status').textContent = 'Connecting...';
            document.getElementById('status').className = 'status-box';
            
            socket = new WebSocket('ws://127.0.0.1:8000/ws');
            
            socket.onopen = function(e) {
                document.getElementById('status').textContent = 'Connected';
                document.getElementById('status').className = 'status-box connected';
                socket.send(JSON.stringify({"file": "manual_robot_calibration"}));
                document.getElementById('status').textContent = 'Manual calibration started. Move the robot to Position 1 (Front) and click "Capture Position".';
            };
            
            socket.onmessage = function(event) {
                const message = event.data;
                document.getElementById('status').textContent = message;
                
                // Check if the message indicates a successful position capture
                if (message.includes("Position") && message.includes("captured successfully")) {
                    pointsCollected++;
                    updatePositionIndicator(pointsCollected);
                }
                
                // Check if calibration is complete
                if (message.includes("Calibration complete")) {
                    document.getElementById('capturePoint').disabled = true;
                    document.getElementById('finishCalibration').disabled = true;
                    document.getElementById('startCalibration').disabled = false;
                }
                
                console.log("Message from server:", message);
            };
            
            socket.onclose = function(event) {
                if (event.wasClean) {
                    document.getElementById('status').textContent = `Connection closed cleanly, code=${event.code} reason=${event.reason}`;
                } else {
                    document.getElementById('status').textContent = 'Connection died';
                    document.getElementById('status').className = 'status-box error';
                }
                
                document.getElementById('startCalibration').disabled = false;
                document.getElementById('capturePoint').style.display = 'none';
                document.getElementById('finishCalibration').style.display = 'none';
                
                // Reset position indicators
                for (let i = 1; i <= totalPositions; i++) {
                    document.getElementById(`pos${i}`).classList.remove('active', 'completed');
                }
            };
            
            socket.onerror = function(error) {
                document.getElementById('status').textContent = `Error: ${error.message}`;
                document.getElementById('status').className = 'status-box error';
            };
        }
    </script>
</body>
</html>