# # import numpy as np
# #
# # # Input points
# # pointA = np.array([560.4995447417572,463.48750499327315,293.27758516149925])
# # pointB = np.array([545.8464330977317,463.1413932167414,291.44065039121676])
# # pointC = np.array([545.9994818240366,448.6019008431797,297.76511438140534])  /origin
# # ME = np.array([554.9607806232505,441.67841834668843,267.9819521437248])
# # LE = np.array([554.4008073480967,444.77458050225744,281.05911532075646])
# #
# #
# #
# # # Compute LCS axes
# # LCS_y = pointC - pointB
# # LCS_x = pointB - pointA
# # LCS_z = np.cross(LCS_x, LCS_y)
# #
# # # Normalize axes
# # LCS_x /= np.linalg.norm(LCS_x)
# # LCS_y /= np.linalg.norm(LCS_y)
# # LCS_z /= np.linalg.norm(LCS_z)
# #
# # # Vector from ME to LE
# # # V = LE - ME
# # V = ME - LE
# #
# # # Project V onto LCS axes
# # proj_x = np.dot(V, LCS_x)
# # proj_y = np.dot(V, LCS_y)  # not used
# # proj_z = np.dot(V, LCS_z)
# #
# # # Compute angle in XZ plane
# # angle_rad = np.arctan2(proj_z, proj_x)
# # angle_deg = np.degrees(angle_rad)
# #
# # print(f"Angle of ME→LE vector on LCS XZ plane relative to LCS_x: {angle_deg:.2f} degrees")
#
#
#
# import numpy as np
#
# # Input points in camera coordinate system
# pointA = np.array([560.4995447417572, 463.48750499327315, 293.27758516149925])
# pointB = np.array([545.8464330977317, 463.1413932167414, 291.44065039121676])  # Origin of LCS
# pointC = np.array([545.9994818240366, 448.6019008431797, 297.76511438140534])
# ME = np.array([554.9607806232505, 441.67841834668843, 267.9819521437248])
# LE = np.array([554.4008073480967, 444.77458050225744, 281.05911532075646])
#
# # Step 1: Compute LCS axes
# LCS_y = pointB - pointA
# LCS_x = pointC - pointB
# LCS_z = np.cross(LCS_x, LCS_y)
#
# # Normalize axes
# LCS_x /= np.linalg.norm(LCS_x)
# LCS_y /= np.linalg.norm(LCS_y)
# LCS_z /= np.linalg.norm(LCS_z)
#
# # Step 2: Build rotation matrix (3x3)
# R = np.column_stack((LCS_x, LCS_y, LCS_z))  # [LCS_x | LCS_y | LCS_z]
#
# # Step 3: Translate ME and LE to origin (pointA)
# ME_translated = ME - pointB
# LE_translated = LE - pointB
#
# # Step 4: Transform to LCS frame (apply rotation)
# ME_local = R.T @ ME_translated
# LE_local = R.T @ LE_translated
#
# # Step 5: Compute vector in LCS frame
# V_local = ME_local - LE_local
#
# # Step 6: Compute angle in LCS XZ plane
# angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
# angle_deg = np.degrees(angle_rad)
#
# print(f"Angle of ME→LE vector on LCS XZ plane relative to LCS_x: {angle_deg:.2f} degrees")



import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# --------- Utility function for normalization ----------
def normalize(v):
    norm = np.linalg.norm(v)
    return v / norm if norm != 0 else v

# --------- Step 1: Input Points ----------
pointA = np.array([560.4995447417572, 463.48750499327315, 293.27758516149925])
pointB = np.array([545.8464330977317, 463.1413932167414, 291.44065039121676])  # LCS Origin
pointC = np.array([545.9994818240366, 448.6019008431797, 297.76511438140534])
ME = np.array([554.9607806232505, 441.67841834668843, 267.9819521437248])
LE = np.array([554.4008073480967, 444.77458050225744, 281.05911532075646])

# --------- Step 2: Compute Local Coordinate System (LCS) Axes ----------
LCS_x = pointC - pointB
LCS_y = pointA - pointB
LCS_z = np.cross(LCS_x, LCS_y)

# Normalize the axes
LCS_x = normalize(LCS_x)
LCS_y = normalize(LCS_y)
LCS_z = normalize(LCS_z)

# Re-orthogonalize Y to ensure orthogonality (optional, but good practice)
LCS_y = np.cross(LCS_z, LCS_x)
LCS_y = normalize(LCS_y)

# --------- Step 3: Build Rotation Matrix ---------
R = np.column_stack((LCS_x, LCS_y, LCS_z))  # Columns are LCS axes

# --------- Step 4: Translate ME and LE to LCS Origin ----------
ME_translated = ME - pointB
LE_translated = LE - pointB

# --------- Step 5: Apply rotation (World to Local) ----------
ME_local = R.T @ ME_translated
LE_local = R.T @ LE_translated

# --------- Step 6: Compute ME→LE vector in LCS ----------
V_local = LE_local - ME_local

# --------- Step 7: Compute angle in LCS XZ plane ----------
angle_rad = np.arctan2(V_local[2], V_local[0])  # Z vs X
angle_deg = np.degrees(angle_rad)

print(f"Angle of ME→LE vector on LCS XZ plane relative to LCS_x: {angle_deg:.2f} degrees")

# --------- Step 8: Visualization ----------
fig = plt.figure()
ax = fig.add_subplot(111, projection='3d')

# Plot LCS origin
ax.scatter(0, 0, 0, color='black', label='LCS Origin')

# Plot LCS axes
scale = 1
ax.quiver(0, 0, 0, LCS_x[0], LCS_x[1], LCS_x[2], color='red', length=scale, label='LCS_x')
ax.quiver(0, 0, 0, LCS_y[0], LCS_y[1], LCS_y[2], color='green', length=scale, label='LCS_y')
ax.quiver(0, 0, 0, LCS_z[0], LCS_z[1], LCS_z[2], color='blue', length=scale, label='LCS_z')

# Plot ME and LE in local frame
ax.scatter(ME_local[0], ME_local[1], ME_local[2], color='orange', label='ME')
ax.scatter(LE_local[0], LE_local[1], LE_local[2], color='purple', label='LE')

# Plot ME→LE vector
ax.plot([ME_local[0], LE_local[0]], [ME_local[1], LE_local[1]], [ME_local[2], LE_local[2]],
        color='magenta', label='ME→LE')

# Labels
ax.set_xlabel('LCS X')
ax.set_ylabel('LCS Y')
ax.set_zlabel('LCS Z')
ax.legend()
ax.set_title(f"ME→LE Angle in LCS XZ plane: {angle_deg:.2f}°")

plt.show()
