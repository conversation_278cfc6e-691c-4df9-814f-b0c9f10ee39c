import numpy as np
import time

from pythonProject.general.common.ReadSerialData import read_encoder_data

point1 = np.array([538.66843039, 512.92515387, 258.89817359])
point2 = np.array([542.65836499, 512.93034537, 259.00216237])

# Calculate the midpoint
pointA = (point1 + point2) / 2

pointB = np.array([540.57753906, 507.17568289, 259.95490083])

# Calculate the scale
calculated_distance = np.linalg.norm(pointA - pointB)
real_life_distance = 104  # in mm

# Check the scale
scale = real_life_distance / calculated_distance
'''
# Define the DH parameters (theta, alpha, a, d)
d_h_table = np.array([
    [np.deg2rad(0), np.deg2rad(90), 0, 137.5 / scale],  # For joint 1
    [np.deg2rad(0), np.deg2rad(90), 112 / scale, 0],  # For joint 2
    # [0, np.deg2rad(90), 60.15 / scale, 127.11575197433243 / scale],  # For joint 2
    [np.deg2rad(0), np.deg2rad(-90), 0, 265.85 / scale],  # For joint 3
    [np.deg2rad(0), np.deg2rad(90), 0, (76+88.5) / scale],  # For joint 4
    [np.deg2rad(0), np.deg2rad(0), 229.5 / scale, 0],  # For joint 5
    [np.deg2rad(0), np.deg2rad(0), 0, 102.5 / scale]  # For joint 6
])
# Define the DH parameters (theta, alpha, a, d)






# [542.41972351 494.58292976 259.05657194]
# d_h_table = np.array([
#     [0, 0, 0,0],  # For joint 1
#     [0, np.deg2rad(90), 0, 137.5 / scale],  # For joint 1
#     [0, np.deg2rad(90), 112/scale, 205.7+60 / scale],  # For joint 2
#     [0, np.deg2rad(90), 0, 0],  # For joint 3
#     [0, np.deg2rad(90), 0, 76 / scale],  # For joint 4
#     [0, np.deg2rad(0), 229.5 / scale, 0],  # For joint 5
#     [0, np.deg2rad(0), 0, 102.5 / scale]  # For joint 6
# ])

# d_h_table = np.array([
#     [0, np.deg2rad(90), 137.5 / scale, 0],  # For joint 1
#     [0, np.deg2rad(90), 60.15 / scale, 112 / scale],  # For joint 2
#     [0, np.deg2rad(90), 205.7 / scale, 0],  # For joint 3
#     [0, np.deg2rad(90), 76 / scale, 0],  # For joint 4
#     [0, np.deg2rad(0), 0, 229.5 / scale],  # For joint 5
#     [0, np.deg2rad(0), 102.5 / scale, 0]  # For joint 6
# ])


# Function to compute the homogeneous transformation matrix
def compute_homogeneous_matrix(theta, alpha, a, d):
    return np.array([
        [np.cos(theta), -np.sin(theta) * np.cos(alpha), np.sin(theta) * np.sin(alpha), a * np.cos(theta)],
        [np.sin(theta), np.cos(theta) * np.cos(alpha), -np.cos(theta) * np.sin(alpha), a * np.sin(theta)],
        [0, np.sin(alpha), np.cos(alpha), d],
        [0, 0, 0, 1]
    ])


# Function to calculate end effector position based on updated joint angles
def calculate_end_effector_position(theta_angles):
    # theta_angles.insert(0, 0)
    # Update the DH table with the new joint angles (theta values)
    for i in range(len(theta_angles)):
        d_h_table[i, 0] = theta_angles[i]  # Update theta (joint angles)

    # Compute the transformation matrices for each joint (frame to frame)
    homgen_0_1 = compute_homogeneous_matrix(d_h_table[0, 0], d_h_table[0, 1], d_h_table[0, 2], d_h_table[0, 3])
    homgen_1_2 = compute_homogeneous_matrix(d_h_table[1, 0], d_h_table[1, 1], d_h_table[1, 2], d_h_table[1, 3])
    homgen_2_3 = compute_homogeneous_matrix(d_h_table[2, 0], d_h_table[2, 1], d_h_table[2, 2], d_h_table[2, 3])
    homgen_3_4 = compute_homogeneous_matrix(d_h_table[3, 0], d_h_table[3, 1], d_h_table[3, 2], d_h_table[3, 3])
    homgen_4_5 = compute_homogeneous_matrix(d_h_table[4, 0], d_h_table[4, 1], d_h_table[4, 2], d_h_table[4, 3])
    homgen_5_6 = compute_homogeneous_matrix(d_h_table[5, 0], d_h_table[5, 1], d_h_table[5, 2], d_h_table[5, 3])
    # homgen_6_7 = compute_homogeneous_matrix(d_h_table[6, 0], d_h_table[6, 1], d_h_table[6, 2], d_h_table[6, 3])

    # Combine the transformation matrices to get the final transformation matrix (frame 0 to frame 5)
    homgen_0_7 = homgen_0_1 @ homgen_1_2 @ homgen_2_3 @ homgen_3_4 @ homgen_4_5 @ homgen_5_6

    # Extract the position of the end effector (translation part)
    end_effector_position = homgen_0_7[:3, 3]  # The first three elements of the last column

    return end_effector_position



# Main loop to continuously read encoder data and calculate end effector position
def main_loop():
    initial_position = calculate_end_effector_position([0, 0, 0, 0, 0, 0])  # Initial position when all joints are 0
    # print("Initial End Effector Position (theta = 0):")
    print(initial_position)
    base_position = np.array([528.28843338, 481.55731928, 261.24525447])  # Example base position

    while True:

        # d_h_table = np.array([
        #     [0, np.deg2rad(90), 0, 137.5 / scale],  # For joint 1
        #     [0, np.deg2rad(90), 127.11575197433243 / scale, -60.15 / scale],  # For joint 2
        #     # [0, np.deg2rad(90), 60.15 / scale, 127.11575197433243 / scale],  # For joint 2
        #     [0, np.deg2rad(-90), 0, 205.7 / scale],  # For joint 3
        #     [0, np.deg2rad(90), 0, 76 / scale],  # For joint 4
        #     [0, np.deg2rad(0), 229.5 / scale, 0],  # For joint 5
        #     [0, np.deg2rad(0), 0, 102.5 / scale]  # For joint 6
        # ])

        # Simulate reading encoder data
        data = read_encoder_data()
        for key in ['6', '2', '3']:
            data[key] = -data[key]
        # data['4'] = -data['1']
        # [540.9900782  479.94785676 251.47918559]
        if data:
            data['4'], data['1'] = data['1'], data['4']
            # data['1'] = -data['1']
            # Create the tuple and update the dictionary based on sorted values
            # print(f'init data {data}')
            swapped_data = {
                '1': data['1'],  # Key 4 becomes key 1
                '2': data['4'],  # Key 1 becomes key 2
                '3': data['2'],  # Key 2 becomes key 3
                '4': data['3'],  # Key 3 becomes key 4
                # Keep other keys unchanged
                '5': data['5'],
                '6': data['6']
            }
            # print(f'sorted data {sorted(swapped_data.items())}')
            # if '4' in swapped_data:
            #     del swapped_data['4']
            # if '5' in swapped_data:
            #     del swapped_data['5']
            # if '6' in swapped_data:
            #     del swapped_data['6']
            data = swapped_data
            # data['1'] = -data['1']

            # joint_angles = [np.deg2rad(angle) for angle in data.values()]

            print(f'sorted data.values() {data.values()}')
            # Convert the joint angles (theta) from degrees to radians
            theta_angles = [np.deg2rad(angle) for angle in data.values()]
            print(f'theta_angles {theta_angles}')
            # Calculate the end effector position based on updated joint angles
            end_effector_position = calculate_end_effector_position(theta_angles)
            # end_effector_position += base_position

            # Print the updated end effector position
            # print("\nUpdated End Effector Position (with updated theta):")
            print(f' end_effector_position {end_effector_position}  {end_effector_position - initial_position}')
            # Pause before reading new data (simulate real-time updates)
            time.sleep(2)  # Update every 1 second
            # break

'''

# Run the main loop
# main_loop()
#
#


def test():
    point1 = np.array([538.66843039, 512.92515387, 258.89817359])
    point2 = np.array([542.65836499, 512.93034537, 259.00216237])

    # Calculate the midpoint
    pointA = (point1 + point2) / 2

    pointB = np.array([540.57753906, 507.17568289, 259.95490083])

    # Calculate the scale
    calculated_distance = np.linalg.norm(pointA - pointB)
    real_life_distance = 104  # in mm

    # Check the scale
    scale = real_life_distance / calculated_distance

    def dh_transform(theta, d, a, alpha):
        """
        Compute the DH Transformation Matrix for a single joint.
        """
        return np.array([
            [np.cos(theta), -np.sin(theta) * np.cos(alpha), np.sin(theta) * np.sin(alpha), a * np.cos(theta)],
            [np.sin(theta), np.cos(theta) * np.cos(alpha), -np.cos(theta) * np.sin(alpha), a * np.sin(theta)],
            [0, np.sin(alpha), np.cos(alpha), d],
            [0, 0, 0, 1]
        ])

    # (theta, d, a, alpha)
    dh_params = np.array([
        [np.deg2rad(0), 0, 0, np.deg2rad(0)],  # For joint 1
        [np.deg2rad(0), 0, 0, np.deg2rad(90)],  # For joint 2
        [np.deg2rad(0), 205.70 / scale, 0, np.deg2rad(90)],  # For joint 3
        [np.deg2rad(0), 76 / scale, 0, np.deg2rad(90)],  # For joint 4
        [np.deg2rad(0), 0, 229.5 / scale, np.deg2rad(0)],  # For joint 5
        [np.deg2rad(0), 0, 0, np.deg2rad(0)]  # For joint 6
    ])
    base_position = np.array([528.36721727, 481.34115526, 261.46507843])  # Example base position
    while True:
        # Simulate reading encoder data
        data = read_encoder_data()
        # data = {'1': 0, '2': 0, '3': 0, '4': 0, '5': 0, '6': 0}
        print(f' original {data}')
        for key in ['4', '3', '1']:
            data[key] = -data[key]
        if data:
            data['4'], data['1'] = data['1'], data['4']
            swapped_data = {
                '1': data['1'],  # Key 4 becomes key 1
                '2': data['4'],  # Key 1 becomes key 2
                '3': data['2'],  # Key 2 becomes key 3
                '4': data['3'],  # Key 3 becomes key 4
                '5': data['5'],
                '6': data['6']
            }
            data = swapped_data

            print(f'sorted data.values() {data.values()}')
            # Convert the joint angles (theta) from degrees to radians
            theta_angles = [np.deg2rad(angle) for angle in data.values()]

            # Calculate the End-Effector Transformation Matrix
            T = np.eye(4)  # Initialize as the identity matrix
            for i, (theta, d, a, alpha) in enumerate(dh_params):
                T_joint = dh_transform(theta_angles[i] + theta, d, a, alpha)  # Add joint angle to theta
                # print(f"Transformation matrix for joint {i + 1}:\n", T_joint)
                T = np.dot(T, T_joint)

            # print("\nFinal End-Effector Transformation Matrix:")
            # print(T)

            # Extract Position and Orientation
            position = T[:3, 3]
            # orientation = T[:3, :3]

            # print("\nEnd-Effector Position (x, y, z):", position)
            print("\nEnd-Effector Position (x, y, z):", (position * scale)/10)
            print("\nEnd-Effector Position (x, y, z):", base_position + (position * scale)/10)
            time.sleep(1)
            # print("End-Effector Orientation (Rotation Matrix):\n", orientation)


# test()


import math


def spherical_to_cartesian_with_offset(r, theta, phi, origin):
    """
    Convert spherical coordinates (r, theta, phi) to Cartesian coordinates (x, y, z)
    with an offset origin.

    Parameters:
    r : float           -> Radius (distance from origin)
    theta : float       -> Polar angle in radians (measured from the positive z-axis)
    phi : float         -> Azimuthal angle in radians (measured from the positive x-axis)
    origin : tuple      -> Coordinates of the origin (x0, y0, z0)

    Returns:
    tuple : (x, y, z) in Cartesian coordinates
    """
    x0, y0, z0 = origin

    x = x0 + r * math.sin(theta) * math.cos(phi)
    y = y0 + r * math.sin(theta) * math.sin(phi)
    z = z0 + r * math.cos(theta)

    return x, y, z



def test1 ():
    point1 = np.array([538.66843039, 512.92515387, 258.89817359])
    point2 = np.array([542.65836499, 512.93034537, 259.00216237])

    # Calculate the midpoint
    pointA = (point1 + point2) / 2

    pointB = np.array([540.57753906, 507.17568289, 259.95490083])

    # Calculate the scale
    calculated_distance = np.linalg.norm(pointA - pointB)
    real_life_distance = 104  # in mm

    # Check the scale
    scale = real_life_distance / calculated_distance

    while True:
        # Simulate reading encoder data
        data = read_encoder_data()
        for key in ['6', '2', '3']:
            data[key] = -data[key]
            # data['4'] = -data['1']
            # [540.9900782  479.94785676 251.47918559]
        if data:
            data['4'], data['1'] = data['1'], data['4']
            # data['1'] = -data['1']
            # Create the tuple and update the dictionary based on sorted values
            # print(f'init data {data}')
            swapped_data = {
                '1': data['1'],  # Key 4 becomes key 1
                '2': data['4'],  # Key 1 becomes key 2
                '3': data['2'],  # Key 2 becomes key 3
                '4': data['3'],  # Key 3 becomes key 4
                '5': data['5'],
                '6': data['6']
            }
            data = swapped_data

            # print(f'sorted data.values() {data.values()}')

        r1 = 288.47915435954815/scale
        theta1 = data['2']
        phi1 = data['1']
        origin1 = np.array([528.28843338, 481.55731928, 261.24525447])  # Example base position

        x, y, z = spherical_to_cartesian_with_offset(r1, theta1, phi1, origin1)
        print(f"Coordinates 1 : x={x:.3f}, y={y:.3f}, z={z:.3f}\n")

        r2 = 263.38/ scale
        theta2 = data['2']
        phi2 = data['3']
        origin2 = (x, y, z)

        x, y, z = spherical_to_cartesian_with_offset(r2, theta2, phi2, origin2)

        r3 = 229.50/ scale
        theta3 = data['5']
        phi3 = data['4']
        origin3 = (x, y, z)
        x, y, z = spherical_to_cartesian_with_offset(r3, theta3, phi3, origin3)
        time.sleep(1)


        print(f"Cartesian Coordinates: x={x:.3f}, y={y:.3f}, z={z:.3f}")



test1()