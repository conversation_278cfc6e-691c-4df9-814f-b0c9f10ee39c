<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Artikon</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
		<link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                         <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">07/01/2025</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut resect_proximal">
                        <div class="profile">
                            <ul>
                                <li>Planning Proximal Tibia Cut</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center justify-content-center">
                                        <div class="col-8">
                                            <div class="new-grid-box">
                                                <div class="row">
                                                    <div class="col-3">
                                                        <div class="grid-box">
                                                            <button class="d-flex align-items-center justify-content-center invisible">
                                                            </button>
                                                            <div class="position-relative">
                                                                <div class="grid-box-img">
                                                                    <img src="../static/images/revision-tkr/planning-proximal-tibia-cut/tibia_cut_planning_side.jpg" class="img-fluid" alt="">
                                                                </div>
                                                                <div class="txt left">
                                                                    M
                                                                </div>
                                                                <div class="txt right">
                                                                    L
                                                                </div>
                                                            </div>
                                                            <div class="sub-grid ps-0 pe-0">
                                                                <div class="depth">
                                                                    <div class="depth_bg">Slope</div>
                                                                    <div class="depth_btn">
                                                                        <!--ul>
                                                                            <li id='li-1'>0</li>
                                                                            <sup class="fs-14">0</sup>
                                                                        </ul-->
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <h5>Align to</h5>
                                                        <div class="row justify-content-center g-2">
                                                            <div class="col-6">
                                                                <div class="grid-box-center-img">
                                                                    <form>
                                                                        <input class="checkbox" type="checkbox" id="mechanical_axis" name="" value="">
                                                                        <label for="" class="dashed-border label pb-2">Mechanical Axis</label>
                                                                    </form>
                                                                    <div class="grid-box-img">
                                                                        <img src="../static/images/revision-tkr/planning-proximal-tibia-cut/tibia_cut_planning_full_front_mechenical_axis.jpg" class="img-fluid" alt="">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-6">
                                                                <div class="grid-box-center-img">
                                                                    <form>
                                                                        <input class="checkbox" type="checkbox" id="im_canal" name="" value="">
                                                                        <label for="" class="solid-border label pb-2" >Im Canal</label>
                                                                        <span class="dashed-border"></span>
                                                                    </form>
                                                                    <div class="grid-box-img">
                                                                        <img src="../static/images/revision-tkr/planning-proximal-tibia-cut/tibia_cut_planning_full_front_im_canal.jpg" class="img-fluid" alt="">
                                                                    </div> 
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="grid-box">
                                                            <button class="d-flex align-items-center justify-content-center invisible">
<!--                                                                <span id="textbox2">90.0</span>-->
<!--                                                                <sup class="fs-14">0</sup>-->
                                                            </button>
                                                            <div class="grid-box-img">
                                                                <img src="../static/images/revision-tkr/planning-proximal-tibia-cut/tc.jpg" class="img-fluid" alt="">
                                                            </div>
                                                            <div class="sub-grid ps-0 pe-0">
                                                                <div class="depth">
                                                                    <div class="depth_bg">Varus / Valgus</div>
                                                                    <div class="depth_btn">
                                                                        <!--ul>
                                                                            <li id='li-2'>0</li>
                                                                            <sup class="fs-14">0</sup>
                                                                        </ul-->
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="note">
                                                <p class="note_txt">Notes:</p>
                                                <p>Planning proximal tibia cut according to mechanical axis or IM canal axis</p>
                                            </div>
                                        </div>
                                       
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="blank blank-none"></div>
                                    <div class="btn">
                                        <a id="backBtn" href="tibia-im-canal-ream.html">
                                            <span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back
                                        </a>
                                    </div>
                                    <div class="btn">
                                        <a id="nextBtn" href="femur-im-canal-ream.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                 <a href="#" id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
                            </li>
                            <li class="footer_btn_three">
								<a href="#" id="powerbuttonLink">
									<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
								</a>
								<script>
									document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
										event.preventDefault();  // Prevent default anchor behavior
										window.location.href = '/';  // Redirect to root path
									});
								</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>

<script>
$(document).ready(function () {
    // Listen for change event on checkboxes
    $('.checkbox').change(function () {
        // Get the current checkbox and its state
        let currentCheckbox = $(this);
        let isChecked = currentCheckbox.is(':checked');

        // Uncheck all other checkboxes
        $('.checkbox').not(this).each(function () {
            // If previously checked, notify the backend of the change
            if ($(this).is(':checked')) {
                sendUpdateToBackend($(this).attr('id'), false);
            }
            $(this).prop('checked', false).closest('.grid-box-center-img').removeClass('checked-color');
        });

        // Update the current checkbox's parent class
        currentCheckbox.closest('.grid-box-center-img').toggleClass('checked-color', isChecked);

        // Notify the backend of the current checkbox state
        sendUpdateToBackend(currentCheckbox.attr('id'), isChecked);
    });

    // Function to send checkbox state to the backend
    function sendUpdateToBackend(id, state) {
        let dataToSend = {
            id: id,
            state: state
        };

        // Send the data to the backend
        $.ajax({
            url: '/update_checkbox', // FastAPI endpoint
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(dataToSend),
            success: function (response) {
                console.log(`Checkbox ${id} state updated successfully:`, response);
            },
            error: function (xhr, status, error) {
                console.error(`Error updating checkbox ${id} state:`, error);
            }
        });
    }
});


    // server js

    let socket;

    function startServer(index) {
        
        if (!socket || socket.readyState !== WebSocket.OPEN) {
        const currentUrl = window.location.href;
            const pageName = "rev" + currentUrl.substring(currentUrl.lastIndexOf('/') + 1).split('?')[0];
        socket = new WebSocket('ws://127.0.0.1:8000/ws');

        socket.onopen = function(event) {
            console.log('Socket opened for ');

            socket.send(JSON.stringify({ file: pageName }));
        };

        socket.onmessage = function(event) {
            const values = event.data.split(',');
            const abbreviation = values[0];
            const data = values[1];
            // Get the current URL
            var currentUrl = window.location.href;
            // Remove the page name from the URL
            var projectUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);
            if (abbreviation == 'exit') {
                window.location.href = projectUrl + 'femur-im-canal-ream.html';
            }
            
        };

        socket.onerror = function(error) {
            console.error('Socket error:', error);
        };
      }
    };
	
	function handleNavigation(event) {
    event.preventDefault();
    const targetUrl = event.currentTarget.href;

    if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({ file: " " }));
        socket.addEventListener('close', function () {
            window.location.href = targetUrl;
        }, { once: true });

        socket.close();
    } else {
        window.location.href = targetUrl;
    }
};
	
	
	document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
    document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);
	
	// Camera functionality
    document.addEventListener('DOMContentLoaded', function() {
        const cameraButton = document.querySelector('.footer_btn_one .btn.first');
        const cameraModal = document.getElementById('cameraModal');
        const closeCamera = document.querySelector('.close-camera');
        const videoStream = document.getElementById('videoStream');
        let mediaStream = null;

        // Function to start the camera
        async function startCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });
                mediaStream = stream;
                videoStream.srcObject = stream;
                videoStream.play();
            } catch (err) {
                console.error('Error accessing camera:', err);
            }
        }

        // Function to stop the camera
        function stopCamera() {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                videoStream.srcObject = null;
                mediaStream = null;
            }
        }

        // Open camera modal
        cameraButton.addEventListener('click', function(e) {
            e.preventDefault();
            cameraModal.style.display = 'block';
            startCamera();
        });

        // Close camera modal
        closeCamera.addEventListener('click', function() {
            cameraModal.style.display = 'none';
            stopCamera();
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === cameraModal) {
                cameraModal.style.display = 'none';
                stopCamera();
            }
        });
    });

    //  Start the server automatically on page load
     window.onload = function() {
        startServer('Planning_Proximal_Tibia_Cut');
    };

</script>


</html>