<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Back Button Example</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            color: white;
            background-color: black;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .table-container {
            width: 70%;
            display: grid;
            grid-template-columns: 1fr 0.5fr 0.5fr;
            gap: 10px;
            text-align: center;
        }
        .header {
            grid-column: span 3;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
            background-color: black;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .label, .value {
            padding: 10px;
            border: 2px solid white;
            border-radius: 5px;
        }
        .label {
            background-color: transparent;
        }
        .value {
            background-color: black;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
        }
        .back-button {
            background-color: #1e1e2f;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            font-size: 16px;
            display: flex;
            align-items: center;
            cursor: pointer;
            width: 120px;
            justify-content: center;
            text-align: center;
        }
        .back-button:hover {
            background-color: #2a2a3f;
        }
    </style>
</head>
<body>
    <div class="table-container">
        <div class="header">Actual vs Planned Values</div>
        <div class="label">Label</div>
        <div class="label">Actual</div>
        <div class="label">Planned</div>

        <div class="label">Cup Inclination</div>
        <div class="value">41°</div>
        <div class="value">40°</div>

        <div class="label">Cup Version</div>
        <div class="value">21°</div>
        <div class="value">20°</div>

        <div class="label">Stem Version</div>
        <div class="value">-2°</div>
        <div class="value">-4°</div>

        <div class="label">Combined Version</div>
        <div class="value">19°</div>
        <div class="value">16°</div>

        <div class="label">Hip Length</div>
        <div class="value">5 mm</div>
        <div class="value">5 mm</div>

        <div class="label">Combined Offset</div>
        <div class="value">6 mm</div>
        <div class="value">0 mm</div>

        <!-- Back Button inside table -->
        <div class="label" colspan="3">
            <button class="back-button">
                Back
            </button>
        </div>
    </div>
</body>
</html>
