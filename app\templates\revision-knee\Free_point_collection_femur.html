<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Model Viewer with WebSocket & Ring Points</title>


    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
		<link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>

    <style>
        body { margin: 0; overflow: hidden; }
        #canvas-container {
            background-color: black;
            width: 100vw;
            height: 100vh;
            display: block;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128/examples/js/loaders/OBJLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128/examples/js/controls/OrbitControls.js"></script>
</head>
<body>
    <div id="canvas-container"></div>




    <div class="head_wrap">
    <div class="header">
        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
         <div class="second_head head_bg">
            <div class="inner_head one">
                <span><img src="../static/images/calendar.png" /></span><span id="date">24/11/2022</span>
            </div>
            <div class="inner_head">
                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
            </div>
        </div>
        <div class="third_head head_bg">
            <span><img src="../static/images/settings.png" /></span>Settings
        </div>
    </div>
    </div>


    <script>
    let scene, camera, renderer, controls, object3D, pivot, modelCenter;
    let modelVertices = [];

    const socket = new WebSocket("ws://localhost:8000/ws");

    socket.addEventListener("open", () => {
        const currentUrl = window.location.href;
        let pageName = 'rev' + currentUrl.split('/').pop().split('?')[0];
        socket.send(JSON.stringify({ file: pageName }));
        console.log("Sent file name:", pageName);
    });

    function init() {
        scene = new THREE.Scene();
        camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(0, 50, 50);

        renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        document.getElementById("canvas-container").appendChild(renderer.domElement);

        controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;

        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        scene.add(ambientLight);
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.7);
        directionalLight.position.set(5, 5, 5);
        scene.add(directionalLight);

        const loader = new THREE.OBJLoader();
        loader.load('/static/images/hip-bone/3D/FEMUR_FULL.obj', function (obj) {
            const box = new THREE.Box3().setFromObject(obj);
            modelCenter = box.getCenter(new THREE.Vector3());

            pivot = new THREE.Group();
            scene.add(pivot);

            obj.position.sub(modelCenter); // Center the model
            pivot.add(obj);

            obj.traverse(child => {
                if (child.isMesh) {
                    const position = child.geometry.attributes.position;
                    for (let i = 0; i < position.count; i++) {
                        const vertex = new THREE.Vector3().fromBufferAttribute(position, i);
                        modelVertices.push(vertex.clone().sub(modelCenter));
                    }

                    child.material = new THREE.MeshStandardMaterial({
                        color: 0xf5deb3,
                        side: THREE.DoubleSide
                    });
                }
            });

            object3D = pivot;
        });

        animate();
    }

    function highlightNearestVertex(pointVec3, color = 0xff0000) {
        if (!modelVertices.length) {
            console.warn("Model vertices not yet available.");
            return;
        }

        let minDist = Infinity;
        let nearest = null;

        for (let i = 0; i < modelVertices.length; i++) {
            const dist = pointVec3.distanceToSquared(modelVertices[i]);
            if (dist < minDist) {
                minDist = dist;
                nearest = modelVertices[i];
            }
        }

        if (nearest) {
            const marker = new THREE.Mesh(
                new THREE.SphereGeometry(0.08, 8, 8),
                new THREE.MeshBasicMaterial({ color })
            );
            marker.position.copy(nearest);
            pivot.add(marker);
            console.log("✅ Highlighted nearest model vertex at:", nearest);
        }
    }

    socket.onmessage = function (event) {
        try {
            if (!event.data || event.data.trim() === "") return;

            let trimmed = event.data.trim();
            if (trimmed.toLowerCase() === "exit") {
                socket.close();
                window.location.href = "Free_point_collectionTibia.html";
                return;
            }

            if (trimmed.toLowerCase() === "null") return;

            let data;
            if (/^-?\d+(\.\d+)?,-?\d+(\.\d+)?,-?\d+(\.\d+)?$/.test(trimmed)) {
                data = trimmed.split(",").map(Number);
            } else {
                try {
                    data = JSON.parse(trimmed);
                } catch (e) {
                    console.warn("Invalid data, ignoring:", trimmed);
                    return;
                }
            }

            if (Array.isArray(data) && data.length === 3) {
                const [x, y, z] = data;
                const color = 0x00ff00;
                highlightNearestVertex(new THREE.Vector3(x - modelCenter.x, y - modelCenter.y, z - modelCenter.z), color);
            } else if (typeof data === "object" && "x" in data && "y" in data && "z" in data) {
                const colorHex = data.color ? (data.color.startsWith("#") ? data.color : `#${data.color}`) : "#ff0000";
                const color = parseInt(colorHex.replace(/^#/, ""), 16);
                highlightNearestVertex(new THREE.Vector3(data.x - modelCenter.x, data.y - modelCenter.y, data.z - modelCenter.z), color);
            }

        } catch (err) {
            console.error("Error handling WebSocket message:", err);
        }
    };

    function animate() {
        requestAnimationFrame(animate);
        controls.update();
        renderer.render(scene, camera);
    }

    window.addEventListener("resize", () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    });

    init();
</script>



    <div class="bottom_btn">
        <div class="blank"></div>
        <div class="btn">
            <a id="backBtn" href="ml-size-acquisition.html">
                <span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back
            </a>
        </div>
        <div class="btn">
            <a id="nextBtn" href="Free_point_collectionTibia.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
        </div>
    </div>

    <div class="footer_wrap">
        <div class="footer">
            <ul>
                <li class="copy_right">
                    © <span id="year">2023</span> Artikon AGS
                    <span class="top_txt">Auto Guided Surgery</span>
                </li>
                <li class="footer_btn_one">
                    <a href="#">
                        <div class="btn-group" role="group" aria-label="Basic example">
                            <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                            <button type="button" class="btn second">F</button>
                            <button type="button" class="btn third">T</button>
                        </div>
                    </a>
                </li>
                <li class="footer_btn_two">
                    <a href="#">
                        <span><img src="../static/images/home.png" /></span>Main Menu
                    </a>
                </li>
                <li class="footer_btn_three">
                    <a href="#">
                        <span><img src="../static/images/union.png" /></span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>
</html>
