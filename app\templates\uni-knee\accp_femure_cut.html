<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Artikon</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
		<link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
	<link rel="stylesheet" type="text/css" href="../static/css/unidistalstyle.css">
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                         <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul>
                                <li>ACCP Femur Cut</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center align-items-start">
                                        <div class="col-6">
                                            <div class="Bone-box-style pb-80">
                                                <img id="initImage" src="../static/images/femur/accp/InitImage.jpeg" class="img-fluid d-none" alt="">
                                                <img id="redImage" src="../static/images/femur/accp/PG_7_ME_Red_1.jpg" class="img-fluid d-none" alt="">
                                                <img id="greenImage" src="../static/images/femur/accp/PG_7_ME_Green.jpg" class="img-fluid d-none" alt="">
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <canvas id="safeZoneChart" width="100" height="100"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="btn">
                                        Auto Align
                                    </div>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a id="backBtn" href="femur-distal-femur-cut.html">
                                            <span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back
                                        </a>
                                    </div>
                                    <div class="btn">
                                        <a id="nextBtn" href="tibia-cut.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#">
                                    <span><img src="../static/images/home.png" /></span>Main Menu
                                </a>
                            </li>
                            <li class="footer_btn_three">
                                <a href="#">
                                    <span><img src="../static/images/union.png" /></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/uni_accp_cut.js"></script>
<script src="../static/js/common.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script type="text/javascript">
let socket;
let chart;

const animationStates = {
    GREEN: false,
    RED: false,
    INIT: true
};


// Function to create circle data
function createCircleData(centerX, centerY, radius, points = 100) {
    const data = [];
    for (let i = 0; i <= points; i++) {
        const angle = (2 * Math.PI * i) / points;
        data.push({
            x: centerX + radius * Math.cos(angle),
            y: centerY + radius * Math.sin(angle),
        });
    }
    return data;
}

// Initialize chart
function initializeChart() {
    const ctx = document.getElementById('safeZoneChart').getContext('2d');
    chart = new Chart(ctx, {
        type: 'scatter',
        data: {
            datasets: [
                {
                    label: 'Safe Zone',
                    data: createCircleData(15, 40, 10),
                    backgroundColor: 'rgba(0, 255, 0, 0.3)',
                    borderColor: 'red',
                    borderWidth: 2,
                    showLine: true,
                    fill: true,
                    pointRadius: 0
                },
                {
                    label: 'Current Position',
                    data: [],
                    backgroundColor: 'red',
                    pointRadius: 6,
                },
                {
                    label: '',
                    data: createCircleData(15, 40, 5),
                    borderColor: 'yellow',
                    borderWidth: 2,
                    showLine: true,
                    fill: false,
                    pointRadius: 0,
                },
                {
                    label: '',
                    data: createCircleData(15, 40, 2),
                    borderColor: 'orange',
                    borderWidth: 2,
                    showLine: true,
                    fill: false,
                    pointRadius: 0,
                },
                {
                    label: '',
                    data: createCircleData(15, 40, 0.5),
                    borderColor: 'green',
                    borderWidth: 2,
                    showLine: true,
                    fill: true,
                    backgroundColor: 'red',
                    pointRadius: 0,
                }
            ]
        },
        options: {
            scales: {
                x: {
                    title: { display: true, text: 'Anteversion (degrees)', color: 'white' },
                    ticks: { color: '#fff' },
                    min: 0,
                    max: 50,
                },
                y: {
                    title: { display: true, text: 'Inclination (degrees)', color: 'white' },
                    ticks: { color: '#fff' },
                    min: 20,
                    max: 70,
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: 'white',
                        filter: (legendItem, data) => {
                            return legendItem.text !== '';
                        }
                    }
                }
            },
            responsive: true,
            maintainAspectRatio: true,
            layout: {
                padding: {
                    left: 5,
                    right: 5,
                    top: 5,
                    bottom: 5
                }
            }
        }
    });
<!--    chart.data.datasets[0].data = createCircleData(15, 40, 10);-->
<!--    chart.data.datasets[2].data = createCircleData(15, 40, 5);-->
<!--    chart.data.datasets[3].data = createCircleData(15, 40, 2);-->
}



function startServer() {
    if (!socket || socket.readyState !== WebSocket.OPEN) {
        const currentUrl = window.location.href;
        const pageName = 'uni' + currentUrl.substring(currentUrl.lastIndexOf('/') + 1).split('?')[0];
        socket = new WebSocket('ws://127.0.0.1:8000/ws');

        socket.onopen = function(event) {
            console.log('Socket opened for ' + pageName);
            socket.send(JSON.stringify({ file: pageName }));
        };

        socket.onmessage = function(event) {
            try {
                const parsedData = JSON.parse(event.data);
                const abbreviation = parsedData.abbreviation;
                const anteversion = parsedData.anteversion;
                const inclination = parsedData.inclination;
                console.log('Received data:', parsedData);

                // Update chart position and data
                if (chart) {
                    // Update current position
                    chart.data.datasets[1].data = [{ x: anteversion, y: inclination }];
                    // Update the chart
                    chart.update(); // Use 'none' mode for better performance
                }

                // Hide all images first
                document.getElementById('initImage').classList.add('d-none');
                document.getElementById('redImage').classList.add('d-none');
                document.getElementById('greenImage').classList.add('d-none');

                // Show appropriate image based on abbreviation
                if (abbreviation === 'GREEN') {
                    document.getElementById('greenImage').classList.remove('d-none');
                    animationStates.GREEN = true;
                    animationStates.RED = false;
                    animationStates.INIT = false;
                } else if (abbreviation === 'RED') {
                    document.getElementById('redImage').classList.remove('d-none');
                    animationStates.GREEN = false;
                    animationStates.RED = true;
                    animationStates.INIT = false;
                } else if (abbreviation === 'INIT') {
                    document.getElementById('initImage').classList.remove('d-none');
                    animationStates.GREEN = false;
                    animationStates.RED = false;
                    animationStates.INIT = true;
                }

                // Redirect if exit
                if (abbreviation === 'exit') {
                    window.location.href = projectUrl + 'tibia-cut.html';
                }
            } catch (error) {
                console.error('Error parsing WebSocket data:', error);
            }
        };

        socket.onerror = function(error) {
            console.error('Socket error:', error);
        };

        socket.onclose = function(event) {
            console.log('Socket closed:', event);
            setTimeout(startServer, 1000);
        };
    }
}
	function handleNavigation(event) {
		event.preventDefault();
		const targetUrl = event.currentTarget.href;

		if (socket && socket.readyState === WebSocket.OPEN) {
			socket.send(JSON.stringify({ file: " " }));
			socket.addEventListener('close', function () {
				window.location.href = targetUrl;
			}, { once: true });

			socket.close();
		} else {
			window.location.href = targetUrl;
		}
	};
	document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
    document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);

// Camera functionality
document.addEventListener('DOMContentLoaded', function() {
    const cameraButton = document.querySelector('.footer_btn_one .btn.first');
    const cameraModal = document.getElementById('cameraModal');
    const closeCamera = document.querySelector('.close-camera');
    const videoStream = document.getElementById('videoStream');
    let mediaStream = null;

    // Function to start the camera
    async function startCamera() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                }
            });
            mediaStream = stream;
            videoStream.srcObject = stream;
            videoStream.play();
        } catch (err) {
            console.error('Error accessing camera:', err);
        }
    }

    // Function to stop the camera
    function stopCamera() {
        if (mediaStream) {
            mediaStream.getTracks().forEach(track => track.stop());
            videoStream.srcObject = null;
            mediaStream = null;
        }
    }

    // Open camera modal
    cameraButton.addEventListener('click', function(e) {
        e.preventDefault();
        cameraModal.style.display = 'block';
        startCamera();
    });

    // Close camera modal
    closeCamera.addEventListener('click', function() {
        cameraModal.style.display = 'none';
        stopCamera();
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === cameraModal) {
            cameraModal.style.display = 'none';
            stopCamera();
        }
    });
});

// Start the server and initialize chart when page loads
window.onload = function() {
    initializeChart();
    startServer();
    // Show initial image
    document.getElementById('initImage').classList.remove('d-none');
};
</script>



</html>