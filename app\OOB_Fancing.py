#!/usr/bin/env python3

import os
import sys
import time
import numpy as np
from configparser import ConfigParser
from xarm.wrapper import XArmAPI

# === Configuration ===
DO_PIN = 3  # CO3 for motor control

# TODO: Set your direction vector (unit vector in desired box orientation)
direction_vector = np.array([-0.01178332,0.94914635,0.31461462])  # example: 45° rotated in XY plane
#[-0.01178332  0.94914635  0.31461462]
direction_vector_v = np.array([0.02974738, -0.30491068,  0.95191626])


# TODO: Define AABB bounds in world coordinates (as if unrotated)
aabb_min = np.array([317, 420.6, -17.7])
aabb_max = np.array([394.8, 448.5, 49.3])

# === Compute OBB ===
center = (aabb_min + aabb_max) / 2
extent = (aabb_max - aabb_min) / 2

# Orthonormal basis from direction vector
u = direction_vector / np.linalg.norm(direction_vector)
arbitrary = np.array([1, 0, 0]) if abs(u[0]) < 0.9 else np.array([0, 1, 0])
v = direction_vector_v / np.linalg.norm(direction_vector_v)
w = np.cross(u, v)
R = np.column_stack((u, v, w))

# Generate corners and transform to OBB frame
signs = np.array([[-1, -1, -1], [-1, -1, 1], [-1, 1, -1], [-1, 1, 1],
                  [1, -1, -1], [1, -1, 1], [1, 1, -1], [1, 1, 1]])
corners = center + signs * extent
corners_obb = (R.T @ (corners - center).T).T
min_obb = np.min(corners_obb, axis=0)
max_obb = np.max(corners_obb, axis=0)
print(min_obb,max_obb)

# === Connect to the robot ===
parser = ConfigParser()
parser.read('../robot.conf')
try:
    ip = parser.get('xArm', 'ip')
except:
    ip = '*************'

arm = XArmAPI(ip)
time.sleep(0.5)
print(f"[INFO] Connected to robot at {ip}, version {arm.version}")

if arm.warn_code != 0: arm.clean_warn()
if arm.error_code != 0: arm.clean_error()

# === Output control ===
def control_output(run_motor: bool):
    signal = 0 if run_motor else 1
    code = arm.set_cgpio_digital(DO_PIN, signal)
    if code == 0:
        print(f"[DO] CO{DO_PIN} set to {'LOW (RUN)' if signal == 0 else 'HIGH (STOP)'}")
    else:
        print(f"[ERROR] Failed to set CO{DO_PIN}, Code={code}")

# === Fence logic (using OBB) ===
def is_outside_fence(pos):
    point = np.array(pos[:3])
    local_pos = R.T @ (point - center)
    return np.any(local_pos < min_obb) or np.any(local_pos > max_obb)

# === Start monitoring ===
print("\n[INFO] Monitoring robot position with OBB-based safety fence...\n")
last_state = None

try:
    while True:
        code, pos = arm.get_position(is_radian=False)
        if code == 0 and isinstance(pos, list):
            x, y, z = pos[:3]
            outside = is_outside_fence(pos)
            print(f"[POS] X={x:.1f}, Y={y:.1f}, Z={z:.1f} → {'OUTSIDE' if outside else 'INSIDE'}")

            if not outside and last_state != 'inside':
                control_output(run_motor=True)
                last_state = 'inside'
            elif outside and last_state != 'outside':
                control_output(run_motor=False)
                last_state = 'outside'
        else:
            print("[ERROR] Could not read robot position.")
        time.sleep(0.3)

except KeyboardInterrupt:
    print("\n[INFO] Monitoring stopped by user.")

finally:
    control_output(run_motor=False)
    arm.disconnect()
    print("[INFO] Robot disconnected. Motor stopped.")
