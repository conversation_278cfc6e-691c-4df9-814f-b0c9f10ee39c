import cv2
import numpy as np
import os

# Load stereo calibration parameters once at import
params = np.load(r'D:\SpineSurgery\pythonProject\kneeSequence\marking\primary\stereo_calibration_parameters.npz')
mtxL, distL = params['mtxL'], params['distL']
mtxR, distR = params['mtxR'], params['distR']
R, T = params['R'], params['T']

# Precomputed stereo rectification matrices (initialized on first use)
_rectify_maps = {
    'R1': None, 'R2': None, 'P1': None, 'P2': None,
    'mapLx': None, 'mapLy': None,
    'mapRx': None, 'mapRy': None
}


def rectify_images(frameR, frameL):
    global _rectify_maps

    if _rectify_maps['R1'] is None:
        img_size = (frameL.shape[1], frameL.shape[0])
        R1, R2, P1, P2, _, _, _ = cv2.stereoRectify(mtxL, distL, mtxR, distR, img_size, R, T, alpha=1)

        mapLx, mapLy = cv2.initUndistortRectifyMap(mtxL, distL, R1, P1, img_size, cv2.CV_32FC1)
        mapRx, mapRy = cv2.initUndistortRectifyMap(mtxR, distR, R2, P2, img_size, cv2.CV_32FC1)

        _rectify_maps.update({
            'R1': R1, 'R2': R2, 'P1': P1, 'P2': P2,
            'mapLx': mapLx, 'mapLy': mapLy,
            'mapRx': mapRx, 'mapRy': mapRy
        })

    rectifiedL = cv2.remap(frameL, _rectify_maps['mapLx'], _rectify_maps['mapLy'], cv2.INTER_LINEAR)
    rectifiedR = cv2.remap(frameR, _rectify_maps['mapRx'], _rectify_maps['mapRy'], cv2.INTER_LINEAR)
    return rectifiedR, rectifiedL

def test_reprojection(points_3d, left_detections, right_detections):
    # Ensure points_3d is (N, 3)
    if points_3d.shape[1] != 3:
        raise ValueError("Expected points_3d to have shape (N, 3), but got", points_3d.shape)

    # Convert points_3d back to homogeneous coordinates
    points_4d = np.hstack((points_3d, np.ones((points_3d.shape[0], 1)))).T  # Shape: (4, N)

    # Reproject onto the left and right cameras
    reprojected_left = P1 @ points_4d  # Shape: (3, N)
    reprojected_right = P2 @ points_4d  # Shape: (3, N)

    # Normalize to get pixel coordinates
    reprojected_left = (reprojected_left[:2, :] / reprojected_left[2, :]).T  # (N, 2)
    reprojected_right = (reprojected_right[:2, :] / reprojected_right[2, :]).T  # (N, 2)

    # Compute reprojection error
    left_error = np.linalg.norm(reprojected_left - left_detections, axis=1)
    right_error = np.linalg.norm(reprojected_right - right_detections, axis=1)

    print(f"Reprojected Left Points: {reprojected_left}")
    print(f"Reprojected Right Points: {reprojected_right}")
    print(f"Left Reprojection Error: {left_error}")
    print(f"Right Reprojection Error: {right_error}")

    return left_error, right_error


def get_3D_points(left_detections, right_detections):
    import matplotlib.pyplot as plt
    # print(f'left_detections{left_detections}')
    # print(f'right_detections{right_detections}')
    left_detections1 = np.array(left_detections).T  # Shape: (2, N)
    right_detections1 = np.array(right_detections).T  # Shape: (2, N)
    points_4d = cv2.triangulatePoints(P1, P2, left_detections1, right_detections1)

    # Convert from homogeneous to Euclidean coordinates
    points_3d = points_4d[:3, :].T / points_4d[3, :].T  # Shape: (N, 3)
    # print(f"points_3d {points_3d}")
    print(f'points_3d  {points_3d[0]}  {points_3d[1]}  {points_3d[2]}')

    # # Left Camera: Origin
    # origin_left = np.array([0, 0, 0])
    # x_axis_left = np.array([1, 0, 0])  # Unit vector along X
    # y_axis_left = np.array([0, 1, 0])  # Unit vector along Y
    # z_axis_left = np.array([0, 0, 1])  # Unit vector along Z
    # origin_right = trans
    # x_axis_right = rot @ np.array([1, 0, 0]) + trans
    # y_axis_right = rot @ np.array([0, 1, 0]) + trans
    # z_axis_right = rot @ np.array([0, 0, 1]) + trans
    #
    # fig = plt.figure()
    # ax = fig.add_subplot(111, projection='3d')
    # ax.quiver(*origin_left, *x_axis_left, color='r', label="Left X-axis")
    # ax.quiver(*origin_left, *y_axis_left, color='g', label="Left Y-axis")
    # ax.quiver(*origin_left, *z_axis_left, color='b', label="Left Z-axis")
    #
    # ax.quiver(*origin_right, *x_axis_right, color='r', linestyle='dotted', label="Right X-axis")
    # ax.quiver(*origin_right, *y_axis_right, color='g', linestyle='dotted', label="Right Y-axis")
    # ax.quiver(*origin_right, *z_axis_right, color='b', linestyle='dotted', label="Right Z-axis")
    #
    # ax.set_xlim([-1, 1])
    # ax.set_ylim([-1, 1])
    # ax.set_zlim([-1, 1])
    # ax.set_xlabel("X")
    # ax.set_ylabel("Y")
    # ax.set_zlabel("Z")
    # ax.legend()
    # plt.show()

    # left_detections = np.array(left_detections) # Shape: (2, N)
    # right_detections = np.array(right_detections)  # Shape: (2, N)

    # test_reprojection(points_3d, left_detections, right_detections)
    # return
