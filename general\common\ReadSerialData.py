import serial
import re
import time

# Define the serial port and baud rate
COM_PORT = 'COM9'  # Adjust the COM port as needed
BAUD_RATE = 9600

# Define a regular expression pattern to match encoder data
PATTERN = r"encoder (\d+) angle:= (-?[\d\.]+)"
# PATTERN = r"encoder (\d+) angle:= (.+)"
# PATTERN = re.compile(r"(\d+)=(-?\d+\.?\d*)")
# Function to connect to the serial port
def connect_to_serial():
    try:
        ser = serial.Serial(COM_PORT, BAUD_RATE, timeout=1)
        # print(f"Connected to {COM_PORT} at {BAUD_RATE} baud.")
        return ser
    except serial.SerialException as e:
        print(f"Failed to connect to {COM_PORT}: {e}")
        return None

# Function to read and parse data into a dictionary
def read_encoder_data():
    ser = connect_to_serial()
    if not ser:
        print("Unable to establish connection. Retrying...")
        return {}

    encoder_data = {}  # Dictionary to store encoder data

    while True:
        try:
            line = ser.readline().decode('utf-8').strip()  # Read a line and decode
            if line:  # Skip empty lines
                match = re.match(PATTERN, line)
                if match:
                    encoder = match.group(1)  # Encoder number (1, 2, etc.)
                    angle = float(match.group(2))  # Angle value (e.g., 0.00, 0.09)
                    encoder_data[encoder] = angle  # Store in dictionary
                # else:
                #     print(f"Line did not match expected format: {line}")


            # For demonstration, break after reading 6 encoder values
            if len(encoder_data) >= 6:
                break

        except (serial.SerialException, OSError) as e:
            print(f"Serial connection lost: {e}. Reconnecting...")
            ser.close()
            time.sleep(2)  # Wait before retrying
            ser = connect_to_serial()
            if not ser:
                print("Reconnection failed. Exiting...")
                break

    if ser and ser.is_open:
        ser.close()

    sorted_encoder_data = {k: encoder_data[k] for k in sorted(encoder_data)}
    return sorted_encoder_data
#
# # Example usage
# if __name__ == "__main__":
#     while True:
#         data = read_encoder_data()
#         for key in ['6', '4', '2', '3']:
#             data[key] = -data[key]
#
#         if data:
#             data['4'], data['1'] = data['1'], data['4']
#             # Create the tuple and update the dictionary based on sorted values
#             keys = ['1', '2', '3', '4', '5', '6']
#             data_tuple = (data['4'], data['1'], data['2'], data['3'], data['5'], data['6'])
#             data = dict(zip(keys, sorted(data_tuple)))
#
#             print("Updated Encoder Data as Dictionary:", data)
#         time.sleep(1)  # Pause before the next read


# [544.34885284 484.79591383 262.54529234]