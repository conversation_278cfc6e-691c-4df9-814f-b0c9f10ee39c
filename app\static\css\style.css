@font-face {
    font-family: 'Poppins-Light';
    src: url('../font/Poppins/Poppins-Light.ttf') format('truetype');
    font-weight: Regular;
}

@font-face {
    font-family: 'Poppins-Regular';
    src: url('../font/Poppins/Poppins-Regular.ttf') format('truetype');
    font-weight: Regular;
}

@font-face {
    font-family: 'Poppins-Medium';
    src: url('../font/Poppins/Poppins-Medium.ttf') format('truetype');
    font-weight: Medium;
}

@font-face {
    font-family: 'Poppins-SemiBold';
    src: url('../font/Poppins/Poppins-SemiBold.ttf') format('truetype');
    font-weight: SemiBold;
}

@font-face {
    font-family: 'Poppins-Bold';
    src: url('../font/Poppins/Poppins-Bold.ttf') format('truetype');
    font-weight: bold;
}

@font-face {
    font-family: 'Poppins-ExtraBold';
    src: url('../font/Poppins/Poppins-ExtraBold.ttf') format('truetype');
    font-weight: ExtraBold;
}

@font-face {
    font-family: 'Montserrat';
    src: url('../font/Montserrat/static/Montserrat-Bold.ttf') format('truetype');
    font-weight: ExtraBold;
}

html,
body {
    height: 100%;
}

.tbl,
.tbl-cell {
    width: 100%;
    height: 100%;
}

.tbl {
    display: table;
    table-layout: fixed;
}

.tbl-cell {
    display: table-cell;
    vertical-align: middle;
}

.content-area {
    max-width: 1920px;
    max-height: 1000px;
    min-width: 1024px;
    /*min-height: 768px;*/
    width: 100%;
    height: 100%;
    /* background: #131022; */
    background: #000;
    margin: 0 auto;
    position: relative;
}


a {
    color: unset;
    text-decoration: none;
}

a:hover {
    color: unset;
}

ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

p {
    margin: 0;
}

body {
    color: #fff;
    font-family: 'Poppins-Light';
}

/*-------------common clsses-------------*/
.p-0 {
    padding: 0;
}

.mr-10 {
    margin-right: 10px;
}

.mr-20 {
    margin-right: 20px;
}

.fs-16 {
    font-size: 16px;
}

.text-center {
    text-align: center;
}

.position-relative {
    position: relative;
}

.hide {
    display: none;
}

.show {
    display: block;
}
.w-80{
    width: 80% !important;
}

/*----------------------------------------*/




/*-------------------WELCOM PAGE START---------------*/
.main_desk .middle_logo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.bottom_txt h2 {
    font-family: Montserrat;
    font-size: 128px;
    font-weight: 800;
    line-height: 156px;
    letter-spacing: 0em;
    text-align: center;
    text-transform: uppercase;
    text-shadow: -1px 0 #009eb217, 0 1px #009eb217, 1px 0 #009eb217, 0 -1px #009eb217;
    margin: 0;
    width: 100%;
    position: absolute;
    bottom: 0%;
    left: 50%;
    transform: translate(-50%, 0%);
    color: #1a1729;
}


@media (min-width: 1921px) {
    .main_section {
        align-items: center;
        height: calc(100vh - 80px);
    }
}

@media (max-width: 1700px) {
    .main_desk .middle_logo img {
        width: 80%;
    }

    .bottom_txt h2 {
        font-size: 105px;
    }
}

@media (max-width: 1400px) {
    .bottom_txt h2 {
        font-size: 82px;
    }
}

@media (max-width: 1200px) {
    .bottom_txt h2 {
        font-size: 62px;
    }
}

@media (max-width: 992px) {
    .bottom_txt h2 {
        font-size: 52px;
    }
}

/*-------------------WELCOM PAGE ENG------------------*/


/*-------------------LANDING PAGE START---------------*/
.landing_page {
    font-family: 'Poppins-Regular';
}

.head_wrap {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1;
}

.header {
    color: #fff;
    display: flex;
    justify-content: space-between;

}

.header {
    display: flex;
    width: 100%;
    padding: 10px;
}

.header .first_head.head_bg {
    flex: 1;
    line-height: 25px;
}

.header .second_head.head_bg {
    flex: none;
    display: flex;
    line-height: 20px;
    width: 360px;
}

.header .third_head.head_bg {
    flex: none;
    line-height: 20px;
}

.header .first_head h5 {
    font-size: 18px;
    font-weight: 400;
    line-height: 27px;
    letter-spacing: 0em;
}

.header .first_head span.bold {
    font-weight: 500;
    font-family: 'Poppins-SemiBold';
}

.header .first_head span.font-color {
    color: #E56E19;
    font-weight: 500;
    font-family: 'Poppins-SemiBold';
}

.header .first_head {
    font-size: 18px;
    margin-right: 10px;
}

.header .second_head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 20px;
    margin-right: 10px;
}

.header .head_bg {
    background: #2F2B41;
    padding: 7px 20px;
    border-radius: 8px
}

.header .second_head .inner_head span {
    margin-right: 11px;
}

.header .third_head {
    font-size: 20px;
    background: linear-gradient(#2D2264, #131022);
}

.header .third_head span {
    margin-right: 11px;
}

.header .second_head .inner_head.one {
    margin-right: 50px;
    width: unset;
}
.header .second_head .inner_head {
    width: 120px;
}
.middle_section_wrap {
    height: 100%;
}

.middle_section {
    padding-top: 59px;
    padding-bottom: 60px;
    height: 100%;
    position: relative;
}

.middle_section .main_block .row {
    align-items: center;
    margin: 0;
}

.middle_section .main_block .grid.two {
    text-align: right;
    padding-right: 10%;
    overflow: hidden;
}

.middle_section .main_block .grid.one {
    padding-left: 10%;
}

/*.middle_section .main_block .grid.two {
    padding-right: 10%;
}*/

.footer_wrap {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1;
}

.footer {
    color: #fff;
    padding: 10px;
}

.footer ul {
    display: flex;
    list-style: none;
    justify-content: space-between;
    padding: 0;
    margin: 0;
}

.footer ul li.copy_right {
    border-radius: 8px;
    background: #2F2B41;
    font-size: 18px;
    padding: 6px 20px;
    position: relative;
    flex: 1;
    margin-right: 10px;
}

.footer ul li.footer_btn_one {
    border-radius: 8px;
    font-size: 18px;
    /*    padding: 6px 20px;*/
    background: linear-gradient(#2D2264, #131022);
    flex: none;
    margin-right: 10px;
}

.footer ul li.footer_btn_two {
    border-radius: 8px;
    font-size: 18px;
    padding: 6px 20px;
    background: linear-gradient(#2D2264, #131022);
    flex: none;
    margin-right: 10px;
}

.footer ul li.footer_btn_three {
    background: #14216A;
    width: 60px;
    height: 60px;
    font-size: 20px;
    text-align: center;
    line-height: 49px;
    border-radius: 50px;
    margin-top: -20px;
    flex: none;
}

.footer ul li.footer_btn_one span {
    margin-right: 10px;
}

.footer ul li.footer_btn_two span {
    margin-right: 10px;
}

.footer ul li.copy_right span.top_txt {
    position: absolute;
    top: -50px;
    left: 25px;
    font-size: 40px;
    font-family: Montserrat;
    text-transform: uppercase;
    text-shadow: -1px 0 #009eb217, 0 1px #009eb217, 1px 0 #009eb217, 0 -1px #009eb217;
    color: #1a1729;
}

.footer ul li.footer_btn_one .first {
    background: linear-gradient(#2D2264, #131022);
    width: 60px;
}

.footer ul li.footer_btn_one .second {
    background: #304FFD;
    /*    font-size: 28px;*/
    width: 46px;
}

.footer ul li.footer_btn_one .third {
    background: #49C96D;
    /*    font-size: 23px;*/
    width: 46px;
}

.footer ul li.footer_btn_one .btn {
    line-height: 1;
    border: none;
    color: #fff;
    font-size: 26px;
}


.main_block .grid.two .grid-gallery .row {
    justify-content: center;
}

.main_block .grid.two .grid-gallery .holder {
    margin: 10px;
    border: 5px solid #fff;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
    transition: border 0.3s linear;
}

.main_block .grid.two .grid-gallery .holder .zoom-eff {
    transform: scale(1.4);
}

.main_block .grid.two .grid-gallery .holder .zoom-eff .zoom {
    transition: 0.3s linear;
}

.main_block .grid.two .grid-gallery .holder:hover .zoom-eff .zoom {
    transform: scale(1.3);
}

.main_block .grid.two .grid-gallery .holder img {
    width: 100%;
    transform: rotate(316deg);
}

.middle_section .main_block .grid.two .grid-gallery {
    transform: rotate(45deg);
    width: 75%;
    margin: 0 0 0 auto;
}

.grid-gallery .img-lable {
    position: absolute;
    top: 43px;
    right: 10px;
    color: #fff;
    transform: rotate(316deg);
    width: 110px;
    line-height: 1;
    z-index: 1;
    font-size: 29px;
    color: white;
    text-shadow:
        2px 2px 0 #000,
        -1px -1px 0 #000,
        1px -1px 0 #000,
        -1px 1px 0 #000,
        1px 1px 0 #000;
    display: none;
}

.grid-gallery .holder:hover .img-lable {
    display: block;
    transition: 0.3s linear;
}

.main_block .grid.two .grid-gallery .holder:hover {
    border: 5px solid #53c5b9;
}

@media (max-width: 1600px) {
    .middle_section .main_block .grid.one img {
        width: 70%;
    }

    .middle_section .main_block .grid.two img {
        width: 70%;
    }
}

@media (max-width: 1400px) {
    .grid-gallery .img-lable {
        font-size: 21px;
    }

    .main_block .grid.two .grid-gallery .holder:hover {
        border: 4px solid #53c5b9;
    }

    .header .first_head {
        font-size: 14px;
    }

    .header .second_head {
        font-size: 15px;
    }

    .header .third_head {
        font-size: 15px;
    }
    .header .head_bg {
        padding: 2px 20px;
    }

    .bone_cut .profile ul {
        font-size: 17px;
    }

    .bone_cut .profile .fs-16 {
        font-size: 15px;
    }

    .footer ul li.copy_right {
        font-size: 14px;
        padding: 3px 20px;
    }

    .footer ul li.footer_btn_two {
        font-size: 14px;
        padding: 3px 20px;
    }

    .footer ul li.footer_btn_one .btn {
        font-size: 14px;
    }

    .footer ul li.copy_right span.top_txt {
        top: -32px;
        font-size: 26px;
    }
    .footer ul li.footer_btn_three {
        width: 40px;
        height: 40px;
        line-height: 36px;
    }
    .footer_btn_three img {
        height: 20px;
    }
}

@media (max-width: 1200px) {
    .grid-gallery .img-lable {
        font-size: 19px;
        top: 30px;
        right: 1px;
    }
}

/*-------------------LANDING PAGE END-----------------*/



/*-------------------BONE CUT START-------------------*/

.bone_cut .profile ul {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0px 10px;
    font-size: 20px;
    border-bottom: 1px solid #585C7B;
}

.bone_cut .profile {
    padding: 0px 80px;
    position: absolute;
    width: 100%;
    top: 59px;
}

.bottom_btn {
    display: flex;
    gap: 10px;
    justify-content: space-between;
    align-items: center;
    padding: 0 80px;
    position: absolute;
    width: 100%;
    bottom: 100px;
}

.bottom_btn .blank {
    flex: 1;
    width: 100%;
    height: 1px;
    background: #585C7B;
    position: relative;
}

.bottom_btn .blank:after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    background: #585C7B;
    right: 0;
    top: -2.5px;
    border-radius: 50%;
}

.bottom_btn .btn {
    color: #fff;
    flex: none;
    background: linear-gradient(#585C7B -70%, #1B173137);
    padding: 8px 25px;
    border: 1px solid #585C7B;
    font-size: 21px;
    font-family: 'Poppins-Regular';
}

.x-ray {
    font-size: 28px;
    color: #B4B7C9;
}

.bone_cut .main_block .grid img {
    /* height: 580px; */
    height: 500px;
}

@media (min-width: 1400px) and (max-width: 1800px) {
    .bone_cut .main_block .grid img {
        height: 400px;
    }
}
@media (max-width: 1400px) {
    .bone_cut .main_block .grid img {
        height: 280px;
    }

    .x-ray {
        font-size: 22px;
    }

    .bottom_btn .btn {
        padding: 8px 25px;
        font-size: 14px;
    }
    .bottom_btn  {
        bottom: 70px;
    }
}


/*-------------------BONE CUT END---------------------*/



/*----------------MULTIPLE PAGE START-----------------*/
.multiple_page .main_block .grid img {
/*    width: 15%;*/
/*    height: 580px;*/
}

.green_border {
    border: 1px solid #49c96d !important;
}

.danger_border {
    border: 1px solid #FD7972 !important;
}

.check_box {
    position: absolute;
    top: 189px;
    right: 100px;
/*    background: #1B172E;*/
    background-color: #000;
    padding: 20px 30px;
    border-radius: 15px;
    border: 2px solid #2F2B41;
}

.form-group input {
    padding: 0;
    height: initial;
    width: initial;
    margin-bottom: 0;
    display: none;
    cursor: pointer;
}

.form-group label {
    position: relative;
    cursor: pointer;
    font-size: 22px;
}

.form-group label:before {
    content: '';
    -webkit-appearance: none;
    background-color: transparent;
    border: 1px solid #b4b7c9a6;
    border-radius: 5px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
    padding: 15px;
    display: inline-block;
    position: relative;
    vertical-align: middle;
    cursor: pointer;
    margin-right: 20px;
}

.form-group input:checked+label:after {
    content: '';
    display: block;
    position: absolute;
    top: 8px;
    left: 14px;
    width: 7px;
    height: 16px;
    border: solid #fff;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

 .check_box .form-group {
    padding: 6px 0px;
} 
/*
.form-group:hover label:before {
    border: 2px solid #00FFF5 !important;

}

.form-group:hover label {
    color: #00FFF5;
}

.form-group-hover-label {
    color: #00FFF5;
}
/* .form-group label:before {
    border: 2px solid #00FFF5;
} */
/* .form-group.txt_color {
    color: #00FFF5;
}  */

/* Add a class to the form-group when the checkbox is checked */
.form-group input:checked + label {
    color: #00FFF5; /* Change the color of the label */
}

.form-group input:checked + label:before {
    border: 1px solid #00FFF5; /* Change the border color of the checkbox */
}

/* Style the label when hovered (optional) */
.form-group label:hover {
    color: #00FFF5;
}

.note {
    position: absolute;
    /* bottom: 230px; */
    bottom: 176px;
    left: 80px;
/*    background: #1B172E;*/
    background: #000;
    padding: 15px 25px;
    border-radius: 15px;
    font-size: 20px;
    border: 2px solid #2F2B41;
    color: #B4B7C9;
}

.note p {
    margin-bottom: 12px;
    color: #B4B7C9;
}

.note_txt {
    color: #E56E19 !important;
}

.pointer_bg {
    background: #131022 !important;
}

span.pro_label {
    position: absolute;
    bottom: -36px;
    padding: 6px 15px;
    right: 0;
    text-align: right;
    background: #00FFF5;
    color: #000;
    font-size: 16px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    font-family: 'Poppins-Regular';
}

.multiple_page .bottom_btn .blank:before {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    background: #585C7B;
    left: 0;
    top: -2.5px;
    border-radius: 50%;
}

.multiple_page .middle_section:not(:first-child) .main_block .grid img {
    animation: fadeInAnimation ease 0.5s;
    animation-fill-mode: forwards;
    animation-duration: 0.5s;
    position: relative;
}

@keyframes fadeInAnimation {
    0% {
        opacity: 0; left:20px;
    }
    100% {
        opacity: 1; left:0px;
    }
}

@media (max-width: 1600px) {
    .check_box {
        padding: 7px 11px;
        top: 153px;
    }

    .form-group label {
        font-size: 14px;
    }

    .form-group label:before {
        padding: 7px;
        margin-right: 10px;
        border: 1px solid #fff;
    }

    .form-group input:checked+label:after {
        top: 7px;
        left: 6px;
        width: 4px;
        height: 7px;
        border-width: 0 1px 1px 0;
    }
    .form-group label:before {
        border-radius: 3px;
    }
    .form-group input:checked + label:before {
        border: 1px solid #00FFF5;
    }

    .note {
        bottom: 180px;
        padding: 6px 15px;
        font-size: 13px;
    }

    .note p {
        margin-bottom: 6px;
    }

    .check_box .form-group {
        padding: 1px 0px;
    }
    span.pro_label {
        bottom: -27px;
        padding: 4px 7px;
        font-size: 12px;
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;
    }
}
@media (max-width: 1200px) {
    .check_box {
        right: 60px;
    }
}

/*-----------------MULTIPLE PAGE END------------------*/



/*--------------SECOND INNER PAGE START---------------*/
.resect_proximal .main_block .grid img {
    width: unset;
    height: 250px;
}
.resect_proximal .main_block .grid {
    /* background: #2f2b4152; */
    background: #000;
    margin: 20px 0px;
    padding: 0px 0 70px 0;
    border-radius: 16px;
    border: 2px solid #2F2B41;
}
.resect_proximal .main_block .note {
    position: unset;
    display: inline-block;
/*    margin: 0 65px;*/
}
.depth {
    background: #2F2B41;
    display: inline-block;
    padding: 4px 4px;
    text-align: center;
    border-radius: 8px;
    font-size: 26px;
    font-family: 'Poppins-Regular';
}
.depth_bg {
    background: #131022;
    padding: 3px 30px;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
}
.depth_btn {
    margin: 10px 0 5px 0px;
}
.depth ul li {
    display: inline;
    background: linear-gradient(#585C7B -70%, #1B173137);
    padding: 5px 15px;
    border: 1px solid #585C7B;
    border: 1px solid #585C7B;
    border-radius: 6px;
    font-size: 26px;
}
.depth ul li:nth-child(2) {
    background: transparent;
    border: none;
}
.depth .depth_btn .small-txt {
    padding-top: 0;
    font-size: 14px;
    vertical-align: super;
}
.resect_proximal .main_block {
    margin: 0 65px;
    position: relative;
}
.resect_proximal .main_block .grid .sub-grid .depth img {
    height: unset;
}
.sub-grid .depth {
    border: 1px solid #2F2B41;
    font-size: 19px;
    padding: 0;
}
.sub-grid {
    text-align: left;
    display: table-cell;
    padding: 20px;
}
.sub-grid .depth ul li {
    background: none;
    border: none;
    font-size: 19px;
}
.sub-grid .depth_bg {
    padding: 0 15px;
}
.depth.btn_box {
    margin-left: -100px;
}
.btn_box_spacing .depth_bg {
    padding: 0px 40px;
}
.sub-grid .depth .depth_btn .small-txt {
    font-size: 11px;
}
.sub-grid .depth_btn {
    margin: 6px 0 3px 0px;
}
.grid.position-relative .img {
    position: absolute;
    top: 25%;
    font-size: 26px;
    line-height: 26px;
}
.grid.position-relative .img.left {
    left: 23%;
    text-align: right;
}
.grid.position-relative .img.right {
    right: 23%;
    text-align: left;
}
.grid.position-relative .txt {
    position: absolute;
    top: 50%;
    font-size: 20px;
}
.grid.position-relative .txt.right {
    right: 30px;
}
.grid.position-relative .txt.left {
    left: 30px;
}
.grid.position-relative .img .f_size {
    font-size: 20px !important;
}
.bottom_btn_left {
    position: absolute;
    bottom: 15px;
    left: 25px;
    color: #585C7B;
    font-size: 20px;
    font-family: 'Poppins-Regular';
}

@media (max-width: 1600px) {
    .sub-grid {
        padding: 6px;
    }
    .sub-grid .depth {
        font-size: 14px;
    }
    .sub-grid .depth_bg {
        padding: 0 10px;
    }
    .sub-grid .depth .depth_btn .small-txt {
        font-size: 8px;
    }
    .sub-grid .depth_btn {
        margin: 1px 0 1px 0px;
    }
    .resect_proximal .main_block .grid img {
        height: 140px;
    }
    .resect_proximal .main_block .grid {
        padding: 0px 0 10px 0;
    }
    .depth.btn_box {
        margin-left: -75px;
    }
    .depth ul li {
        font-size: 16px;
        padding: 2px 10px;
    }
    .depth ul li img {
        width: 8px;
    }
    .depth {
        padding: 3px 3px;
        font-size: 16px;
    }
    .depth_btn {
        margin: 4px 0 1px 0px;
    }
    .grid.position-relative .img .f_size {
        font-size: 14px !important;
    }
    .grid.position-relative .img {
        top: 33%;
        line-height: 21px;
        font-size: 19px;
    }
    .grid.position-relative .txt {
        font-size: 16px;
    }
    .btn_box_spacing .depth_bg {
        padding: 0px 30px;
    }
    .sub-grid .depth ul li {
        font-size: 16px;
    }
    .depth .depth_btn .small-txt {
        font-size: 11px;
    }
    .resect_proximal .main_block .grid {
        margin: 13px 0px;
    }
    .bottom_btn_left {
        bottom: 10px;
        left: 15px;
        font-size: 14px;
    }
}
/*---------------SECOND INNER PAGE END---=------------*/


/*------------SECOND MULTIPLE PAGE START--------------*/
.note ul {
    list-style: disc;
    padding-left: 2rem;
}
.hip_in_flexion .main_block .grid img {
    height: 410px;
}


/*.multiple_page .middle_section.hip_center .main_block .grid img {
    animation: keyName 0.5s ease-out forwards;
}
@keyframes keyName {
  
  100% {
    transform: scale(1.1);
  }
}*/

.multiple_page .middle_section.lateral_epicondyle .main_block .grid img {
    animation: fadeInAnimation1 ease 0.5s;
}
@keyframes fadeInAnimation1 {
    0% {
        opacity: 0; right:20px;
    }
    100% {
        opacity: 1; right:0px;
    }
}
.multiple_page .middle_section.femur_center .main_block .grid img {
    animation: fadeInAnimation2 ease 0.5s; 
}
@keyframes fadeInAnimation2 {
    0% {
        opacity: 0; top:-10px;
    }
    100% {
        opacity: 1; top:0px;
    }
}

.multiple_page .middle_section.medial_distal_condyle .main_block .grid img {
    animation: fadeInAnimation3 ease 0.5s; 
}
@keyframes fadeInAnimation3 {
    0% {
        opacity: 0; top:-10px;
    }
    100% {
        opacity: 1; top:0px;
    }
}
.multiple_page .middle_section.lateral_distal_condyle .main_block .grid img {
    animation: fadeInAnimation4 ease 0.5s; 
}
@keyframes fadeInAnimation4 {
    0% {
        opacity: 0; right:-10px;
    }
    100% {
        opacity: 1; right:0px;
    }
}
.multiple_page .middle_section.medial_posterior_condyle .main_block .grid img {
    animation: keyName 0.5s ease-out forwards;
}
@keyframes keyName {
  
  100% {
    transform: scale(1.1);
  }
}
.multiple_page .middle_section.lateral_posterior_condyle .main_block .grid img {
    animation: fadeInAnimation4 ease 0.5s; 
}
@keyframes fadeInAnimation4 {
    0% {
        opacity: 0; top:-10px;
    }
    100% {
        opacity: 1; top:0px;
    }
}
.multiple_page .middle_section.anterior_cortex .main_block .grid img {
    animation: fadeInAnimation4 ease 0.5s; 
}
@keyframes fadeInAnimation4 {
    0% {
        opacity: 0; 
    }
    100% {
        opacity: 1;
    }
}


@media (max-width: 1600px) {
    .hip_in_flexion .main_block .grid img {
        height: 240px;
    }
    /*.second_page .check_box {
        overflow-y: scroll;
        height: 255px;
    }*/
}
@media (max-height: 900px) {
    /*.second_page .check_box {
        overflow-y: scroll;
        height: 255px;
    }*/
}
/*-------------SECOND MULTIPLE PAGE END---------------*/

/* ===========================================Assess initial leg alignments start=========================================== */
.img-input-text{
    position: absolute;
    top: 2px;
    left: 0;
}
.img-input-text input{
    height: 80px;
    width: 47%;
    border-radius: 7px;
    border: 1px solid white;
    color: #fff;
    font-size: 35px;
    padding: 0px 32px;
    font-weight: 800;
    text-align: left;
    background-color: rgb(19,16,35, 0.8);
    position: relative;
}
.img-input-text input::placeholder,
.upper-input input::placeholder
 {
    color: #fff;
    font-size: 30px;
    font-weight: 800;
    opacity: 1; 
}
.text-inner-content{
    position: absolute;
    right: 30%;
    bottom: 7px;
    font-size: 20px;
    font-weight: 600;
}
.left-top{
    position: absolute;
    top: 35%;
    width: 25%;
    left: 30%;
}
.left-bottom{
    position: absolute;
    bottom: 16%;
    width: 25%;
    left: 30%;
}
.right-top{
    position: absolute;
    top: 35%;
    width: 25%;
    right: 10%;
}
.right-bottom{
    position: absolute;
    bottom: 16%;
    width: 25%;
    right: 10%;
}
.upper-input input {
    width: 100%;
    background-color: rgb(19,16,35, 0.2);
    font-size: 35px;
    color: white;
    font-weight: 800;
    border: none;
    position: relative;
}
.upper-input input::placeholder{
    color:white;
    font-size: 35px;
    opacity: 0.4;
}
span.graph-inner-content {
    font-size: 12px;
    font-weight: 800;
    position: absolute;
    left: 48%;
    bottom: 0%;
}
.bottom-left-content ul li:not(:last-child){
    border-right: 1px solid #4e4a58;
    font-size: 10px;
    text-align: center;
}
.bottom-left-content ul li{
    padding: 0px 28px;
}
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}  
.switch input { 
    opacity: 0;
    width: 0;
    height: 0;
} 
.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #7e7c87;
    -webkit-transition: .4s;
    transition: .4s;
    width: 85%;
    height: 26px;
} 
.slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 3px;
    bottom: 3px;
    background-color: #120f20;
    -webkit-transition: .4s;
    transition: .4s;
} 
input:checked + .slider {
    background-color: #2196F3;
} 
input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
} 
input:checked + .slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
} 
  /* Rounded sliders */
.slider.round {
    border-radius: 34px;
} 
.slider.round:before {
    border-radius: 50%;
}
.row.text-center.asses-box {
    display: flex;
    margin: 0px 100px;
}
.asses-box img{
    max-height: 513px;
}
.asses-right-box .rotate-btn {
    right: 20%;
    top: 20px;
    font-size: 15px;
    padding: 7px;
    left: inherit;
}

.center-img img,
.setting-btn {
    max-height: 48px;
}
.asses-box .Bone-text {
    position: absolute;
    content: "";
    top: 33px;
    left: 50%;
    width: inherit;
    transform: translate(-50%, -50%);
}
.asses-box .Bone-text input {
    background-color: rgb(19,16,35, 0.8);
    color: #fff;
    width: 130px;
    height: 65px;
    font-size: 40px;
    font-weight: 800;
    text-align: center;
    border-radius: 8px;
    border-right: none;
    border: 1px solid #7a7983;
}
.Bone-text .input-group-text {
    display: flex;
    align-items: center;
    padding: 0;
    padding-right: 6px;
    font-size: 14px;
    font-weight: 400;
    line-height: inherit;
    color: #fff;
    text-align: end;
    white-space: nowrap;
    background-color: #221f2f;
    border: 1px solid #7a7983;
    border-left: none;
    border-radius: 8px;
}
.asses-box .Bone-text .input-group-text {
    background-color: rgb(19,16,35, 0.8);
}
.center-img {
    margin-left: 60px;
}
.bottom-right-button{
    display: flex;
    gap: 10px;
}
@media only screen and (max-width: 1600px){
    /* .center-img img {
        max-height: 60px;
    } */
    .bottom-right-button{
        display: flex;
        gap: 10px;
    }
    .bottom-right-button img {
        max-height: 34px;
    }
    span.graph-inner-content {
        left: 44%;
    }
    .bottom-left-content ul li:not(:last-child) {
        font-size: 10px;
    }
    .text-center.fs-16{
        font-size: 14px;
    }
    .slider {
        width: 80%;
        height: 20px;
    }
    .switch {
        width: 50px;
        height: 20px;
    }
    .slider:before {
        height: 15px;
        width: 15px;
    }
    .text-inner-content {
        font-size: 14px;
    }
    .upper-input input::placeholder{
        font-size: 30px;
        color: white;
        opacity: 0.5;
    }
    .upper-input input {
        font-size: 30px;
    }
    .asses-box img {
        max-height: 500px;
    }
    .img-input-text input {
        height: 65px;
        width: 50%;
    }
    .bottom-left-content ul li {
        padding: 0px 10px;
    }
    
}
@media only screen and (max-width: 1400px){
    /* .bottom_btn.assess-bottom {
        bottom: 100px!important;
    } */
    .bottom-left-content ul li:not(:last-child) {
        font-size: 8px;
    }
    .text-center.fs-16 {
        font-size: 11px;
    }
    .asses-box img {
        max-height: 250px;
    }
    .center-img img {
        max-height: 36px;
    }
    .asses-box .Bone-text input {
        background-color: rgb(19,16,35, 0.8);
        color: #fff;
        width: 110px;
        height: 60px;
        font-size: 33px;
        font-weight: 800;
        text-align: center;
        border-radius: 8px;
        border-right: none;
        border: 1px solid #7a7983;
    }
    .upper-input input::placeholder{
        font-size: 20px;
        color: white;
        opacity: 0.5;
    }
    .upper-input input {
        font-size: 20px;
    }
    .center-img {
        margin-left: 20px;
    }
}
@media only screen and (max-width: 1200px){
    .center-img img {
        max-height: 30px;
    }
    span.graph-inner-content {
        left: 56%;
    }
    
    .asses-box img {
        max-height: 220px;
    }
    .bottom-left-content ul li:not(:last-child) {
        font-size: 6px;
    }
    .bottom-left-content ul li {
        padding: 0px 8px;
    }
    .text-center.fs-16{
        font-size: 10px;
    }
    .switch {
        width: 50px;
        height: 20px;
    }
    .slider {
        width: 85%;
        height: 20px;
    }
    .slider:before {
        height: 14px;
        width: 14px;
        left: 3px;
        bottom: 3px;;
    }
    .img-input-text input {
        font-size: 20px;
        height: 40px;
        padding: 0px 20px;
    }
    .text-inner-content {
        font-size: 14px;
    }
    .asses-box .Bone-text input {
        background-color: rgb(19,16,35, 0.8);
    color: #fff;
    width: 80px;
    height: 54px;
    font-size: 33px;
    font-weight: 800;
    text-align: center;
    border-radius: 8px;
    border-right: none;
    border: 1px solid #7a7983;
    }
}

/* TRK Screen 2 CSS Start */
.new-main_block {
    margin: 0 200px 0 100px;
}
.bottom_btn .blank-none.blank {
    background: transparent;
}
.bottom_btn .blank-none.blank:before,
.bottom_btn .blank-none.blank:after {
    display: none !important;
}

.bottom-icon .icon-img {
    width: 53px;
    height: 53px;
    margin: 0 5px;
}
.bottom-icon .icon-img.active {
    border: 1px solid #2F2B41;
    border-radius: 6px;
    background: linear-gradient(#585C7B -70%, #1B173137);
}
/* TRK Screen 2 CSS End */

/* TRK Screen 3 CSS Start */
.pb-80 {
    padding-bottom: 80px;
}
.main-Bone-box {
    margin-left: 100px;
}
.Bone-box-style {
    position: relative;
    margin-top: 10px;
}
.Bone-box-style .bone-img {
    height: 230px;
}
.Bone-text {
    position: absolute;
    content: "";
    top: -26px;
    left: 50%;
    z-index: 1;
    transform: translate(-50%, -50%);
}
.Bone-text input {
    background: #221f2f;
    color: #fff;
    width: 84px;
    height: 42px;
    font-size: 26px;
    font-weight: 700;
    text-align: center;
    border-radius: 8px;
    border-right: none;
    border: 1px solid #7a7983;
}
.Bone-text input::placeholder {
    color: #fff;
    opacity: 0.4;
}
.Bone-text .input-group-text {
    display: flex;
    align-items: center;
    padding: 0;
    padding-right: 6px;
    font-size: 14px;
    font-weight: 400;
    line-height: inherit;
    color: #fff;
    text-align: end;
    white-space: nowrap;
    background-color: #221f2f;
    border: 1px solid #7a7983;
    border-left: none;
    border-radius: 8px;
}
.Bone-text input:focus-visible {
    outline: -webkit-focus-ring-color auto 0px;
}
.bottom-input {
    top: inherit;
    bottom: 0;
}
.single-bottom-input {
    bottom: -75px;
}
.Bone-box-style .input-btn span {
    width: 250px;
    padding-left: 8px;
}
.two-input {
    flex-wrap: unset;
}
.hka-border {
    position: relative;
    font-size: 20px;
    font-weight: 700;
    text-align: left;
    padding-left: 80px;
}
.hka-border::after {
    position: absolute;
    content: "";
    top: 50%;
    left: 0;
    height: 2px;
    width: 74px;
    background: #fff;
}
.hka-border::before {
    position: absolute;
    content: "";
    top: 50%;
    left: 206px;
    right: 0;
    height: 2px;
    width: 74%;
    background: #fff;
}
.rotate-btn {
    position: absolute;
    top: 0;
    left: 0;
    display: grid;
    z-index: 1;
    font-size: 24px;
    padding: 10px;
    border: 1px solid;
    border-radius: 12px;
}
.rotate-btn i {
    transform: rotate(45deg);
}
.graph-text input {
    font-size: 38px;
    font-weight: bold;
}
.graph-text .input-group-text {
    font-size: 17px;
    font-weight: bold;
}
.graph-input input {
    background-color: transparent;
    border: none;
}
.graph-input .input-group-text {
    background-color: transparent;
    border: none;
}
.graph-text input {
    width: 100px;
}
.graph-top-left-input {
    top: 42%;
    left: 36%;
}
.graph-top-right-input {
    top: 42%;
    left: inherit;
    right: 8%;
}
.graph-bottom-left-input {
    top: inherit;
    bottom: 14%;
    left: 36%;
}
.graph-bottom-right-input {
    top: inherit;
    bottom: 14%;
    left: inherit;
    right: 8%;
}
/*.asses-box img.graph-img {
    max-height: 100%;
}*/ 
.tkr-screen-3-section .row.text-center {
    margin: 0px 100px;
}
.tkr-screen-3-section {
    padding-top: 30px;
}
@media screen and (min-device-width: 1400px) and (max-device-width: 1800px) { 
    .tkr-screen-3-section {
        padding-top: 0;
    }
    .tkr-screen-3-section .Bone-text input {
        width: 60px;
        height: 30px;
        font-size: 18px;
    }
    .tkr-screen-3-section .pb-80 {
        padding-bottom: 50px;
    }
    .tkr-screen-3-section .graph-img {
        height: 500px;
    }
    .tkr-screen-3-section .main-Bone-box {
        margin-top: 60px;
    }
}
@media only screen and (max-width: 1400px){
    .tkr-screen-3-section .row.text-center {
        margin-top: 84px;
    }
    .tkr-screen-3-section {
        padding-top: 0;
    }
    .tkr-screen-3-section .pb-80 {
        padding-bottom: 0px;
    }
    .tkr-screen-3-section .graph-img {
        height: 310px;
    }
    .rotate-btn {
        font-size: 18px;
        padding: 4px;
        border-radius: 10px;
    }
    .tkr-screen-3-section .main-Bone-box {
        margin-left: 0;
    }
    .tkr-screen-3-section .graph-text input {
        font-size: 24px;
        font-weight: bold;
    }
    .tkr-screen-3-section .Bone-text input {
        width: 60px;
        height: 30px;
        font-size: 20px;
    }
    .tkr-screen-3-section .graph-top-left-input,
    .tkr-screen-3-section .graph-bottom-left-input {
        left: 40%;
    }
    .graph-text .input-group-text {
        font-size: 16px;
    }
    .tkr-screen-3-section .graph-bottom-right-input,
    .tkr-screen-3-section .graph-top-right-input {
        right: 0px;
    }
    .hka-border {
        font-size: 18px;
    }
}
@media only screen and (max-width: 1200px){
    .rotate-btn {
        top: -10px;
        left: -10px;
        font-size: 18px;
    }
    .tkr-screen-3-section .Bone-text input {
        width: 40px;
        font-size: 14px;
    }
}
@media only screen and (max-width: 991px){
    .tkr-screen-3-section .Bone-text input {
        width: 48px;
        font-size: 10px;
    }
    .Bone-text .input-group-text {
        font-size: 10px;
    }
    .rotate-btn {
        padding: 6px;
        border-radius: 8px;
    }
    .hka-border::before {
        width: 60%;
    }
}
/* TRK Screen 3 CSS End */

/* Select Operating Position Screen css Start */
.operating-position-box {
    border: 2px solid #2F2B41;
    border-radius: 16px;
    padding: 40px;
    text-align: center;
}
.operating-position-box:hover {
    border: 2px solid #00FFF5;
}
.operating-position-box:hover .btn-inline {
    background: #00FFF5;
    color: #131022;
    border: 1px solid #00FFF5;
}
.operating-position-box-img {
    /* width: 380px; */
    height: 400px;
}
.btn-inline {
    padding: 10px 30px;
    color:#B4B7C9;
    background: #131022;               
    border: 1px solid #FFFFFF;
    border-radius: 8px;
    font-size: 28px;
    font-weight: 600;
    text-transform: capitalize;
    box-sizing: border-box;
    transition: all 0.5s;
}
/* Select Operating Position Screen css End */

/* System Setup Screen css Start */
.system-setup-screen .grid {
    margin-left: 100px;
    margin-top: -140px;
}
.system-setup-screen .bone_cut .main_block .grid img {
    height: 440px;
}
.system-setup-screen .note {
    border-radius: 10px;
    bottom: 160px;
}
.system-setup-screen .check_box {
    padding: 30px;
}
.system-setup-screen .form-group label:before {
    padding: 14px;
    border: 2px solid #00FFF5;
}
.system-setup-screen .form-group input:checked+label:after {
    top: 8px;
    left: 12px;
    width: 8px;
    height: 16px;
    border-width: 0 2px 2px 0;
}
.system-setup-screen .form-group input:checked + label:before {
    border: 2px solid #00FFF5;
}
.supine-img {
    position: absolute;
    bottom: 260px;
    right: 150px;
    width: 364px;
}
/* System Setup Screen css End */

/* System Setup Screen Two css Start */
.system-setup-screen-two .supine-img {
    bottom: 210px;
}
/* .system-setup-screen-two .supine-img img {
    height: 170px;
} */
/* System Setup Screen Two css End */

/* Pelvis Registration Screen css Start */
.form-group label.rounded-circle:before {
    border-radius: 50%;
    border: 1px solid #fff;
}
.form-group input:checked+label.rounded-circle:after {
    top: 8px;
    left: 5px;
    border-width: 10px;
    border-radius: 50%;
    background: #fff;
}
.form-group input:checked + label.rounded-circle:before {
    border: 1px solid #00FFF5;
}
/* Pelvis Registration Screen css End */

/* Pelvis Registration Screen Two css Start */
.w-92 {
    width: 92%;
}
.pelvis-registration-screen .pr2-img-section {
    margin-top: -200px;
}
.bone_cut .main_block .grid .pr-2-img {
    height: 410px;
}
.pelvis-registration-screen .note.w-92 {
    bottom: 194px;
}
@media screen and (min-device-width: 1400px) and (max-device-width: 1800px) { 
    .pelvis-registration-screen .bone_cut .main_block .grid .pr-2-img {
        height: 230px;
    }
    .pelvis-registration-screen .pr2-img-section {
        margin-top: -100px;
    }
    .pelvis-registration-screen .note.w-92 {
        bottom: 186px;
    }
    .pelvis-registration-screen .w-92 {
        width: 89%;
    }
}
@media only screen and (max-width: 1400px){ 
    .pelvis-registration-screen .note.w-92 {
        bottom: 150px;
    }
    .pelvis-registration-screen .bone_cut .main_block .grid .pr-2-img {
        height: 230px;
    }
    .pelvis-registration-screen .pr2-img-section {
        margin-top: -100px;
    }
    .pelvis-registration-screen .w-92 {
        width: 89%;
    }
}
@media only screen and (max-width: 1200px){
    .pelvis-registration-screen .w-92 {
        width: 86%;
    }
}
/* .pelvis-registration-line {
    position: relative;
}
.pelvis-registration-line::before {
    position: absolute;
    content: "";
    top: 91px;
    left: 68%;
    width: 925px;
    height: 2px;
    background: #00FFF5;
}
.pelvis-registration-line::after {
    position: absolute;
    content: "";
    top: 125px;
    left: 33%;
    width: 970px;
    height: 2px;
    background: #00FFF5;
    transform: rotate(1deg);
} */
/* Pelvis Registration Screen Two css End */

/* Mid Axial Body Plane Screen css Start */
.new-note {
    position: inherit;
    margin-left: 200px;
    margin-right: 72px;
    border-radius: 10px;
    padding: 20px 25px;
}
.new-note .note_txt {
    font-weight: 600;
}
@media screen and (min-device-width: 1400px) and (max-device-width: 1800px) { 
    .note.new-note {
        margin-left: 228px;
    }
}
@media only screen and (max-width: 1400px){ 
    .note.new-note {
        margin-left: 162px;
    }
}
@media only screen and (max-width: 1200px){ 
    .note.new-note {
        margin-left: 70px;
    }
}
/* Mid Axial Body Plane Screen css End */

/* Anterior Pelvic and Functional Plane css Start */
.anterior-pelvic-grid {
    background: #2f2b4152;
    border-radius: 16px;
    padding: 20px 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.bone_cut .main_block .anterior-pelvic-grid img {
    height: 250px;
}
.anterior-pelvic-img-3 img{
    height: 300px !important;
    margin-top: 30px;
}
.mr-13 {
    margin-right: -13px;
}
.ms-75 {
    margin-left: 75px;
}
.me-75 {
    margin-right: 75px;
}
.anterior-pelvic-section .switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 30px;
}
.anterior-pelvic-section input:checked + .slider {
    background-color: #385399;
}
.anterior-pelvic-section .switch .slider:before {
    left: -5px;
    bottom: 4px;
    height: 18px;
    width: 18px;
    background-color: #fff;
}
.anterior-pelvic-section .bottom_btn .btn {
    font-size: 20px;
}
@media screen and (min-device-width: 1400px) and (max-device-width: 1800px) { 
    .bone_cut .main_block .anterior-pelvic-grid img {
        height: 200px;
    }
    .anterior-pelvic-img-3 img {
        height: 150px !important;
        margin-top: 40px;
    }
    .anterior-pelvic-section .switch .slider:before {
        bottom: 2px;
        height: 16px;
        width: 16px;
    }
}
@media only screen and (max-width: 1400px){ 
    .bone_cut .main_block .anterior-pelvic-grid img {
        height: 170px;
    }
    .anterior-pelvic-img-3 img {
        height: 150px !important;
        margin-top: 0;
    }
}
@media only screen and (max-width: 1200px){
    .anterior-pelvic-section .sub-grid .depth_bg,
    .anterior-pelvic-section .sub-grid .depth ul li {
        font-size: 10px;
    }
}
/* Anterior Pelvic and Functional Plane css End */

/* Register Acetabulum css Start */
.turquoise-blue {
    color: #00FFF5 !important;
}
.points-acetabulum-box .note_txt.fw-normal {
    font-size: 22px;
}
.points-acetabulum-box .new-note {
    margin-left: 280px;
}
@media screen and (min-device-width: 1400px) and (max-device-width: 1800px) { 
    .points-acetabulum-box .new-note {
        margin-left: 200px;
    }
    .points-acetabulum-box .note_txt.fw-normal {
        font-size: 20px;
    }
    .points-acetabulum-box .note p {
        font-size: 18px;
    }
}
@media only screen and (max-width: 1400px){ 
    .points-acetabulum-box .new-note {
        margin-left: 200px;
    }
    .points-acetabulum-box .note_txt.fw-normal {
        font-size: 20px;
    }
    .points-acetabulum-box .note p {
        font-size: 18px;
    }
}
@media only screen and (max-width: 1200px){ 
    .points-acetabulum-box .new-note {
        margin-left: 80px;
    }
    .points-acetabulum-box .note_txt.fw-normal {
        font-size: 16px;
    }
    .points-acetabulum-box .note p {
        font-size: 14px;
    }
}
/* Register Acetabulum css End */

/* =================================Jash add css Start Final Reduction/ Leg Length Assessment=========================================== */
    .joystic-img img {
        height: 60%;
    }
    .joystic-img{
        height: 380px;
    }
@media screen and (max-width: 1800px) { 
    .joystic-img img {
        height: 70%;
    }
    .joystic-img{
        height: 270px;
    }
    .note.new-note.text-start.leg-assessment {
        margin-left: 100px;
    }
}
@media screen and (max-width: 1400px) { 
    .joystic-img img {
        height: 60%;
    }
    .joystic-img{
        height: 180px;
    }
}
.hidden-content{
    visibility: hidden;
}

/* =================================Jash add css End Final Reduction/ Leg Length Assessment=========================================== */
.green {
   color: #49C96D !important;
}
.yellow {
    color: #FFD240 !important;
}
.raisin-black-bg {
    background-color: #1b182b !important;
}
.cup-position-box {
    height: 456px;
    padding: 0 !important;
    margin: 0 0 30px 0 !important;
}
/* .cup-position-box .depth {
    background: #1b182b;
} */
.cup-position-img.cup-position-img-1 img {
    height: 340px !important;
    margin-top: -50px;
}
.cup-position-img img {
    height: 300px !important;
}
.diameter-desc {
    font-size: 22px;
    margin-bottom: 10px;
}
@media screen and (min-device-width: 1400px) and (max-device-width: 1800px) { 
    .cup-position-box {
        height: 364px;
    }
    .cup-position-img img {
        height: 210px !important;
    }
    .cup-position-img.cup-position-img-1 img {
        height: 320px !important;
    }
}
@media screen and (max-width: 1400px) { 
    .cup-position-box {
        height: 364px;
    }
    .cup-position-img img {
        height: 250px !important;
    }
    .cup-position-img.cup-position-img-1 img {
        height: 320px !important;
    }
}
/* Multiple Page2 Buffer Animation CSS Start */
.buffer-img-1 {
    position: relative;
    z-index: 1;
}
.buffer-img-2 {
    position: relative;
    z-index: 2;
}
.buffer-img-3 {
    position: relative;
    z-index: 3;
}
.buffer-img-4 {
    position: relative;
    z-index: 4;
}
.buffer-img-5 {
    position: relative;
    z-index: 5;
}
.buffer-img-6 {
    position: relative;
    z-index: 6;
}
.buffer-img-7 {
    position: relative;
    z-index: 7;
}
.buffer-img-8 {
    position: relative;
    z-index: 8;
}
.buffer-img-9 {
    position: relative;
    z-index: 9;
}
.buffer-img-10 {
    position: relative;
    z-index: 10;
}
.buffer-img-11 {
    position: relative;
    z-index: 11;
}
.buffer-img-12 {
    position: relative;
    z-index: 12;
}
.buffer-img-13 {
    position: relative;
    z-index: 13;
}
.buffer-img-14 {
    position: relative;
    z-index: 14;
}
.buffer-img-15 {
    position: relative;
    z-index: 15;
}
.buffer-img-16 {
    position: relative;
    z-index: 16;
}
.buffer-img-17 {
    position: relative;
    z-index: 17;
}
.buffer-img-18 {
    position: relative;
    z-index: 18;
}
.buffer-img-19 {
    position: relative;
    z-index: 19;
}
.buffer-img-20 {
    position: relative;
    z-index: 20;
}
.buffer-img-21 {
    position: relative;
    z-index: 21;
}
.multiple_page .middle_section:not(:first-child) .main_block .grid.buffer-img img {
    animation: none;
}
/* Multiple Page2 Buffer Animation CSS End */ 

/* TKR Screen Input Button css Start*/
.input-btn {
    background-color: rgb(19,16,35, 0.8);
    color: #fff;
    height: 65px;
    width: 180px;
    font-size: 40px;
    font-weight: 800;
    border-radius: 8px;
    padding: 10px 20px;
    border: 1px solid #7a7983;
}
.input-btn-text {
    font-size: 14px;
    font-weight: 400;
    padding-left: 20px;
}

.Bone-center-grid .Bone-text.bottom-input {
    left: 64%;
}
.deg-input-btn {
    padding: 10px;
    height: 50px;
    font-size:24px;
    width: 192px; 
    color: #7a7982;
    background-color: #221f2f;
}
.deg-input-btn .input-btn-text {
    padding-left: 0px;
    width: 70px !important;
    padding-right: 8px;
}
.deg-input-btn.bl-rounded {
    border-left: none;
}
.w-28 {
    width: 28px;
}
.br-rounded {
    border-radius: 8px 0px 0px 8px;
}
.bl-rounded {
    border-radius: 0px 8px 8px 0px;
}
.text-success {
    color: #49c96d!important;
}
.text-danger {
    color: #dc3545!important;
}
.text-orange {
    color: #e56e19 !important;
}
.btn-danger {
    color: #fff !important;
    background-color: #dc3545 !important;
    border: 1px solid #dc3545 !important;
}
.btn-success {
    color: #fff !important;
    background-color: #49c96d !important;
    border: 1px solid #49c96d !important;
}
.btn-success::placeholder,
.btn-danger::placeholder {
  color: #fff;
}
/*
.fs-14 {
    font-size: 14px;
}*/


.fs-14 {
  font-size: 12px; /* Modify this to fit your design */
  vertical-align: super;
  margin-left: -2px; /* Adjust the margin to eliminate unwanted gaps */
}

.fs-18 {
    font-size: 18px;
}
.cursor-pointer {
    cursor: pointer;
}
.input-text {
    padding: 10px;
    width: 250px;
    height: 50px;
    font-size: 24px;
    color: #7a7982;
    border: none;
    outline: none;
    border-radius: 8px;
    text-align: center;
    background-color: #000;
    border: 1px solid #7a7983;
}


@media screen and (min-device-width: 1400px) and (max-device-width: 1800px) {  
    .deg-input-btn {
        font-size: 20px;
        width: 142px;
    }
    .deg-input-btn .w-28 {
        width: 24px;
    }
}
/* TKR Screen 3 Input Button css End*/

.tibia-img {
    height: 280px;
}
.distal-femur-cut-img {
    height: 260px !important;
}


.distal-femur-cut-img_large {
    height: 286px !important;
    width: 508px;
}


.distal-femur-cut-img_robot {
    height: 429px !important;  /* 286px * 1.5 = 429px */
    width: 762px;              /* 508px * 1.5 = 762px */
}

/* .Bone-text.top-44 {
    top: 44px;
}
.box-border {
    border-radius: 16px;
    border: 2px solid #2F2B41;
} */
.tkr-Bone-img {
    height: 350px;
}
@media screen and (min-device-width: 1400px) and (max-device-width: 1800px) {  
    .tkr-Bone-img {
        height: 300px;
    }
}

.bone_cut .main_block .grid .hc-image {
    height: 350px;
}

.conflict-message-box {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    height: 400px;
    padding: 30px !important;
}
.btn-message-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.btn-msg {
    font-size: 22px;
    padding: 6px;
    width: 170px;
    border-radius: 6px;
    background: linear-gradient(#585C7B -70%, #1B173137);
    border: 1px solid #585C7B;
}
/* revision-knee CSS Start */
.new-grid-box {
    display: flex;
    justify-content: space-evenly;
    border: 2px solid #2F2B41;
    padding: 10px;
    border-radius: 16px;
    margin-bottom: 20px;
    height: 500px;
}
.grid-box {
    height: 460px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
}
.new-grid-box img {
    height: 250px;
}
/* .grid-box-center-img img {
    height: 300px;
} */
.grid-box-center-img {
    height: 380px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    border: 2px solid #2F2B41;
    border-radius: 16px;
}
.grid-box-center-img form {
    min-height: 106px;
    padding: 20px 10px;
    border-bottom: 1px solid #2F2B41;
    display: flex;
    justify-content: space-around;
}
.new-grid-box .txt {
    position: absolute;
    bottom: 0;
}
.new-grid-box .txt-top {
    bottom: inherit;
    top: 0;
}
.new-grid-box .txt.left {
    left: 0;
}
.new-grid-box .txt.right {
    right: 0;
}
.new-grid-box .checkbox {
    width: 28px;
    height: 28px;
}
.new-grid-box .label {
    font-size: 18px;
    min-height: 66px
}
.dashed-border {
    border-bottom: 2px dashed #fff;
}
.solid-border {
    border-bottom: 1px solid #fff;
}
.checked-color {
    border-color: #92C9E1;
    background-color: #92C9E1;
}
/* revision-knee CSS End */
.deg-custom {
    position: absolute;
    top: 34%;
    left: 51%;
    color: #7a7982;
}


#textbox9 {
    font-size: 24px;       /* Adjust the size to fit your design */
    display: inline-block; /* Ensure inline behavior */
}


.bottom_btn_angle {
    position: fixed; /* Fix it to the viewport */
    bottom: 20px; /* Distance from the bottom */
    right: 20px; /* Distance from the right */
    display: flex; /* Arrange buttons horizontally */
    gap: 10px; /* Space between buttons */
    align-items: center; /* Align items vertically */
    z-index: 100; /* Ensure it appears above other content */
}