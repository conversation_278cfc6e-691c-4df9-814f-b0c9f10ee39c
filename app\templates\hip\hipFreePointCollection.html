<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Model Viewer with WebSocket & Ring Points</title>


    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="/static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>

    <style>
        body { margin: 0; overflow: hidden; }
        #canvas-container {
            background-color: black;
            width: 100vw;
            height: 100vh;
            display: block;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128/examples/js/loaders/OBJLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128/examples/js/controls/OrbitControls.js"></script>
</head>
<body>
    <div id="canvas-container"></div>




    <div class="head_wrap">
    <div class="header">
        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
         <div class="second_head head_bg">
            <div class="inner_head one">
                <span><img src="../static/images/calendar.png" /></span><span id="date">24/11/2022</span>
            </div>
            <div class="inner_head">
                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
            </div>
        </div>
        <div class="third_head head_bg">
            <span><img src="../static/images/settings.png" /></span>Settings
        </div>
    </div>
    </div>

    <script>
        let scene, camera, renderer, controls, object3D, pivot, modelCenter;
        let socket = new WebSocket("ws://localhost:8000/ws"); // Connect to WebSocket server

        socket.addEventListener("open", () => {
        const currentUrl = window.location.href;
        let pageName = currentUrl.split('/').pop().split('?')[0]; // Default to "index" if empty
        socket.send(JSON.stringify({ file: pageName }));
        console.log("Sent file name:", pageName);
    });

        function init() {
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 50, 150);

            renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.getElementById("canvas-container").appendChild(renderer.domElement);

            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;

            const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
            scene.add(ambientLight);
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.7);
            directionalLight.position.set(5, 5, 5);
            scene.add(directionalLight);

            const loader = new THREE.OBJLoader();
            loader.load('/static/images/hip-bone/3D/3Dhip/pelvis.obj', function(obj) {
                const box = new THREE.Box3().setFromObject(obj);
                modelCenter = box.getCenter(new THREE.Vector3());

                pivot = new THREE.Group();
                scene.add(pivot);

                obj.position.sub(modelCenter);
                pivot.add(obj);

                obj.traverse(child => {
                    if (child.isMesh) {
                        child.material = new THREE.MeshStandardMaterial({ color: 0xf5deb3, side: THREE.DoubleSide });
                    }
                });

                object3D = pivot;
            });

            animate();
        }

        function addPoint(position, color) {
            const geometry = new THREE.SphereGeometry(0.5, 32, 32);
            const material = new THREE.MeshBasicMaterial({ color: color });
            const point = new THREE.Mesh(geometry, material);
            point.position.copy(position);
            pivot.add(point);
        }

        // WebSocket message handler - listens for new points
        socket.onmessage = function (event) {
    try {
        console.log("🔹 Raw WebSocket Data Received:", event.data); // Log raw data

        if (!event.data || event.data.trim() === "") {
            console.warn("⚠️ Received empty message, ignoring.");
            return;
        }

        let data;
        let trimmedData = event.data.trim();

        // Handle "exit" command correctly
        if (trimmedData.toLowerCase().startsWith("exit")) {
            console.log("🚪 Received exit command. Redirecting...");
            window.location.href = "handle-position.html"; // Change to your target URL
            return;
        }

        // Ignore null messages
        if (trimmedData.toLowerCase() === "null") {
            console.warn("⚠️ Received 'null' message, ignoring.");
            return;
        }

        // Handle raw comma-separated numbers (e.g., "197.33,115.74,-208.20")
        if (/^-?\d+(\.\d+)?,-?\d+(\.\d+)?,-?\d+(\.\d+)?$/.test(trimmedData)) {
            data = trimmedData.split(",").map(Number); // Convert to an array of numbers
        } else {
            // Try parsing JSON safely
            try {
                data = JSON.parse(trimmedData);
            } catch (jsonError) {
                console.warn("⚠️ Invalid JSON format, ignoring:", trimmedData);
                return;
            }
        }

        console.log("📩 Parsed Data:", data); // Log parsed data

        // Handle array format [x, y, z]
        if (Array.isArray(data) && data.length === 3) {
            let [x, y, z] = data;
            console.log(`📍 Received 3D Point: X=${x}, Y=${y}, Z=${z}`);
            let color = 0xff0000; // Default red color
            addPoint(new THREE.Vector3(x - modelCenter.x, y - modelCenter.y, z - modelCenter.z), color);
            return;
        }

        // Ensure data is an object before accessing properties
        if (typeof data === "object" && "x" in data && "y" in data && "z" in data) {
            console.log(`📍 Received Point Object: X=${data.x}, Y=${data.y}, Z=${data.z}, Color=${data.color || "#ff0000"}`);
            let colorHex = data.color ? (data.color.startsWith("#") ? data.color : `#${data.color}`) : "#ff0000";
            let color = parseInt(colorHex.replace(/^#/, ''), 16);
            addPoint(new THREE.Vector3(data.x - modelCenter.x, data.y - modelCenter.y, data.z - modelCenter.z), color);
            return;
        }

        console.warn("⚠️ Unrecognized data format:", data);
    } catch (error) {
        console.error("❌ Error processing WebSocket message:", error);
    }
};

        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }

        window.addEventListener("resize", () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        init();
    </script>

    <div class="footer_wrap">
        <div class="footer">
            <ul>
                <li class="copy_right">
                    © <span id="year">2023</span> Artikon AGS
                    <span class="top_txt">Auto Guided Surgery</span>
                </li>
                <li class="footer_btn_one">
                    <a href="#">
                        <!-- <span><img src="../static/images/camera-video.png" /></span>Cemera -->
                        <div class="btn-group" role="group" aria-label="Basic example">
                            <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                            <button type="button" class="btn second">F</button>
                            <button type="button" class="btn third">T</button>
                        </div>
                    </a>
                </li>

                <div class="bottom_btn">

                    <div class="blank"></div>
                    <div class="btn">
                        <a id="backBtn" href="hipRingPointCollection.html"><span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back</a>
                    </div>
                    <div class="btn">
                        <a id="nextBtn" href="handle-position.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
                    </div>
                </div>


                <li class="footer_btn_two">
				   <a id='home'>
					<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
					</a>
					<script>
							document.getElementById('home').addEventListener('click', function(event) {
								event.preventDefault();  // Prevent default anchor behavior
								window.location.href = '../landing-page.html';  // Redirect to root path
							});
					</script>
				</li>
			   <li class="footer_btn_three">
					<a id="powerbuttonLink">
						<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
					</a>
					<script>
						document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
							event.preventDefault();  // Prevent default anchor behavior
							window.location.href = '/';  // Redirect to root path
						});
					</script>
				</li>
            </ul>
        </div>
    </div>




<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>
</html>
