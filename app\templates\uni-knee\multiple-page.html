<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
    	<link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut TC ">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Tibia</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/uni-knee/TC.jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                          <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                <ul>
                                   <li>Place Pointer at the Tibia Center </li>
									<li>To Record, Press Button</li>
                                </ul>
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut AL1 d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Tibia</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/uni-knee/TC_AL1.jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
						
						<div class="note">
							<p class="note_txt">Notes:</p>
							<p>
								<ul>
								   <li>Place Pointer at the Akagi's Line Anterior Point </li>
									<li>To Record, Press Button</li>
								</ul>
							</p>
                        </div>
						
                    </div>
					<div class="middle_section bone_cut AL2 d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Tibia</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/uni-knee/TC_AL2.jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
						<div class="note">
							<p class="note_txt">Notes:</p>
							<p>
								<ul>
								   <li>Place Pointer at the Akagi's Line Posterior Point </li>
									<li>To Record, Press Button</li>
								</ul>
							</p>
                        </div>
						
                    </div>
                    <div class="middle_section bone_cut MC d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Tibia</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/uni-knee/MC.jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
						
						<div class="note">
							<p class="note_txt">Notes:</p>
							<p>
								<ul>
								   <li>Place Pointer at the medial compartment </li>
									<li>To Record, Press Button</li>
								</ul>
							</p>
                        </div>
						
                    </div>
                    <div class="middle_section bone_cut MP d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Tibia</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/uni-knee/TC_MP.jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
						<div class="note">
							<p class="note_txt">Notes:</p>
							<p>
								<ul>
									<li>Place Pointer at the Most Medial Point </li>
									<li>To Record, Press Button</li>
								</ul>
							</p>
						</div>
                    </div>
                    <div class="middle_section bone_cut MM d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Tibia</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/knee-bone/tibia-points/5-medial-malleolus(mm).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                         <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                Place Pointer onto the most prominent<br />
                                aspect of medial malleolus.<br />
                                To record point and Press button.
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut LM d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Tibia</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/knee-bone/tibia-points/6-lateral-malleolus(lm).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                Place Pointer onto the most prominent<br />
                                aspect of lateral malleolus.<br />
                                To record point and Press button.
                            </p>
                        </div>
                    </div>
                    <div class="check_box">
                        <div class="new">
                            <form>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question1" class="section-toggle TC" data-section="TC" />
                                    <label for="coupon_question1">Tibia Center</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question2" class="section-toggle AL1" data-section="AL1" />
                                    <label for="coupon_question2">Akagi's Line Anterior Point</label>
                                </div>
								<div class="form-group">
                                    <input type="checkbox" id="coupon_question2" class="section-toggle AL2" data-section="AL2" />
                                    <label for="coupon_question2">Akagi's Line Posterior Point</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question3" class="section-toggle MC" data-section="MC" />
                                    <label for="coupon_question3">Medial Compartment</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question4" class="section-toggle MP" data-section="MP" />
                                    <label for="coupon_question4">Most Medial Point</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question5" class="section-toggle MM" data-section="MM" />
                                    <label for="coupon_question5">Medial Malleolus</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question6" class="section-toggle LM" data-section="LM" />
                                    <label for="coupon_question6">Lateral Malleolus</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question6" class="section-toggle TOK" data-section="TOK" />
                                    <label for="coupon_question6">Collect Tibia Free Points</label>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
				
				
				<div class="bottom_btn">
					<div class="btn green_border pointer_bg">
						<span class="mr-20"><img src="../static/images/icon/check.png" /></span>Pointer
					</div>
					<div class="blank"></div>
					<div class="btn">
						<a id="backBtn" href="multiple-page.html"><span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back</a>
					</div>
					<div class="btn">
						<a id="nextBtn" href="Free_point_collectionTibia.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
					</div>
				</div>
								
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../static/images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#" id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
                            </li>
                            <li class="footer_btn_three">
							<a href="#" id="powerbuttonLink">
								<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
							</a>
							<script>
								document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
									event.preventDefault();  // Prevent default anchor behavior
									window.location.href = '/';  // Redirect to root path
								});
							</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>

<!-- Scripting code starts -->

<script>
    
    let socket;

    function startServer() {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
            const currentUrl = window.location.href;
                const pageName = "uni" + currentUrl.substring(currentUrl.lastIndexOf('/') + 1).split('?')[0];
            socket = new WebSocket('ws://127.0.0.1:8000/ws');

            socket.onopen = function(event) {
                console.log('Socket opened for ');

                socket.send(JSON.stringify({ file: pageName }));
            };
			//const dataToSend = 'multiple_page'; // For example, sending screen ID or some command
            //socket.send(dataToSend);
            socket.onmessage = function(event) {
                console.log('Received message:', event.data);
                const values = event.data.split(',');
                const abbreviation = values[0];

                // Get the current URL
                var currentUrl = window.location.href;

                // Remove the page name from the URL
                var projectUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);
                
                if (abbreviation == 'LM') {
					setTimeout(function() {
					window.location.href = projectUrl + 'Free_point_collectionTibia.html';
				}, 2000); // 2000 milliseconds = 2 seconds
                }

                // Hide all sections
                $('.middle_section').addClass('d-none');
                $('.' + values[0]).next().removeClass('d-none');
                $('.section-toggle.' + values[0]).attr('checked', true);
            };

            socket.onerror = function(error) {
                console.error('Socket error:', error);
            };
        }
    };
	
	function handleNavigation(event) {
		event.preventDefault();
		const targetUrl = event.currentTarget.href;

		if (socket && socket.readyState === WebSocket.OPEN) {
			socket.send(JSON.stringify({ file: " " }));
			socket.addEventListener('close', function () {
				window.location.href = targetUrl;
			}, { once: true });

			socket.close();
		} else {
			window.location.href = targetUrl;
		}
	};
	document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
    document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);
	
	// Camera functionality
    document.addEventListener('DOMContentLoaded', function() {
        const cameraButton = document.querySelector('.footer_btn_one .btn.first');
        const cameraModal = document.getElementById('cameraModal');
        const closeCamera = document.querySelector('.close-camera');
        const videoStream = document.getElementById('videoStream');
        let mediaStream = null;

        // Function to start the camera
        async function startCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });
                mediaStream = stream;
                videoStream.srcObject = stream;
                videoStream.play();
            } catch (err) {
                console.error('Error accessing camera:', err);
            }
        }

        // Function to stop the camera
        function stopCamera() {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                videoStream.srcObject = null;
                mediaStream = null;
            }
        }

        // Open camera modal
        cameraButton.addEventListener('click', function(e) {
            e.preventDefault();
            cameraModal.style.display = 'block';
            startCamera();
        });

        // Close camera modal
        closeCamera.addEventListener('click', function() {
            cameraModal.style.display = 'none';
            stopCamera();
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === cameraModal) {
                cameraModal.style.display = 'none';
                stopCamera();
            }
        });
    });
	
    setInterval(function() {
        startServer();
    }, 100); // Change the interval duration as needed

</script>
</html>

