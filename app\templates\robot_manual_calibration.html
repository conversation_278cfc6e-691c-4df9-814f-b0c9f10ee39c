<!DOCTYPE html>
<html>
<head>
    <title>Manual Robot Calibration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h2 {
            color: #333;
            text-align: center;
        }
        #status {
            background-color: #f5f5f5;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            margin: 20px 0;
            text-align: center;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <h2>Manual Robot Calibration (4 Points)</h2>
    <p>Move the robot manually to 4 different positions. Hold steady for 2 seconds at each position to record a point.</p>
    <div id="status">Connecting...</div>
    <script>
        let ws = new WebSocket("ws://" + location.host + "/ws");
        ws.onopen = function() {
            document.getElementById("status").innerText = "Connected. Starting calibration...";
            ws.send(JSON.stringify({file: "robot_manual_calibration"}));
        };
        ws.onmessage = function(event) {
            let data = JSON.parse(event.data);
            if (data.status === "point_captured") {
                document.getElementById("status").innerText = "Captured point " + data.count + " of 4. Move to next position.";
            } else if (data.status === "done") {
                document.getElementById("status").innerText = "Calibration complete! File: " + data.file;
            } else if (data.status === "error") {
                document.getElementById("status").innerText = "Error: " + data.message;
            }
        };
        ws.onerror = function(event) {
            document.getElementById("status").innerText = "WebSocket error. Please refresh the page.";
        };
        ws.onclose = function(event) {
            document.getElementById("status").innerText = "Connection closed. Please refresh the page.";
        };
    </script>
</body>
</html>

