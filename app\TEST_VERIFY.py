import numpy as np

# Paste your MDC_robot data points here
mdc_data = np.array(
 [363.33137991, 389.38712295, 175.99142255],
    [363.92902988, 388.66415588, 175.81844361],
    [364.14338053, 389.5394795, 175.59251303],
    [364.38622474, 389.39206023, 175.48519877],
    [363.75283098, 389.07511322, 175.13854605],
    [364.22897516, 388.56880761, 175.37404252],
    [364.27461876, 389.3086851, 175.66499801],
    [364.75125328, 389.7762758, 174.82293362],
    [363.99889898, 387.80308097, 175.15492416],
    [363.76845833, 388.23165873, 175.3212777],
    [363.67333598, 387.97603462, 175.54130929],
    [363.89774377, 388.09060946, 175.24802839],
    [364.46007511, 388.42633908, 175.47844254],
    [364.29122095, 388.38059912, 175.16226433],
    [364.53774693, 388.90922314, 175.39397053],
    [363.51367728, 387.49717633, 174.69884753],
    [364.14389091, 388.17755492, 175.41150494],
    [364.54762818, 388.67202614, 175.27961698],
    [363.22394311, 387.86173219, 175.41567449],
    [363.30173763, 387.46018234, 175.17031948],
    [363.98831417, 388.54819378, 175.00455531]
)

# Mean point (centroid)
mean_point = np.mean(mdc_data, axis=0)

# MAE, RMSE, Standard Deviation
mae = np.mean(np.abs(mdc_data - mean_point), axis=0)
rmse = np.sqrt(np.mean((mdc_data - mean_point) ** 2, axis=0))
std_dev = np.std(mdc_data, axis=0)

# Euclidean distance errors from mean point
euclidean_errors = np.linalg.norm(mdc_data - mean_point, axis=1)
mean_euclidean_error = np.mean(euclidean_errors)
max_euclidean_error = np.max(euclidean_errors)
combined_rmse = np.sqrt(np.mean(np.sum((mdc_data - mean_point) ** 2, axis=1)))

# Print results
print("📍 Mean Point (Centroid):")
print(f"X = {mean_point[0]:.4f}, Y = {mean_point[1]:.4f}, Z = {mean_point[2]:.4f}\n")

print("📊 Axis-wise Error Metrics:")
print(f"MAE :  X = {mae[0]:.4f}, Y = {mae[1]:.4f}, Z = {mae[2]:.4f}")
print(f"RMSE:  X = {rmse[0]:.4f}, Y = {rmse[1]:.4f}, Z = {rmse[2]:.4f}")
print(f"STD :  X = {std_dev[0]:.4f}, Y = {std_dev[1]:.4f}, Z = {std_dev[2]:.4f}\n")

print("📏 Euclidean Distance Errors:")
print(f"Mean Euclidean Error   : {mean_euclidean_error:.4f}")
print(f"Max Euclidean Error    : {max_euclidean_error:.4f}")
print(f"Combined RMSE (3D)     : {combined_rmse:.4f}")
