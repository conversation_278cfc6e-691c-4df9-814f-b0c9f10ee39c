<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Artikon</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">07/01/2025</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul>
                                <li>Edit and confirm surgical plan before processing</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl tkr-screen-3-section">
                            <div class="tbl-cell">
                                <div class="row text-center">
                                    <div class="col-8">
                                       <div class="main-Bone-box">
                                            <div class="row">
                                                <div class="col-4">
                                                    <!-- <div class="position-relative">
                                                         <div class="rotate-btn">
                                                            <i class="fa-solid fa-rotate-left"></i>
                                                        </div>
                                                    </div> -->
                                                    <div class="Bone-box-style pb-80">
                                                        <div class="Bone-text">
                                                            <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                <a class="minus-button cursor-pointer">
                                                                    <img src="../static/images/icon/minus-button.png" class="w-28" alt="">
                                                                </a>
                                                                <span class="text-success" id="femure_input2">0.0</span>
                                                                <sup class="text-success fs-14">0</sup>
                                                                <span class="input-btn-text text-white">var</span>
                                                                <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </div>
                                                        </div>
                                                        <img src="../static/images/femur/femur-cut-planning-2.png" class="img-fluid bone-img" alt="">
                                                        <!-- <img src="../static/images/bone/Bone-img-1.png" class="img-fluid" alt=""> -->
                                                        <div class="Bone-text bottom-input">
                                                            <div class="d-flex align-items-center">
                                                                <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                    <a class="minus-button cursor-pointer"><img src="../static/images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                    <span class="text-success" id="femure_input1">0.0</span>

                                                                    <span class="input-btn-text te  xt-white">mm</span>
                                                                    <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>
                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="Bone-box-style pb-80 position-relative">
                                                        <div class="Bone-text ">
                                                            <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                <a class="minus-button cursor-pointer"><img src="../static/images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                <span class="text-danger" id="femure_input3">0.0</span>
                                                                <sup class="text-danger fs-14">0</sup>
                                                                <span class="input-btn-text text-white">ext</span>
                                                                <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </div>
                                                        </div>
                                                        <img src="../static/images/femur/femur-cut-planning-1.png" class="img-fluid bone-img" alt="">
                                                        <div class="Bone-text bottom-input ">
															  <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                <a class="minus-button cursor-pointer"><img src="../static/images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                <span class="text-success" id="femure_input4">0.0</span>
                                                                <span class="input-btn-text text-white">mm</span>
                                                                <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="Bone-box-style pb-80">
                                                        <div class="Bone-text">
                                                            <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                <a class="minus-button cursor-pointer"><img src="../static/images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                <span class="text-danger" id="femure_input5">0.0</span>
                                                                <sup class="text-danger fs-14">0</sup>
                                                                <span class="input-btn-text text-white">Flex</span>
                                                                <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </div>
                                                        </div>
                                                        <img src="../static/images/femur/femur-cut-planning-3.png" class="img-fluid bone-img" alt="">
                                                        <div class="Bone-text bottom-input">
                                                            <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                <a class="minus-button cursor-pointer"><img src="../static/images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                <span class="text-success" id="femure_input6">0.0</span>
                                                                <sup class="fs-14">0</sup>
                                                                <span class="input-btn-text text-white">mm</span>
                                                                <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="hka-border">HKA 0.0&deg var</div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="position-relative Bone-box-style">
                                                        <img src="../static/images/femur/tibia-cut-planning-1.jpg" class="img-fluid bone-img" alt="">
                                                        <div class="Bone-text bottom-input single-bottom-input">
                                                            <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                <a class="minus-button cursor-pointer"><img src="../static/images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                <span class="text-success" id="tibia_input1">0.0</span>
                                                                <sup class="fs-14">0</sup>
                                                                <span class="input-btn-text text-white">var</span>
                                                                <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="position-relative Bone-box-style">
                                                        <img src="../static/images/femur/tibia-cut-planning-4.jpg" class="img-fluid bone-img" alt="">
                                                        <div class="Bone-text bottom-input single-bottom-input">
                                                            <div class="d-flex align-items-center">
																<div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                <a class="minus-button cursor-pointer"><img src="../static/images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                <span class="text-success" id="tibia_input2">0.0</span>
                                                                <span class="input-btn-text text-white">mm</span>
                                                                <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="position-relative Bone-box-style">
                                                        <img src="../static/images/femur/tibia-cut-planning-3.jpg" class="img-fluid bone-img" alt="">
                                                        <div class="Bone-text bottom-input single-bottom-input">
                                                            <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                <a class="minus-button cursor-pointer"><img src="../static/images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                <span class="text-success" id="tibia_input3">0.0</span>
                                                                <sup fs-14">0</sup>
                                                                <span class="input-btn-text text-white">mm</span>
                                                                <a class="plus-button cursor-pointer"><img src="../static/images/icon/plus-button.png" class="w-28" alt=""></a>

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--div class="col-4">
                                        <div class="position-relative">
                                            <div class="Bone-text graph-text graph-top-left-input">
                                                <div class="input-group graph-input flex-nowrap">
                                                    <input type="text" placeholder="8.5" aria-label="" aria-describedby="" id="graphbox1">
                                                    <span class="input-group-text">mm</span>
                                                </div>
                                            </div>
                                            <div class="Bone-text graph-text graph-top-right-input">
                                                <div class="input-group graph-input flex-nowrap">
                                                    <input type="text" placeholder="9.0" aria-label="" aria-describedby="" id="graphbox2">
                                                    <span class="input-group-text">mm</span>
                                                </div>
                                            </div>
                                            <div class="Bone-text graph-text graph-bottom-left-input">
                                                <div class="input-group graph-input flex-nowrap">
                                                    <input type="text" placeholder="8.5" aria-label="" aria-describedby="" id="graphbox3">
                                                    <span class="input-group-text">mm</span>
                                                </div>
                                            </div>
                                            <div class="Bone-text graph-text graph-bottom-right-input">
                                                <div class="input-group graph-input flex-nowrap">
                                                    <input type="text" placeholder="9.0" aria-label="" aria-describedby="" id="graphbox4">
                                                    <span class="input-group-text">mm</span>
                                                </div>
                                            </div>
                                            <img src="../static/images/bone/Bone-image-13.png" class="img-fluid graph-img" alt="">
                                        </div>
                                    </div-->
                                </div>
                                <div class="bottom_btn">
                                    <div class="blank blank-none"></div>
                                    <div class="btn">
                                        <a id="backBtn" href="planning-distal-femur-cut.html">
                                            <span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back
                                        </a>
                                    </div>
                                    <div class="btn">
                                        <a id="nextBtn" href="Robot_intoduction.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul class="align-items-center">
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li>
                                <ul class="bottom-icon">
                                    <li><img src="../static/images/icon/icon-1.png" class="img-fluid icon-img" alt=""></li>
                                    <li><img src="../static/images/icon/icon-2.png" class="img-fluid icon-img active" alt=""></li>
                                    <li><img src="../static/images/icon/icon-3.png" class="img-fluid icon-img" alt=""></li>
                                    <li><img src="../static/images/icon/icon-4.png" class="img-fluid icon-img" alt=""></li>
                                </ul>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../static/images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                               <a href="#" id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
                            </li>
                           <li class="footer_btn_three">
								<a href="#" id="powerbuttonLink">
									<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
								</a>
								<script>
									document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
										event.preventDefault();  // Prevent default anchor behavior
										window.location.href = '/';  // Redirect to root path
									});
								</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>
<script>
    let socket;

    const inputRanges = {
        'femure_input1': { min: 5.0, max: 13.0, default: 9.0 },
        'femure_input2': { min: -3.0, max: 3.0, default: 0.0 },
        'femure_input3': { min: -3.0, max: 3.0, default: 3.0 },
        'femure_input4': { min: 0.0, max: 5.0, default: 0.0 },
        'femure_input5': { min: -1.0, max: 3.0, default: 0.0 },
        'femure_input6': { min: -1.0, max: 2.0, default: 0.0 },
        'tibia_input1': { min: -3.0, max: 3.0, default: 0.0 },
        'tibia_input2': { min: 5.0, max: 13.0, default: 0.0 },
        'tibia_input3': { min: 0.0, max: 13.0, default: 3.0 }
    };

    function handleNavigation(event) {
        event.preventDefault();
        const targetUrl = event.currentTarget.href;

        if (socket && socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({ file: " " }));
            socket.onclose = () => window.location.href = targetUrl;
            socket.close();
        } else {
            window.location.href = targetUrl;
        }
    }

    function startServer() {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            const currentUrl = window.location.href;
            const pageName = 'rev' + currentUrl.substring(currentUrl.lastIndexOf('/') + 1).split('?')[0];
            socket = new WebSocket('ws://127.0.0.1:8000/ws');

            socket.onopen = () => {
                console.log('WebSocket connected');
                socket.send(JSON.stringify({ file: pageName }));

                setDefaultValues();
            };


            socket.onerror = error => console.error('WebSocket error:', error);
        }
    }

    function setDefaultValues() {
        Object.keys(inputRanges).forEach(inputId => {
            const element = document.getElementById(inputId);
            if (element) {
                const defaultValue = inputRanges[inputId].default.toFixed(1);
                element.textContent = defaultValue;
                sendData(inputId, defaultValue);
            }
        });
    }

    function sendData(inputId, inputValue) {
        if (socket && socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({ inputId, inputValue }));
            console.log(`Sent via WebSocket: ${inputId} = ${inputValue}`);
        } else {
            console.error("WebSocket not connected!");
        }
    }

    function handleButtonClick(change) {
        return function () {
            const span = $(this).closest('.input-btn').find('span').first();
            const inputId = span.attr('id');
            if (!inputId || !inputRanges[inputId]) return;

            const text = span.text();
            const currentValue = parseFloat(text);
            if (!isNaN(currentValue)) {
                let newValue = currentValue + change;
                const range = inputRanges[inputId];

                newValue = Math.max(range.min, Math.min(range.max, newValue));

                if (newValue !== currentValue) {
                    span.text(newValue.toFixed(1));
                    sendData(inputId, newValue.toFixed(1));
                }
            }
        };
    }

    document.addEventListener('DOMContentLoaded', function () {
        startServer();


        // Navigation buttons
        document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
        document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);

        // Button click handlers
        $('.minus-button').on('click', handleButtonClick(-0.5));
        $('.plus-button').on('click', handleButtonClick(0.5));

        // Camera modal functionality
        const cameraButton = document.querySelector('.footer_btn_one .btn.first');
        const cameraModal = document.getElementById('cameraModal');
        const closeCamera = document.querySelector('.close-camera');
        const videoStream = document.getElementById('videoStream');
        let mediaStream = null;

        async function startCamera() {
            try {
                mediaStream = await navigator.mediaDevices.getUserMedia({
                    video: { width: { ideal: 1280 }, height: { ideal: 720 } }
                });
                videoStream.srcObject = mediaStream;
                videoStream.play();
            } catch (err) {
                console.error('Error accessing camera:', err);
            }
        }

        function stopCamera() {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                videoStream.srcObject = null;
                mediaStream = null;
            }
        }

        cameraButton?.addEventListener('click', function (e) {
            e.preventDefault();
            cameraModal.style.display = 'block';
            startCamera();
        });

        closeCamera?.addEventListener('click', function () {
            cameraModal.style.display = 'none';
            stopCamera();
        });

        window.addEventListener('click', function (event) {
            if (event.target === cameraModal) {
                cameraModal.style.display = 'none';
                stopCamera();
            }
        });
    });
</script>




</html>