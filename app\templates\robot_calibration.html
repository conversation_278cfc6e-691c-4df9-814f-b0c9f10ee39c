<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon - Robot Calibration</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="/static/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="/static/css/style.css">
    <style>
        .status-box {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ccc;
            min-height: 100px;
            background-color: #f9f9f9;
        }
        .connected { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="/index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="/static/images/calendar.png" /></span><span id="date"></span>
                            </div>
                            <div class="inner_head">
                                <span><img src="/static/images/time.png" /></span><span id="time"></span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="/static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                
                <div class="middle_section_wrap">
                    <div class="container">
                        <div class="row">
                            <div class="col-md-12 text-center">
                                <h1>Robot Calibration</h1>
                                <p>Click the button below to start the robot calibration process.</p>
                                <button id="startCalibration" class="btn btn-primary btn-lg">Start Calibration</button>
                                <div id="status" class="status-box">Ready to start calibration</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Update date and time
        function updateDateTime() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('en-GB');
            const timeStr = now.toLocaleTimeString('en-GB');
            document.getElementById('date').textContent = dateStr;
            document.getElementById('time').textContent = timeStr;
        }
        
        updateDateTime();
        setInterval(updateDateTime, 1000);
        
        // WebSocket connection
        let socket;

        document.getElementById('startCalibration').addEventListener('click', function() {
            connectWebSocket();
        });

        function connectWebSocket() {
            document.getElementById('status').textContent = 'Connecting...';
            document.getElementById('status').className = 'status-box';
            
            socket = new WebSocket('ws://127.0.0.1:8000/ws');
            
            socket.onopen = function(e) {
                document.getElementById('status').textContent = 'Connected';
                document.getElementById('status').className = 'status-box connected';
                socket.send(JSON.stringify({"file": "robot_calibration"}));
                document.getElementById('status').textContent = 'Calibration started. Please wait while the robot moves through calibration points...';
            };
            
            socket.onmessage = function(event) {
                document.getElementById('status').textContent = 'Received: ' + event.data;
                console.log("Message from server:", event.data);
            };
        }
    </script>
</body>
</html>

