.camera-modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 50%;
    top: 50%;
    width: 600px; /* Reduced for a more professional look */
    height: 450px;
    transform: translate(-50%, -50%);
    background-color: rgba(15, 15, 15, 0.97);
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.6);
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    border: 2px solid #2F2B41; /* Added border with your color */
}

.camera-modal.show {
    display: block;
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.camera-modal-content {
    width: 100%;
    height: 100%;
    position: relative;
}

.close-camera {
    position: absolute;
    right: 12px;
    top: 12px;
    color: #fff;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease, transform 0.2s ease;
}

.close-camera:hover {
    color: #ff5555;
    transform: scale(1.2);
}

/* Updated Text Styling */
.camera-modal h2 {
    color: #FFFFFF;
    font-size: 16px;
    margin-bottom: 8px;
    font-weight: 500;
}

.camera-modal p {
    color: #2F2B41; /* Your requested color */
    font-size: 13px;
    margin-bottom: 10px;
    font-weight: 500;
}

/* Video Stream */
#videoStream {
    width: 100%;
    height: calc(100% - 40px);
    object-fit: cover;
    border-radius: 8px;
    background: #000;
}

/* Responsive Design */
@media (max-width: 768px) {
    .camera-modal {
        width: 90%;
        height: 400px;
    }
    .camera-modal h2 {
        font-size: 14px;
    }
    .camera-modal p {
        font-size: 12px;
    }
}
