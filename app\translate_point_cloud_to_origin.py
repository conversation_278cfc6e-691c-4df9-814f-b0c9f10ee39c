import os
import numpy as np

current_dir = os.path.dirname(os.path.abspath(__file__))

def load_data(file_path):
    led_points = []
    robot_points = []

    with open(file_path, "r") as file:
        for line in file:
            if "LED" in line and "robot" in line:
                led_part = line.split("LED [")[1].split("]")[0].split()
                robot_part = line.split("robot (")[1].split(")")[0].split(", ")

                # Convert to float
                led_x, led_y, led_z = map(float, led_part)
                robot_x, robot_y, robot_z = map(float, robot_part)

                led_points.append([led_x, led_y, led_z])
                robot_points.append([robot_x, robot_y, robot_z])

    return np.array(led_points), np.array(robot_points)

def translate_point_cloud_to_origin():
    robot_calib_data = os.path.join(
        current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
    )
    led_points, robot_points = load_data(file_path=robot_calib_data)

    Hardcoded_origin = [519.25012223, 477.1978664,  333.06544753]
    print(led_points)
    led_points = led_points- np.array(Hardcoded_origin)
    print(led_points)

    with open('translated_point_cloud_4_7_2025-1000.txt', 'w') as file:
        for point in led_points:
            file.write(
                f'LED {point}\n')
    

# driver code
translate_point_cloud_to_origin()