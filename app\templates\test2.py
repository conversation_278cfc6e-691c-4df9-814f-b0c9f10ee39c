import numpy as np
from scipy.optimize import least_squares

# Assuming your data is in a suitable format (e.g., lists of tuples)

def sphere_residuals(p, x, y, z):
    """
    Calculates the residuals for sphere fitting.

    Args:
        p: Parameters of the sphere (x_c, y_c, z_c, r)
        x, y, z: Coordinates of the points

    Returns:
        Residuals for each point
    """
    x_c, y_c, z_c, r = p
    return np.sqrt((x - x_c)**2 + (y - y_c)**2 + (z - z_c)**2) - r


def normalize_point_cloud(point_cloud):
    """
    Normalize the point cloud by centering it around the origin and scaling.

    Args:
        point_cloud: numpy array of shape (N, 3), where N is the number of points

    Returns:
        normalized_points: normalized point cloud (centered and scaled)
        scaling_factor: the scaling factor used for normalization
    """
    # Calculate the centroid (mean)
    centroid = np.mean(point_cloud, axis=0)

    # Shift points to the origin by subtracting the centroid
    centered_points = point_cloud - centroid

    # Calculate the scaling factor (maximum distance from the origin)
    max_distance = np.max(np.linalg.norm(centered_points, axis=1))

    # Scale the points
    normalized_points = centered_points / max_distance

    return normalized_points, centroid, max_distance

def sphere_fit(point_cloud):
    point_cloud, centroid, max_distance = normalize_point_cloud(point_cloud)
    """
    input
        point_cloud: xyz of the point clouds　numpy array
    output
        radius : radius of the sphere
        sphere_center : xyz of the sphere center
    """

    A_1 = np.zeros((3,3))
    #A_1 : 1st item of A
    v_1 = np.array([0.0,0.0,0.0])
    v_2 = 0.0
    v_3 = np.array([0.0,0.0,0.0])
    # mean of multiplier of point vector of the point_clouds
    # v_1, v_3 : vector, v_2 : scalar

    N = len(point_cloud)
    #N : number of the points

    """Calculation of the sum(sigma)"""
    for v in point_cloud:
        v_1 += v
        v_2 += np.dot(v, v)
        v_3 += np.dot(v, v) * v

        A_1 += np.dot(np.array([v]).T, np.array([v]))

    v_1 /= N
    v_2 /= N
    v_3 /= N
    A = 2 * (A_1 / N - np.dot(np.array([v_1]).T, np.array([v_1])))
    # formula ②
    b = v_3 - v_2 * v_1
    # formula ③
    sphere_center = np.dot(np.linalg.inv(A), b)
    #　formula ①
    radius = (sum(np.linalg.norm(np.array(point_cloud) - sphere_center, axis=1))
              /len(point_cloud))
    sphere_center = sphere_center * max_distance + centroid

    return (radius, sphere_center)
# Prepare data (example)
# centroids = np.array([[519.5540631302565,486.11570980058553, 249.29106372187707],
# [531.6390642219059,487.8084753454878, 250.68979603974915],
# [530.8806144364204,487.912618245764, 250.22325344596797],
# [530.2416072211068,487.9779082428224, 249.90137051670573],
# [529.7187989363023,487.9847920289456, 249.61050210199815],
# [529.3431107061311,487.9402987650448, 249.19432139541027],
# [529.1867622489649,487.833933083736, 248.79961841558327],
# [529.2580296860764,487.66623631471674, 248.43477381585907],
# [529.5775094130239,487.40454781439365, 247.79250464417234],
# [530.0672575171569,487.09629243143354, 247.441324464647],
# [530.794577892885,486.72537405462634, 246.97172055247847],
# [531.7464047229043,486.24274713241704, 246.76713298752858],
# [532.7781041215843,485.72450285281883, 246.60270937004648],
# [533.8563262191177,485.1453684484907, 246.35689745504257],
# [534.9666787187681,484.49774597221176, 246.50622205106632],
# [536.0229011459604,483.83127115419376, 246.57630972615357],
# [537.4104947598056,482.9456010427854, 246.9648912707984],
# [538.0257059424957,482.61598196237395, 247.37795778052816],
# [538.7313739011988,482.3188376801466, 248.10634360185705],
# [539.3082585367514,482.19462813021823, 248.95871961941057],
# [539.3770260424765,482.5010688102961, 249.71690815371133],
# [539.0675699999889,483.06740183388996, 250.0830883584703],
# [538.7728636730379,483.47077251473496, 250.47203107816748],
# [538.0920081517887,484.2222791808343, 250.51301874287546],
# [537.5325320069119,484.78210430749664, 251.17712342485257],
# [536.3911920173153,485.66440685092266, 251.06047489253106],
# [534.8030195533112,486.62763517397434, 251.40131217387673],
# [533.3140521410614,487.2717808675737, 251.07867859624295],
# [531.9867323436132,487.6999492890786, 250.80492803299992],
# [530.8425779373227,487.9559640595796, 250.44476246637635],
# [529.9138447694587,488.05639157909087, 250.14000372618878],
# [529.2029851799322,488.10158378544975, 249.5077153381877],
# [529.0372615966243,487.93092903214074, 249.15568485352483],
# [529.1976967182213,487.69924594003345, 248.45979154927178],
# [529.9700224537453,487.18977435859546, 247.60031278067422],
# [530.7909587731798,486.8270790967604, 247.149991964765],
# [531.9819655291976,486.14493416341594, 246.69489008324317],
# [533.3292840982361,485.27987183091597, 246.2282561998311],
# [534.257016480892,484.69940861595825, 246.13939900620497],
# [535.750450224234,483.7509390756488, 246.29437994902523],
# [536.9779762231427,482.95326603528184, 246.44800426240204],
# [537.9674104108364,482.43587554694176, 246.96068653436285],
# [538.7613335038217,482.170951125542, 247.86824711329425]])# Prepare data (example)



# centroids = np.array([[517.5487802115262,486.9450157066851, 246.39530565666323],
# [535.2589312214867,487.90932965853887, 247.44320072993673],
# [534.1966195522483,488.35361981406203, 247.72509390873407],
# [533.2079713516731,488.69489575328953, 247.12822610476147],
# [531.882160305917,489.00459265066416, 247.4051155099513],
# [530.394123739633,489.22243466428716, 246.59362344615298],
# [529.4218027151085,489.29560325731154, 246.36699522951892],
# [528.5914358556066,489.2540596387021, 245.88549512618124],
# [527.8582521795867,489.12002262999704, 245.58769438273464],
# [527.1389109449274,488.87786792129486, 245.08688525991568],
# [526.4450651009591,488.3702415521652, 244.4475267826592],
# [526.2282278804869,487.61859721502043, 243.37937431008672],
# [526.7253201100676,486.66598854675357, 242.33822142907061],
# [527.7399275354627,485.74385613913864, 241.2649963612241],
# [528.6779171712129,485.10572999765833, 241.15542767266143],
# [530.254328001014,483.84088323918576, 240.59613315474937],
# [531.4395603154838,482.93910010680884, 240.44556875043477],
# [533.0425078186294,481.6561497909304, 240.23315200154732],
# [534.7249427555554,480.37637059240234, 240.30232428729622],
# [536.3836936815792,479.1096928111148, 240.45695960742748],
# [537.7547256692357,478.24754496914215, 240.60673754517913],
# [538.7444784905202,478.09205769080205, 241.4740868336054],
# [539.6380258808636,478.1080768064484, 242.1878665740691],
# [540.2304998227299,478.34587306182857, 242.84525910876312],
# [540.9414512630589,478.80127921122556, 243.6668014371788],
# [541.4493869864328,479.1388274277887, 244.43220066082696],
# [541.8680065856879,479.7733017916409, 245.28565614673232],
# [541.8274352447437,480.70932791525973, 245.9730590796768],
# [541.5240205286449,481.98891626583463, 246.6888238713697],
# [541.1481481624936,483.08158934950325, 247.4400009436947],
# [540.5120638449857,484.1747907179867, 247.91760135873872],
# [539.4152048391326,485.59616065642604, 248.20547002647467],
# [537.9647453286046,486.99314860587316, 248.60884112229726],
# [536.3441195952737,488.1165090305206, 248.81021935194198],
# [535.1310231020266,488.754066778433, 248.77958155583087],
# [534.0038996105304,489.18803020688284, 248.69607775869272],
# [532.2117423163598,489.6556582973805, 248.40203444862092],
# [530.7605589371972,489.91665998830626, 248.33598784100914],
# [529.5990619684832,489.9958272060475, 247.8528124521339],
# [528.8763821276351,489.8823072788004, 247.4315343048572],
# [528.583240686814,489.5880435527437, 246.6803223650746],
# [528.7464310165159,489.21051730950495, 245.95338716678546],
# [529.5875677862815,488.13973168192905, 244.58225992714176]])


#
# centroids = np.array([[519.6327791676399,486.1327938460384, 249.3569228779791],
# [530.3721734498153,487.8989282924958, 249.5082211512735],
# [530.5393182833117,487.6693738701036, 248.97799713039535],
# [531.0977147103672,487.2474470020668, 248.21963692922023],
# [531.9439693141575,486.74876431057527, 247.77561663650582],
# [533.0275842600701,486.1162975198002, 247.2843434090821],
# [534.09606370422,485.5062799766072, 247.20475334781523],
# [535.3011125010067,484.8355595239641, 247.29330647014584],
# [536.4215039004092,484.2152066311794, 247.46821970924086],
# [537.3692138674131,483.69674050844293, 247.95770221805614],
# [538.15453223871,483.2111710437005, 248.41395036109452],
# [538.817708332161,482.83169151099565, 248.85336520171754],
# [539.2455683206541,482.6252383499291, 249.52659859666304],
# [539.7379508197706,482.2713395698579, 250.04978830964092],
# [540.0920723747124,481.99369757208075, 250.4706133442435],
# [540.1903362261997,482.08004564436806, 251.114119204116],
# [539.9113781512957,482.5608454048704, 251.9112899161233],
# [539.1439813323917,483.4978493586827, 252.6454400892826],
# [538.0880376591546,484.56891167529875, 252.99230781828714],
# [536.6928004300929,485.69059130877076, 253.25626167918077],
# [535.3288970364073,486.52705493683084, 253.08232720237336],
# [533.8363601637556,487.24499904508974, 252.57880067314377],
# [532.6583542521479,487.64191899734783, 252.25257337135568],
# [531.9959305894138,487.83023887104923, 251.90686433700637],
# [531.1934342951213,488.00747608541434, 251.210285275843],
# [530.8086608109999,488.03376017819664, 250.70955490639787],
# [530.6462948921668,487.95196840543355, 250.04731508335905],
# [530.8619212433305,487.6860909043053, 249.20397806606903],
# [531.2734810676984,487.3889943373394, 248.7790647120519],
# [532.280452452436,486.7018575443979, 247.89605454295474],
# [533.1073559928985,486.1477675919156, 247.41558402210978],
# [534.3851569246358,485.314854688731, 247.18064185514083],
# [535.8885910581939,484.4124734643644, 247.1433237344155],
# [536.844360973391,483.8315575044166, 247.38676676191565],
# [538.1904048246147,482.92423961962066, 247.56969044733623],
# [538.8713978153347,482.41443300587366, 248.32361024837803],
# [539.6647560797859,481.92774545961964, 249.1158107607874],
# [540.1251037445289,481.70486580415144, 249.82894469418036],
# [540.3672756846952,481.6710407828768, 250.55172386671086],
# [540.3480878061363,481.90104361779714, 251.07617291967185],
# [540.2417399132214,482.14159738352146, 251.3540791789853],
# [539.7738575554723,482.9061453936931, 252.3080097709992],
# [539.2610124052768,483.50389985987755, 252.45744428786807]])# Prepare data (example)




#
#
#
#
# centroids = np.array([[520.4528506310929,486.5792067525144, 249.6673681284096],
# [539.9284199038781,482.61655702841125, 252.1125684194308],
# [539.6675814160061,482.941627242483, 252.57904915606719],
# [539.3443250009421,483.31205186924245, 252.61633985394442],
# [538.9451270559402,483.7145106318929, 252.80105344903583],
# [538.5413021988951,484.1110977161763, 252.6925130936821],
# [538.0281041242455,484.5742790860038, 252.586527375032],
# [537.3475824010587,485.13505449033795, 252.45376535369928],
# [536.5158496222033,485.69850711730845, 252.26047376714823],
# [535.3094803010245,486.3977245395286, 251.85327013940193],
# [534.5357470672044,486.78706200339616, 251.52816377687455],
# [533.3337013184977,487.2771638964255, 251.1363970175842],
# [532.3609527147819,487.58579324191874, 250.6583664883867],
# [531.6528273034812,487.7090647722037, 250.43665706876655],
# [531.1439738340013,487.8051331255992, 250.0800157885325],
# [530.3785227057582,487.9173997469874, 249.90209309527958],
# [529.9598921134144,487.93647409980264, 249.71052860842212],
# [529.7142439371313,487.93640098868127, 249.48418800156097],
# [529.595848994897,487.8299020447302, 248.98893584202483],
# [529.7741867617146,487.64372507550866, 248.57118626127587],
# [530.4060062240334,487.2051464871602, 247.60722976618547],
# [531.0263340036471,486.810247289568, 247.41138595820817],
# [532.4828338685787,485.90865649455014, 246.66660882502356],
# [533.8564994930788,485.0176511252262, 246.28535229713842],
# [535.2232902675247,484.1437598473444, 246.21708290412732],
# [536.1244161870113,483.6168590814074, 246.44070008563745],
# [537.4641326047222,482.8599143174845, 246.8827959514084],
# [538.3052417692478,482.35692184944315, 247.3312424973981],
# [539.1355747421725,481.8355829875263, 247.9478157294834],
# [539.809818620063,481.55934960161386, 248.77342018827287],
# [540.0986080426025,481.42913410610254, 249.26221632214492],
# [540.3994348654622,481.36424139243826, 250.0085281468381],
# [540.3624071161267,481.6117059568638, 250.54766108584172],
# [540.1338008643165,482.0564991817926, 250.90722404384937],
# [539.7262244382276,482.7069342405887, 251.47848787129865],
# [539.3534521474924,483.1719242247584, 251.8597152934133],
# [538.6437091473501,483.9158284736806, 251.88783518507523],
# [537.7751044582784,484.7337810685644, 252.3493965227974],
# [537.080209983978,485.26977726378806, 252.03158118952777],
# [535.8797701309728,486.0805423762604, 251.78666807076323],
# [534.8345412927699,486.64642375995487, 251.5755188359902],
# [533.5638537050964,487.20696317008066, 251.3829704322559],
# [532.7938045587379,487.49046384974537, 251.182105808786]])# Prepare data (example)

centroids = np.array([
    [518.8954432283282, 485.5923583748565, 250.27342528533214],
    [539.0936010236759, 483.4033422622824, 251.44194160976198],
    [538.826010332727, 483.70062260774813, 251.58768359714983],
    [538.4017018944525, 484.1429903766852, 251.78538438934746],
    [537.8024713103446, 484.6920441161344, 251.9316783690559],
    [537.1320196480891, 485.2228156302367, 251.8781638812127],
    [536.4503224828746, 485.68532100959027, 251.70132309302804],
    [535.5914676183337, 486.2110692783918, 251.40465451241968],
    [534.6516531322943, 486.6797852792234, 251.18431581424667],
    [533.5681129900577, 487.1366921443533, 250.71604213686066],
    [532.8511825253378, 487.36250690222823, 250.58387589688013],
    [532.2660639404393, 487.53210254087827, 250.4413272441142],
    [531.9011864675584, 487.61099651229796, 250.16066011543046],
    [531.6492812367492, 487.60065876401177, 249.81092695619887],
    [531.60847800964, 487.45466889047697, 249.33369777426216],
    [531.7623456140967, 487.22162892155956, 248.75881807323708],
    [532.4004326790841, 486.71597957538046, 248.0344078185539],
    [533.1831424013882, 486.2121955599612, 247.62602344509503],
    [534.5317510415936, 485.1177534041951, 246.96109628289426],
    [535.4841487264965, 484.31788744939604, 246.68347045314638],
    [536.8321783121, 483.15672040912904, 246.57874403618464],
    [537.9802673566259, 482.2069680247617, 246.74604479459768],
    [538.4785407445421, 481.9058773704049, 246.93027356404107],
    [539.08241238515, 481.6938497088634, 247.6719800220744],
    [539.4415047496468, 481.59117899663994, 248.194288295267],
    [539.8713591000709, 481.46425972483377, 248.8217054300172],
    [540.1508292383946, 481.4393076844981, 249.52699419616695],
    [540.1411543134705, 481.6543939761791, 249.84499149815636],
    [539.7139750626034, 482.39037865201584, 250.37715800417266],
    [539.281987554216, 483.01603592567017, 250.6417219016714],
    [538.8202333151856, 483.53550564576545, 250.8866634392708],
    [537.774616138856, 484.5318408976823, 250.85867373454678],
    [537.0418662833525, 485.11584975115335, 250.79768599289716],
    [535.8212511282933, 485.919912497646, 250.59934256772647],
    [534.7099525627176, 486.5334088175768, 250.49975479158698],
    [533.2529543681993, 487.14835015087186, 249.86378240652206],
    [532.1131469727829, 487.51311683239174, 250.06395123432785],
    [531.542197787324, 487.6195739868163, 249.81053640344248],
    [531.1859862354423, 487.6322236584999, 249.60757165250917],
    [530.9844762399925, 487.5572235262708, 249.07479031798803],
    [531.1770844303138, 487.19537505203215, 248.14075307538937],
    [532.3609857646309, 486.06222530642793, 246.80277952834084],
    [535.3794673382434, 483.5959767252571, 245.70387064005135]])# Prepare data (example)




center= sphere_fit(centroids)



def calculate_pivot_point(sphere_center, point_on_rod, radius=0):
    """
    Calculate the pivot point by projecting the point on the rod to the surface of the sphere.

    Args:
        sphere_center: The center of the sphere (x_s, y_s, z_s)
        point_on_rod: A point on the rod (x_p, y_p, z_p)

    Returns:
        pivot_point: The projected point on the sphere surface
    """
    # Calculate the radius of the sphere
    # radius = calculate_radius(sphere_center, point_on_rod)

    # Calculate the vector from the sphere center to the point on the rod
    vector = np.array(point_on_rod) - np.array(sphere_center)

    # Normalize the vector
    unit_vector = vector / np.linalg.norm(vector)

    # Project the point onto the sphere surface
    projected_point = np.array(sphere_center) + radius * unit_vector

    return projected_point


# pivot_point = calculate_pivot_point(center[1], point_on_rod=np.array([520.68867495, 476.99107042, 251.54021261]), radius =center[0])
print(f'center {center}')


LE = np.array([551.9156799086714,474.20354248707974,253.17511056806376]) #LE

FC = np.array([552.9451129199415,473.9031014551877,251.0863785566388]) #FC

ME = np.array([552.1747358624618,472.4918932238275,249.73770258631936]) #ME

import math
def adjust_point_to_90_degree_with_midpoint(ME, LE, point):
    # Calculate the midpoint between ME and LE
    midpoint = (np.array(ME) + np.array(LE)) / 2

    # Fix the y-coordinate
    # y_fixed = point[1]

    # Compute vectors
    vector_midpoint_point = np.array(point) - midpoint  # Midpoint -> point
    vector_ME_LE = np.array(LE) - np.array(ME)  # ME -> LE

    # Compute the current angle between the two vectors
    dot_product = np.dot(vector_midpoint_point, vector_ME_LE)
    norm_midpoint_point = np.linalg.norm(vector_midpoint_point)
    norm_ME_LE = np.linalg.norm(vector_ME_LE)
    current_angle = np.arccos(dot_product / (norm_midpoint_point * norm_ME_LE))
    print(f'current_angle {math.degrees(current_angle)}')

    # If already at 90 degrees, no adjustment is needed
    if np.isclose(np.degrees(current_angle), 90):
        return point

    # Calculate the angle difference we need to adjust
    angle_difference = np.degrees(current_angle)
    print(f'Adjusting by {angle_difference} degrees')

    # Rotate vector_midpoint_point in the xz-plane to adjust the angle
    rotation_angle = np.radians(angle_difference)

    # Create a rotation matrix for the xz-plane (around the y-axis)
    rotation_matrix = np.array([[np.cos(rotation_angle), 0, np.sin(rotation_angle)],
                                [0, 1, 0],
                                [-np.sin(rotation_angle), 0, np.cos(rotation_angle)]])

    # Apply the rotation to the vector
    rotated_vector = np.dot(rotation_matrix, vector_midpoint_point)

    # Compute the new point with y fixed
    new_point = midpoint + rotated_vector
    # new_point[1] = y_fixed  # Keep the y-coordinate unchanged

    return new_point
# Adjust the point to ensure a 90° angle
new_point = adjust_point_to_90_degree_with_midpoint(ME, LE, center[1])

midpoint = (np.array(ME) + np.array(LE)) / 2
v1 = new_point - midpoint
v2 = center[1] - midpoint


def calculate_angle(v1, v2):
    # Compute the dot product of the vectors
    dot_product = np.dot(v1, v2)

    # Compute the magnitudes (norms) of the vectors
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)

    # Calculate the cosine of the angle
    cos_angle = dot_product / (norm_v1 * norm_v2)

    # Calculate the angle in radians
    angle_rad = np.arccos(np.clip(cos_angle, -1.0, 1.0))  # Ensure cos_angle is within [-1, 1]

    # Convert the angle to degrees
    angle_deg = np.degrees(angle_rad)

    return angle_deg
angle = calculate_angle(v1, v2)

# Output the result
print(f"New Point ensuring 90° angle: {new_point} angle {angle}")
# print(f"Angle between vectors: {angle:.2f} degrees new_point {new_point}")







x, y, z = centroids[:, 0], centroids[:, 1], centroids[:, 2]

# Initial guess for sphere parameters
p0 = [0, 0, 0, 1]  # Initial guess for x_c, y_c, z_c, and radius

# Perform least squares fitting
result = least_squares(sphere_residuals, p0, args=(x, y, z))

# Extract sphere center
sphere_center = result.x[:3]

print("Sphere Center:", sphere_center)
#
#
#
#
#
# import numpy as np
#
# # Given points
# point_set_1 = np.array([
#     [526.94300641, 472.32845562, 250.55134799],
#     [526.05670058, 471.76627311, 251.02800936],
#     [528.11720195, 473.85483618, 249.75643457]
# ])
#
# point_set_2 = np.array([
#     [527.307041252457, 472.4338382791343, 251.5271558860837]
# ])
#
# # Calculate the RMSE for each corresponding pair of points
# differences = point_set_1 - point_set_2[0]
# squared_diff = differences ** 2
# mean_squared_error = np.mean(squared_diff, axis=0)
# rmse = np.sqrt(np.mean(mean_squared_error))
#
# print(f'rmse {rmse}')
#

