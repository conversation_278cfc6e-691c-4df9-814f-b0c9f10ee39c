import numpy as np
import pandas as pd
from scipy.stats import median_abs_deviation
from sklearn.linear_model import HuberRegressor
from sklearn.preprocessing import RobustScaler
import pickle
import random


def best_epsilon(scaled_knee_landmark_points, scaled_optical_points, optical_points, scaler):
    results = []
    for epsilon in np.arange(1.0, 2.0, 0.05):
        huber_models = []
        for i in range(scaled_optical_points.shape[1]):
            model = HuberRegressor(epsilon=epsilon)
            model.fit(scaled_knee_landmark_points, scaled_optical_points[:, i])
            huber_models.append(model)
        
        def predict(points):
            preds_scaled = np.column_stack([model.predict(points) for model in huber_models])
            return scaler.inverse_transform(preds_scaled)
        predicted = predict(scaled_knee_landmark_points)
        errors = np.linalg.norm(predicted - optical_points, axis=1)
        max_err = np.max(errors)
        mean_err = np.mean(errors)
        rmse = np.sqrt(np.mean(errors ** 2))
        results.append((epsilon, max_err, mean_err, rmse))

    # Print results and best epsilon
    print("epsilon\tmax_err\tmean_err\trmse")
    for epsilon, max_err, mean_err, rmse in results:
        print(f"{epsilon:.2f}\t{max_err:.4f}\t{mean_err:.4f}\t{rmse:.4f}")

    best = min(results, key=lambda x: x[3])
    print(f"\nBest epsilon (lowest RMSE): {best[0]:.2f} (RMSE={best[3]:.4f})")

def compute_transformation(ref_points, opt_points):
    """
    Compute similarity transformation (scale, rotation, translation)
    from ref_points to opt_points using the Kabsch algorithm.
    """
    # Center the points
    ref_centroid = np.mean(ref_points, axis=0)
    opt_centroid = np.mean(opt_points, axis=0)
    ref_centered = ref_points - ref_centroid
    opt_centered = opt_points - opt_centroid

    # Compute covariance matrix
    H = np.dot(ref_centered.T, opt_centered)

    # SVD decomposition
    U, S, Vt = np.linalg.svd(H)
    R = np.dot(Vt.T, U.T)

    # Correct reflection case
    if np.linalg.det(R) < 0:
        Vt[-1, :] *= -1
        R = np.dot(Vt.T, U.T)

    # Compute scale
    var_ref = np.sum(ref_centered ** 2)
    scale = np.sum(S) / var_ref

    # Compute translation
    t = opt_centroid - scale * np.dot(ref_centroid, R.T)

    return scale, R, t

def ransac_faulty_points(reference_points, optical_points, num_iterations=1000, threshold=0.05):
    N = reference_points.shape[0]
    best_inlier_count = 0
    best_inliers = []

    for iteration in range(num_iterations):
        # Randomly select 4 paired points
        indices = random.sample(range(N), 4)
        ref_subset = reference_points[indices]
        opt_subset = optical_points[indices]

        # Compute transformation
        s, R, t = compute_transformation(ref_subset, opt_subset)

        # Apply transformation to all reference points
        transformed_ref = s * np.dot(reference_points, R.T) + t

        # Compute errors
        errors = np.linalg.norm(optical_points - transformed_ref, axis=1)

        # Identify inliers
        inliers = np.where(errors < threshold)[0]

        # Update best model if more inliers found
        if len(inliers) > best_inlier_count:
            best_inlier_count = len(inliers)
            best_inliers = inliers

    # Identify outliers (faulty optical points)
    all_indices = set(range(N))
    inlier_set = set(best_inliers)
    outliers = list(all_indices - inlier_set)

    print(f"Indices of faulty optical points (outliers): {outliers}")
    return best_inliers, outliers

def euclidean_distances(points1, points2):
    """
    Calculate the Euclidean distance between corresponding 3D points in two arrays.

    Parameters:
        points1: np.ndarray of shape (n, 3)
        points2: np.ndarray of shape (n, 3)

    Returns:
        distances: np.ndarray of shape (n,)
    """
    # Ensure input arrays have the correct shape
    points1 = np.asarray(points1)
    points2 = np.asarray(points2)
    if points1.shape != points2.shape or points1.shape[1] != 3:
        raise ValueError("Both inputs must be of shape (n, 3)")

    # Compute the Euclidean distances
    distances = np.linalg.norm(points1 - points2, axis=1)
    return distances

def predict_values(knee_landmark_points, optical_points):
    scaler = RobustScaler()
    all_points = np.vstack([knee_landmark_points, optical_points])
    scaler.fit(all_points)

    scaled_knee_landmark_points = scaler.transform(knee_landmark_points)
    scaled_optical_points = scaler.transform(optical_points)
    # print(scaled_knee_landmark_points)
    # print(scaled_optical_points)

    huber_models = []
    for i in range(scaled_optical_points.shape[1]):
        model = HuberRegressor(epsilon=1.40)
        model.fit(scaled_knee_landmark_points, scaled_optical_points[:, i])
        huber_models.append(model)

    new_scaled_knee_landmark_points = scaler.transform(knee_landmark_points)
    preds = np.column_stack([model.predict(new_scaled_knee_landmark_points) for model in huber_models])
    scaled_preds = scaler.inverse_transform(preds)
    return scaled_preds

def find_outliers(scaled_preds, optical_points):
    dists = euclidean_distances(scaled_preds, optical_points)
    median = np.median(dists)
    mad = median_abs_deviation(dists)
    threshold = 1 * mad

    outliers = []
    inliers = []
    for i in range(dists.shape[0]):
        point = optical_points[i]
        predicted_point = scaled_preds[i]
        print(dists[i])
        if np.abs(dists[i] - median) > threshold:
            outliers.append({"landmark": landmark_variables[i], "point": point, "predicted_point": predicted_point})
        else:
            inliers.append({"landmark": landmark_variables[i], "point": point, "predicted_point": predicted_point})

    print(outliers)
    errors = scaled_preds - optical_points

    rmse_x = np.sqrt(np.mean(errors[:, 0] ** 2))
    rmse_y = np.sqrt(np.mean(errors[:, 1] ** 2))
    rmse_z = np.sqrt(np.mean(errors[:, 2] ** 2))

    # print('rmse_x:', rmse_x)
    # print('rmse_y:', rmse_y)
    # print('rmse_z:', rmse_z)

    for index, value in enumerate(outliers):
        point = value['point']
        predicted_point = value["predicted_point"]
        error_x = abs(predicted_point[0] - point[0])
        error_y = abs(predicted_point[1] - point[1])
        error_z = abs(predicted_point[2] - point[2])

        faulty_axes = []
        if error_x > rmse_x:
            faulty_axes.append('x')
        if error_y > rmse_y:
            faulty_axes.append('y')
        if error_z > rmse_z:
            faulty_axes.append('z')
        
        outliers[index]['faulty_axes'] = faulty_axes
     
    return outliers, inliers


# Example usage:
if __name__ == "__main__":
    df = pd.read_csv('Landmark-Xmm-Ymm-Zmm-Description.csv')

    # Extract X, Y, Z columns and convert to NumPy array
    knee_landmark_points = df[["X (mm)", "Y (mm)", "Z (mm)"]].to_numpy(dtype=np.float32)
    knee_landmark_points = knee_landmark_points[1:10]

    knee_landmark_points = np.array([
        [547.7842653418066, 475.4732041951944, 314.1721523391187],
        [547.80880591, 472.60104216, 309.49255855],
        [547.07402922, 473.58259021, 320.05448506],
        [550.51713013, 473.73974391, 311.47377189],
        [550.06440966, 474.37706699, 319.4552312 ],
        [551.42080318, 471.64308045, 312.78249852],
        [550.87385903, 472.24861728, 318.34750632],
        [544.54879411, 474.78666458, 316.66927878],
    ])

    optical_points = np.array([
        [547.7842653418066, 475.4732041951944, 314.1721523391187],
        [547.80880591, 472.60104216, 309.49255855],
        [547.07402922, 473.58259021, 320.05448506],
        [550.51713013, 473.73974391, 311.47377189],
        [550.06440966, 474.37706699, 319.4552312 ],
        [551.42080318, 471.64308045, 312.78249852],
        [550.87385903, 472.24861728, 318.34750632],
        # [544.54879411, 474.78666458, 316.66927878],
        [544.54879411, 474.78666458, 316.66927878],
    ])

    landmark_variables = {
        0: "FC",
        1: "ME",
        2: "LE",
        3: "MDC",
        4: "LDC",
        5: "MPC",
        6: "LPC",
        7: "AC",
    }

    # with open('kneeSequence\\registration_data\\hip_center.pickle', 'rb') as f:
    #     data = pickle.load(f)

    # print(data)
    
    # # Example data (replace with actual data)
    # reference_points = np.array([
    #     [0.0, 0.0, 0.0],
    #     [1.0, 0.0, 0.0],
    #     [0.0, 1.0, 0.0],
    #     [1.0, 1.0, 0.0],
    #     [0.5, 0.5, 0.0]
    # ])
    # optical_points = np.array([
    #     [0.1, -0.1, 0.0],
    #     [1.1, 0.0, 0.1],
    #     [0.0, 1.0, 0.0],
    #     [1.0, 1.2, 0.0],
    #     [5.0, 5.0, 5.0]  # Faulty point
    # ])

    scaled_preds = predict_values(knee_landmark_points, optical_points)
    # print(scaled_preds)

    outliers, inliers = find_outliers(scaled_preds, optical_points)

    # print("Inliers:")
    # for index, value in enumerate(inliers):
        # print(value["landmark"])
    
    # print("Outliers:")
    # for index, value in enumerate(outliers):
        # print(value["landmark"])
        # print('Faulty axes: ', value["faulty_axes"])
   
    # best_epsilon(scaled_knee_landmark_points, scaled_optical_points, optical_points, scaler)

    # def predict_robot(points):
    #     points_scaled = led_robust_scaler.transform(points)
    #     preds_scaled = np.column_stack([model.predict(points_scaled) for model in huber_models])
    #     return robot_robust_scaler.inverse_transform(preds_scaled)

    inliers, outliers = ransac_faulty_points(knee_landmark_points, optical_points, num_iterations=500, threshold=0.5)

    print("Inliers:")
    for num in inliers:
        print(landmark_variables[num])

    print("Outliers:")
    for num in outliers:
        print(landmark_variables[num])
    # print(inliers)
    # print(outliers)
