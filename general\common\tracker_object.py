# import time
#
# import cv2
# import numpy as np
# import math
# import os
#
#
# def distance_2d(LED1, LED2):
#     if len(LED1) == 3:
#         x1, y1, z1 = LED1
#     else:
#         x1, y1 = LED1
#     if len(LED2) == 3:
#         x2, y2, z2 = LED2
#     else:
#         x2, y2 = LED2
#     distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
#
#     return distance
#
#
# def get_distance_between_points(points_3D):
#     d1 = distance_2d(points_3D[0], points_3D[1])
#     d2 = distance_2d(points_3D[0], points_3D[2])
#     d3 = distance_2d(points_3D[1], points_3D[2])
#     # Create a list of tuples containing distances and points
#     distance_point_pairs = [(d1, tuple(points_3D[0]), tuple(points_3D[1])),
#                             (d2, tuple(points_3D[0]), tuple(points_3D[2])),
#                             (d3, tuple(points_3D[1]), tuple(points_3D[2]))]
#     # Sort the list based on distances
#     sorted_pairs = sorted(distance_point_pairs, key=lambda x: x[0])
#     # print(f'sorted_pairs {sorted_pairs}')
#
#     sorted_3d_coordinates = list([np.array(sorted_pairs[0][1]), np.array(sorted_pairs[0][2])])
#     for i in sorted_3d_coordinates:
#         points_3D = [point for point in points_3D if not all(a == b for a, b in zip(point, i))]
#
#     sorted_3d_coordinates.append(points_3D[0])
#
#     result = tuple(sorted_3d_coordinates)
#     # print(result)
#     return result
#
#
# def pixel_to_point(point_2d, z):
#     current_dir = os.path.dirname(__file__)
#     xml_path = os.path.join(current_dir, '../camera/Calibration_data/stereoCalibration.xml')
#
#     cv_file1 = cv2.FileStorage()
#     cv_file1.open(xml_path, cv2.FileStorage_READ)
#
#     newCameraMatrixL = cv_file1.getNode('newCameraMatrixL').mat()
#     # print(f'newCameraMatrixL {newCameraMatrixL}')
#     cv_file1.release()
#     # Convert 2D coordinates with depth to 3D coordinates
#     point_2d_homogeneous = np.append(point_2d, 1)
#     point_3d = z * np.dot(np.linalg.inv(newCameraMatrixL), point_2d_homogeneous)
#     point_3d = np.array((point_3d[0] + 500, point_3d[1] + 500, point_3d[2]))
#     return point_3d
#
#
# # def pixel_to_point(point_2d, disparity):
# #     # Load the calibration file
# #     current_dir = os.path.dirname(__file__)
# #     xml_path = os.path.join(current_dir, '../camera/Calibration_data/stereoCalibration.xml')
# #
# #     cv_file1 = cv2.FileStorage()
# #     cv_file1.open(xml_path, cv2.FileStorage_READ)
# #     # Retrieve the camera matrices, rotation, translation, and distortion coefficients
# #     newCameraMatrixL = cv_file1.getNode('newCameraMatrixL').mat()
# #     newCameraMatrixR = cv_file1.getNode('newCameraMatrixR').mat()
# #     # rot = cv_file1.getNode('rot').mat()
# #     trans = cv_file1.getNode('trans').mat()
# #     # distR = cv_file1.getNode('distR').mat()
# #     # distL = cv_file1.getNode('distL').mat()
# #
# #     cv_file1.release()
# #
# #
# #     # Extract focal length and principal point from the camera matrices
# #     fxL, fyL, cxL, cyL = newCameraMatrixL[0, 0], newCameraMatrixL[1, 1], newCameraMatrixL[0, 2], newCameraMatrixL[1, 2]
# #     fxR, fyR, cxR, cyR = newCameraMatrixR[0, 0], newCameraMatrixR[1, 1], newCameraMatrixR[0, 2], newCameraMatrixR[1, 2]
# #
# #     # Extract the baseline (distance between the cameras)
# #     baseline = 508
# #
# #     # Construct the Q matrix
# #     Q = np.array([
# #         [1, 0, 0, -cxL],
# #         [0, 1, 0, -cyL],
# #         [0, 0, 0, fxL],
# #         [0, 0, -1 / baseline, (cxL - cxR) / baseline]
# #     ])
# #
# #     # If Q matrix is not available, use your current method
# #     if Q.size == 0:
# #         # Convert 2D coordinates with depth to 3D coordinates using newCameraMatrixL
# #         point_2d_homogeneous = np.append(point_2d, 1)
# #         point_3d = z * np.dot(np.linalg.inv(newCameraMatrixL), point_2d_homogeneous)
# #         point_3d = np.array((point_3d[0] + 500, point_3d[1] + 500, point_3d[2]))
# #     else:
# #         # Use Q matrix to compute the 3D point from 2D pixel coordinates
# #         # disparity = 1 / z  # Assuming 'z' is the depth, disparity is its inverse (or calculated from stereo)
# #         point_2d_homogeneous = np.array([point_2d[0], point_2d[1], disparity, 1])
# #
# #         # Apply the Q matrix to the disparity (or depth) to get 3D coordinates
# #         point_3d_homogeneous = np.dot(Q, point_2d_homogeneous)
# #         point_3d = point_3d_homogeneous[:3] / point_3d_homogeneous[3]  # Normalize to get real coordinates
# #         # point_3d = point_3d_homogeneous[:3]  # Normalize to get real coordinates
# #         # print(f'point_3d {point_3d}')
# #         point_3d = np.array((point_3d[0] , point_3d[1] , -point_3d[2]))
# #     return point_3d
#
#
# class TriangleTracker:
#     def __init__(self, detections_right, detections_left, frame_right, frame_left, mode="default"):
#
#         # self.disparities = None
#         self.zmax = None
#         self.zmin = None
#         self.detections_right = self.sort_points(detections_right, mode)
#         self.detections_left = self.sort_points(detections_left, mode)
#         self.tracker = None
#         self.tip = None
#         self.transformation_matrix = None
#         self.baseline = 50.8  # Distance between the cameras [cm]
#         # self.f = 12  # Camera lense's focal length [mm]
#         self.alpha = 37.88  # Camera field of view in the horisontal plane [degrees]
#         self.frame_right = frame_right
#         self.frame_left = frame_left
#         self.LED1 = None
#         self.depths = None
#         self.f_pixel = (self.frame_left.shape[1] / 2) / np.tan(np.radians(self.alpha) / 2)
#         # self.Q = q_matrix
#
#     def sort_points(self, points, mode):
#         """Sort points based on the selected mode: default, 'F' (Femur), or 'T' (Tibia)."""
#         if mode.lower() == "f":
#             return self.custom_sort_femure(points)
#         elif mode.lower() == "t":
#             return self.custom_sort_tibia(points)
#         else:
#
#             return self.custom_sort(points)
#
#     def custom_sort(self, points):
#         # Step 1: Find the point with the lowest y-value
#         lowest_y_point = max(points, key=lambda p: p[1])
#
#         # Step 2: Remove it from the list and sort the remaining points based on x
#         remaining_points = [p for p in points if p != lowest_y_point]
#         sorted_remaining = sorted(remaining_points, key=lambda p: p[0])
#         data = [lowest_y_point] + sorted_remaining
#         # print(f'data****** {data}')
#         # Step 3: Return sorted list in order: lowest_y_point, then x-sorted points
#         return [lowest_y_point] + sorted_remaining
#
#     def custom_sort_femure(self, points):
#         # Step 1: Find the point with the highest x-value
#         highest_x_point = max(points, key=lambda p: p[0])
#
#         # Step 2: Remove it and find the point with the lowest y-value from the remaining points
#         remaining_points = [p for p in points if p != highest_x_point]
#         lowest_y_point = max(remaining_points, key=lambda p: p[1])
#
#         # Step 3: The last remaining point
#         third_point = [p for p in remaining_points if p != lowest_y_point][0]
#
#         # Step 4: Return the sorted order: highest_x_point, lowest_y_point, third_point
#         return [highest_x_point, lowest_y_point, third_point]
#
#     def custom_sort_tibia(self, points):
#         # Step 1: Find the point with the highest x-value
#         min_x_point = min(points, key=lambda p: p[0])
#
#         # Step 2: Remove it and find the point with the lowest y-value from the remaining points
#         remaining_points = [p for p in points if p != min_x_point]
#         lowest_y_point = min(remaining_points, key=lambda p: p[1])
#
#         # Step 3: The last remaining point
#         third_point = [p for p in remaining_points if p != lowest_y_point][0]
#
#         # Step 4: Return the sorted order: highest_x_point, lowest_y_point, third_point
#         return [min_x_point, lowest_y_point, third_point]
#
#     def sort_detections_y(self):
#         sorted_detections_left = sorted(self.detections_left, key=lambda detection: detection[1])
#         sorted_detections_right = sorted(self.detections_right, key=lambda detection: detection[1])
#         self.detections_right = sorted_detections_right[0:3]
#         self.detections_left = sorted_detections_left[0:3]
#         return self.detections_right, self.detections_left
#
#     def sort_detections_x(self):
#         sorted_detections_left = sorted(self.detections_left, key=lambda detection: detection[0])
#         sorted_detections_right = sorted(self.detections_right, key=lambda detection: detection[0])
#         self.detections_right = sorted_detections_right[0:3]
#         self.detections_left = sorted_detections_left[0:3]
#         return self.detections_right, self.detections_left
#
#     def find_depth(self):
#         height_right, width_right = self.frame_right.shape[:2]
#         height_left, width_left = self.frame_left.shape[:2]
#
#         if width_right != width_left:
#             print('Left and right camera frames do not have the same pixel width')
#             return None
#
#         self.depths = []
#         # self.disparities = []
#         for right_point, left_point in zip(self.detections_right, self.detections_left):
#             x_right = right_point[0]
#             y_right = right_point[1]
#             x_left = left_point[0]
#             y_left = left_point[1]
#
#             # disparity = (x_left - x_right) / 16.0
#             disparity = (x_left - x_right)
#
#             if disparity != 0:
#                 zDepth = (self.baseline * self.f_pixel) / disparity
#                 self.depths.append(zDepth)
#
#                 # self.disparities.append(disparity)
#             else:
#                 print('Disparity is zero for one of the points, skipping depth calculation.')
#
#         return self.depths  # Ensure depths are returned
#
#     def getLEDcordinates(self):
#         LED1 = pixel_to_point(self.detections_left[0], self.depths[0])
#         LED2 = pixel_to_point(self.detections_left[1], self.depths[1])
#         LED3 = pixel_to_point(self.detections_left[2], self.depths[2])
#         # Compute disparity map
#         # LED1 = LED1 * (self.zmax - self.zmin) + self.zmin
#         # LED2 = LED2 * (self.zmax - self.zmin) + self.zmin
#         # LED3 = LED3 * (self.zmax - self.zmin) + self.zmin
#         # LED1 = pixel_to_point(self.detections_left[0], self.detections_right[0])
#         # LED2 = pixel_to_point(self.detections_left[1], self.detections_right[1])
#         # LED3 = pixel_to_point(self.detections_left[2], self.detections_right[2])
#
#         LEDs = [LED1, LED2, LED3]
#         LEDs = get_distance_between_points(LEDs)
#         return LEDs
#
#     def get2LEDcordinates(self):
#         LED1 = pixel_to_point(self.detections_left[0], self.depths[0])
#         LED2 = pixel_to_point(self.detections_left[1], self.depths[1])
#         LEDs = [LED1, LED2]
#         # LEDs = get_distance_between_points(LEDs)
#         return LEDs
#
#     def getStylusPoint(self, result=None):
#         # Convert pixel detections to point coordinates
#         LED1 = pixel_to_point(self.detections_left[0], self.depths[0])
#         LED2 = pixel_to_point(self.detections_left[1], self.depths[1])
#         LED3 = pixel_to_point(self.detections_left[2], self.depths[2])
#         # LED1 = LED1 * (self.zmax - self.zmin) + self.zmin
#         # LED2 = LED2 * (self.zmax - self.zmin) + self.zmin
#         # LED3 = LED3 * (self.zmax - self.zmin) + self.zmin
#         LEDs = [LED1, LED2, LED3]
#         result = get_distance_between_points(LEDs)
#
#         # Get distances between points if result is not provided
#         def stylus_point(A, B):
#             # Calculate the vector from A to B
#             V_AB = B - A
#
#             # Calculate the normalized vector
#             V_AB_normalized = V_AB / np.linalg.norm(V_AB)
#
#             # Calculate the coordinates of point C
#             # C = B + (167.2 / 104.7) * V_AB_normalized * np.linalg.norm(V_AB) 124.60
#             C = B + (124.60 / 103.0) * V_AB_normalized * np.linalg.norm(V_AB)
#             # C[1]= -C[1]
#             return C
#
#         led_a = np.array(result[2])
#         led_b = np.array(result[1])
#         led_c = np.array(result[0])
#         B = np.array(led_b)
#         C = np.array(led_c)
#
#         mid_bc = ((B + C) / 2)
#         return stylus_point(mid_bc, led_a)
#
#     def getAPointfromTracker(self, result=None):
#         # Convert pixel detections to point coordinates
#         LED1 = pixel_to_point(self.detections_left[0], self.depths[0])
#         LED2 = pixel_to_point(self.detections_left[1], self.depths[1])
#         LED3 = pixel_to_point(self.detections_left[2], self.depths[2])
#         LEDs = [LED1, LED2, LED3]
#         result = get_distance_between_points(LEDs)
#
#         # Get distances between points if result is not provided
#         def stylus_point_tracker(A, B):
#             # Calculate the vector from A to B
#             V_AB = B - A
#
#             # Calculate the normalized vector
#             V_AB_normalized = V_AB / np.linalg.norm(V_AB)
#
#             # Calculate the coordinates of point C
#             # C = B + (167.2 / 104.7) * V_AB_normalized * np.linalg.norm(V_AB) 124.60
#             C = B + (400 / 108.0) * V_AB_normalized * np.linalg.norm(V_AB)
#
#             return C
#
#         led_a = np.array(result[2])
#         led_b = np.array(result[1])
#         led_c = np.array(result[0])
#         B = np.array(led_b)
#         C = np.array(led_c)
#
#         mid_bc = np.abs((B + C) / 2)
#         return stylus_point_tracker(mid_bc, led_a)
#
#     def getStylusPointReamer(self, result=None):
#         # Convert pixel detections to point coordinates
#         LED1 = pixel_to_point(self.detections_left[0], self.depths[0])
#         LED2 = pixel_to_point(self.detections_left[1], self.depths[1])
#         LED3 = pixel_to_point(self.detections_left[2], self.depths[2])
#         LEDs = [LED1, LED2, LED3]
#         result = get_distance_between_points(LEDs)
#
#         # Get distances between points if result is not provided
#         def stylus_point(A, B):
#             # Calculate the vector from A to B
#             V_AB = B - A
#
#             # Calculate the normalized vector
#             V_AB_normalized = V_AB / np.linalg.norm(V_AB)
#
#             # Calculate the coordinates of point C
#             C = B + (153.35 / 103.28) * V_AB_normalized * np.linalg.norm(V_AB)
#
#             return C
#
#         led_a = np.array(result[2])
#         led_b = np.array(result[1])
#         led_c = np.array(result[0])
#         B = np.array(led_b)
#         C = np.array(led_c)
#
#         mid_bc = np.abs((B + C) / 2)
#         return stylus_point(mid_bc, led_a)
#
#     def getrobotarmtipwithzcorrection(self, result=None):
#         def stylus_point(A, B):
#             # Calculate the vector from A to B
#             V_AB = B - A
#
#             # Calculate the normalized vector
#             V_AB_normalized = V_AB / np.linalg.norm(V_AB)
#
#             # Calculate the coordinates of point C
#             C = B + (612 / 111) * V_AB_normalized * np.linalg.norm(V_AB)
#
#             return C
#
#         led_a = np.array(result[2])
#         led_b = np.array(result[1])
#         led_c = np.array(result[0])
#         B = np.array(led_b)
#         C = np.array(led_c)
#
#         mid_bc = np.abs((B + C) / 2)
#         return stylus_point(mid_bc, led_a)






import time

import cv2
import numpy as np
import math
import os


def distance_2d(LED1, LED2):
    if len(LED1) == 3:
        x1, y1, z1 = LED1
    else:
        x1, y1 = LED1
    if len(LED2) == 3:
        x2, y2, z2 = LED2
    else:
        x2, y2 = LED2
    distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

    return distance


def get_distance_between_points(points_3D):
    d1 = distance_2d(points_3D[0], points_3D[1])
    d2 = distance_2d(points_3D[0], points_3D[2])
    d3 = distance_2d(points_3D[1], points_3D[2])
    # Create a list of tuples containing distances and points
    distance_point_pairs = [(d1, tuple(points_3D[0]), tuple(points_3D[1])),
                            (d2, tuple(points_3D[0]), tuple(points_3D[2])),
                            (d3, tuple(points_3D[1]), tuple(points_3D[2]))]
    # Sort the list based on distances
    sorted_pairs = sorted(distance_point_pairs, key=lambda x: x[0])
    # print(f'sorted_pairs {sorted_pairs}')

    sorted_3d_coordinates = list([np.array(sorted_pairs[0][1]), np.array(sorted_pairs[0][2])])
    for i in sorted_3d_coordinates:
        points_3D = [point for point in points_3D if not all(a == b for a, b in zip(point, i))]

    sorted_3d_coordinates.append(points_3D[0])

    result = tuple(sorted_3d_coordinates)
    # print(result)
    return result


def pixel_to_point(point_2d, z):
    current_dir = os.path.dirname(__file__)

    data = np.load(r'D:\SpineSurgery\pythonProject\kneeSequence\marking\primary\stereo_calibration_parameters.npz')
    newCameraMatrixL = data['mtxL']
    # distL = data['distL']
    # mtxR = data['mtxR']
    # distR = data['distR']
    # R = data['R']
    # T = data['T']


    # xml_path = os.path.join(current_dir, '../camera/Calibration_data/stereoCalibration.xml')

    # cv_file1 = cv2.FileStorage()
    # cv_file1.open(xml_path, cv2.FileStorage_READ)

    # newCameraMatrixL = cv_file1.getNode('newCameraMatrixL').mat()
    # print(f'newCameraMatrixL {newCameraMatrixL}')
    # cv_file1.release()
    # Convert 2D coordinates with depth to 3D coordinates
    point_2d_homogeneous = np.append(point_2d, 1)
    point_3d = z * np.dot(np.linalg.inv(newCameraMatrixL), point_2d_homogeneous)
    point_3d = np.array((point_3d[0] + 500, point_3d[1] + 500, point_3d[2]))
    return point_3d


# def pixel_to_point(point_2d, disparity):
#     # Load the calibration file
#     current_dir = os.path.dirname(__file__)
#     xml_path = os.path.join(current_dir, '../camera/Calibration_data/stereoCalibration.xml')
#
#     cv_file1 = cv2.FileStorage()
#     cv_file1.open(xml_path, cv2.FileStorage_READ)
#     # Retrieve the camera matrices, rotation, translation, and distortion coefficients
#     newCameraMatrixL = cv_file1.getNode('newCameraMatrixL').mat()
#     newCameraMatrixR = cv_file1.getNode('newCameraMatrixR').mat()
#     # rot = cv_file1.getNode('rot').mat()
#     trans = cv_file1.getNode('trans').mat()
#     # distR = cv_file1.getNode('distR').mat()
#     # distL = cv_file1.getNode('distL').mat()
#
#     cv_file1.release()
#
#
#     # Extract focal length and principal point from the camera matrices
#     fxL, fyL, cxL, cyL = newCameraMatrixL[0, 0], newCameraMatrixL[1, 1], newCameraMatrixL[0, 2], newCameraMatrixL[1, 2]
#     fxR, fyR, cxR, cyR = newCameraMatrixR[0, 0], newCameraMatrixR[1, 1], newCameraMatrixR[0, 2], newCameraMatrixR[1, 2]
#
#     # Extract the baseline (distance between the cameras)
#     baseline = 508
#
#     # Construct the Q matrix
#     Q = np.array([
#         [1, 0, 0, -cxL],
#         [0, 1, 0, -cyL],
#         [0, 0, 0, fxL],
#         [0, 0, -1 / baseline, (cxL - cxR) / baseline]
#     ])
#
#     # If Q matrix is not available, use your current method
#     if Q.size == 0:
#         # Convert 2D coordinates with depth to 3D coordinates using newCameraMatrixL
#         point_2d_homogeneous = np.append(point_2d, 1)
#         point_3d = z * np.dot(np.linalg.inv(newCameraMatrixL), point_2d_homogeneous)
#         point_3d = np.array((point_3d[0] + 500, point_3d[1] + 500, point_3d[2]))
#     else:
#         # Use Q matrix to compute the 3D point from 2D pixel coordinates
#         # disparity = 1 / z  # Assuming 'z' is the depth, disparity is its inverse (or calculated from stereo)
#         point_2d_homogeneous = np.array([point_2d[0], point_2d[1], disparity, 1])
#
#         # Apply the Q matrix to the disparity (or depth) to get 3D coordinates
#         point_3d_homogeneous = np.dot(Q, point_2d_homogeneous)
#         point_3d = point_3d_homogeneous[:3] / point_3d_homogeneous[3]  # Normalize to get real coordinates
#         # point_3d = point_3d_homogeneous[:3]  # Normalize to get real coordinates
#         # print(f'point_3d {point_3d}')
#         point_3d = np.array((point_3d[0] , point_3d[1] , -point_3d[2]))
#     return point_3d


class TriangleTracker:
    def __init__(self, detections_right, detections_left, mode="default"):

        # self.disparities = None
        self.zmax = None
        self.zmin = None
        self.detections_right = self.sort_points(detections_right, mode)
        self.detections_left = self.sort_points(detections_left, mode)
        self.tracker = None
        self.tip = None
        self.transformation_matrix = None
        self.baseline = 50.8  # Distance between the cameras [cm]
        # self.f = 12  # Camera lense's focal length [mm]
        self.alpha = 37.88  # Camera field of view in the horisontal plane [degrees]
        # self.frame_right = frame_right
        # self.frame_left = frame_left
        self.LED1 = None
        self.depths = None
        self.f_pixel = (1936 / 2) / np.tan(np.radians(self.alpha) / 2)
        # self.Q = q_matrix

    def sort_points(self, points, mode):
        """Sort points based on the selected mode: default, 'F' (Femur), or 'T' (Tibia)."""
        if mode.lower() == "f":
            return self.custom_sort_femure(points)
        elif mode.lower() == "t":
            return self.custom_sort_tibia(points)
        else:
            return self.custom_sort(points)

    def custom_sort(self, points):
        # print(f' before sort points {points}')
        points = [tuple(map(float, p)) for p in points]

        # Step 1: Find the point with the lowest Y value
        lowest_y_index = min(range(len(points)), key=lambda i: points[i][1])
        lowest_y_point = points[lowest_y_index]

        # Step 2: Sort the remaining points by X value
        remaining_indices = [i for i in range(len(points)) if i != lowest_y_index]
        sorted_remaining = sorted([points[i] for i in remaining_indices], key=lambda p: p[0])

        # Step 3: Combine
        data = [lowest_y_point] + sorted_remaining
        # print(f'after sorted data****** {data}')
        return data

    def custom_sort_femure(self, points):
        # Step 1: Find the point with the highest x-value
        highest_x_point = max(points, key=lambda p: p[0])

        # Step 2: Remove it and find the point with the lowest y-value from the remaining points
        remaining_points = [p for p in points if p != highest_x_point]
        lowest_y_point = max(remaining_points, key=lambda p: p[1])

        # Step 3: The last remaining point
        third_point = [p for p in remaining_points if p != lowest_y_point][0]

        # Step 4: Return the sorted order: highest_x_point, lowest_y_point, third_point
        return [highest_x_point, lowest_y_point, third_point]

    def custom_sort_tibia(self, points):
        points = [tuple(map(float, p)) for p in points]

        # Step 1: Find point with **lowest x** value (assumed to be the one further left)
        leftmost = min(points, key=lambda p: p[0])

        # Step 2: From remaining, find point with **lowest y** (topmost)
        remaining = [p for p in points if not np.allclose(p, leftmost)]
        topmost = min(remaining, key=lambda p: p[1])

        # Step 3: Remaining point
        third = [p for p in remaining if not np.allclose(p, topmost)][0]
        data = [leftmost, topmost, third]
        # print(f'custom_sort_tibia ******* {data}')
        return [leftmost, topmost, third]

    def sort_detections_y(self):
        sorted_detections_left = sorted(self.detections_left, key=lambda detection: detection[1])
        sorted_detections_right = sorted(self.detections_right, key=lambda detection: detection[1])
        self.detections_right = sorted_detections_right[0:3]
        self.detections_left = sorted_detections_left[0:3]
        return self.detections_right, self.detections_left

    def sort_detections_x(self):
        sorted_detections_left = sorted(self.detections_left, key=lambda detection: detection[0])
        sorted_detections_right = sorted(self.detections_right, key=lambda detection: detection[0])
        self.detections_right = sorted_detections_right[0:3]
        self.detections_left = sorted_detections_left[0:3]
        return self.detections_right, self.detections_left

    def find_depth(self):
        # height_right, width_right = self.frame_right.shape[:2]
        # height_left, width_left = self.frame_left.shape[:2]
        # if width_right != width_left:
        #     print('Left and right camera frames do not have the same pixel width')
        #     return None
        height_right, width_right = 1936, 1464
        height_left, width_left = 1936, 1464



        self.depths = []
        # self.disparities = []
        for right_point, left_point in zip(self.detections_right, self.detections_left):
            x_right = right_point[0]
            y_right = right_point[1]
            x_left = left_point[0]
            y_left = left_point[1]

            # disparity = (x_left - x_right) / 16.0
            disparity = (x_left - x_right)

            if disparity != 0:
                zDepth = (self.baseline * self.f_pixel) / disparity
                self.depths.append(zDepth)

                # self.disparities.append(disparity)
            else:
                print('Disparity is zero for one of the points, skipping depth calculation.')

        return self.depths  # Ensure depths are returned

    def getLEDcordinates(self):
        # print(f'self.detections_left   {self.detections_left}')
        # print(f'self.detections_right  {self.detections_right}')
        self.find_depth()
        LED1 = pixel_to_point(self.detections_left[0], self.depths[0])
        LED2 = pixel_to_point(self.detections_left[1], self.depths[1])
        LED3 = pixel_to_point(self.detections_left[2], self.depths[2])
        # Compute disparity map
        # LED1 = LED1 * (self.zmax - self.zmin) + self.zmin
        # LED2 = LED2 * (self.zmax - self.zmin) + self.zmin
        # LED3 = LED3 * (self.zmax - self.zmin) + self.zmin
        # LED1 = pixel_to_point(self.detections_left[0], self.detections_right[0])
        # LED2 = pixel_to_point(self.detections_left[1], self.detections_right[1])
        # LED3 = pixel_to_point(self.detections_left[2], self.detections_right[2])

        LEDs = [LED1, LED2, LED3]
        LEDs = get_distance_between_points(LEDs)
        return LEDs

    def get2LEDcordinates(self):
        LED1 = pixel_to_point(self.detections_left[0], self.depths[0])
        LED2 = pixel_to_point(self.detections_left[1], self.depths[1])
        LEDs = [LED1, LED2]
        # LEDs = get_distance_between_points(LEDs)
        return LEDs

    def getStylusPoint(self, result=None):
        self.find_depth()
        LED1 = pixel_to_point(self.detections_left[0], self.depths[0])
        LED2 = pixel_to_point(self.detections_left[1], self.depths[1])
        LED3 = pixel_to_point(self.detections_left[2], self.depths[2])
        # LED1 = LED1 * (self.zmax - self.zmin) + self.zmin
        # LED2 = LED2 * (self.zmax - self.zmin) + self.zmin
        # LED3 = LED3 * (self.zmax - self.zmin) + self.zmin
        LEDs = [LED1, LED2, LED3]
        result = get_distance_between_points(LEDs)

        # Get distances between points if result is not provided
        def stylus_point(A, B):
            # Calculate the vector from A to B
            V_AB = B - A

            # Calculate the normalized vector
            V_AB_normalized = V_AB / np.linalg.norm(V_AB)

            # Calculate the coordinates of point C
            # C = B + (167.2 / 104.7) * V_AB_normalized * np.linalg.norm(V_AB) 124.60
            C = B + (124.60 / 103.0) * V_AB_normalized * np.linalg.norm(V_AB)
            # C[1]= -C[1]
            return C

        led_a = np.array(result[2])
        led_b = np.array(result[1])
        led_c = np.array(result[0])
        B = np.array(led_b)
        C = np.array(led_c)

        mid_bc = ((B + C) / 2)
        return stylus_point(mid_bc, led_a)


    def getStylusPointBurr(self, result=None):
        self.find_depth()
        LED1 = pixel_to_point(self.detections_left[0], self.depths[0])
        LED2 = pixel_to_point(self.detections_left[1], self.depths[1])
        LED3 = pixel_to_point(self.detections_left[2], self.depths[2])
        # LED1 = LED1 * (self.zmax - self.zmin) + self.zmin
        # LED2 = LED2 * (self.zmax - self.zmin) + self.zmin
        # LED3 = LED3 * (self.zmax - self.zmin) + self.zmin
        LEDs = [LED1, LED2, LED3]
        result = get_distance_between_points(LEDs)

        # Get distances between points if result is not provided
        def stylus_point(A, B):
            # Calculate the vector from A to B
            V_AB = B - A

            # Calculate the normalized vector
            V_AB_normalized = V_AB / np.linalg.norm(V_AB)

            # Calculate the coordinates of point C
            # C = B + (167.2 / 104.7) * V_AB_normalized * np.linalg.norm(V_AB) 124.60
            C = B + (147.5 / 102.75) * V_AB_normalized * np.linalg.norm(V_AB)
            # C[1]= -C[1]
            return C

        led_a = np.array(result[2])
        led_b = np.array(result[1])
        led_c = np.array(result[0])
        B = np.array(led_b)
        C = np.array(led_c)

        mid_bc = ((B + C) / 2)
        return stylus_point(mid_bc, led_a)

    def getAPointfromTracker(self, result=None):
        # Convert pixel detections to point coordinates
        LED1 = pixel_to_point(self.detections_left[0], self.depths[0])
        LED2 = pixel_to_point(self.detections_left[1], self.depths[1])
        LED3 = pixel_to_point(self.detections_left[2], self.depths[2])
        LEDs = [LED1, LED2, LED3]
        result = get_distance_between_points(LEDs)

        # Get distances between points if result is not provided
        def stylus_point_tracker(A, B):
            # Calculate the vector from A to B
            V_AB = B - A

            # Calculate the normalized vector
            V_AB_normalized = V_AB / np.linalg.norm(V_AB)

            # Calculate the coordinates of point C
            # C = B + (167.2 / 104.7) * V_AB_normalized * np.linalg.norm(V_AB) 124.60
            C = B + (400 / 108.0) * V_AB_normalized * np.linalg.norm(V_AB)

            return C

        led_a = np.array(result[2])
        led_b = np.array(result[1])
        led_c = np.array(result[0])
        B = np.array(led_b)
        C = np.array(led_c)

        mid_bc = np.abs((B + C) / 2)
        return stylus_point_tracker(mid_bc, led_a)

    def getStylusPointReamer(self, result=None):
        # Convert pixel detections to point coordinates
        LED1 = pixel_to_point(self.detections_left[0], self.depths[0])
        LED2 = pixel_to_point(self.detections_left[1], self.depths[1])
        LED3 = pixel_to_point(self.detections_left[2], self.depths[2])
        LEDs = [LED1, LED2, LED3]
        result = get_distance_between_points(LEDs)

        # Get distances between points if result is not provided
        def stylus_point(A, B):
            # Calculate the vector from A to B
            V_AB = B - A

            # Calculate the normalized vector
            V_AB_normalized = V_AB / np.linalg.norm(V_AB)

            # Calculate the coordinates of point C
            C = B + (153.35 / 103.28) * V_AB_normalized * np.linalg.norm(V_AB)

            return C

        led_a = np.array(result[2])
        led_b = np.array(result[1])
        led_c = np.array(result[0])
        B = np.array(led_b)
        C = np.array(led_c)

        mid_bc = np.abs((B + C) / 2)
        return stylus_point(mid_bc, led_a)

    def getrobotarmtipwithzcorrection(self, result=None):
        def stylus_point(A, B):
            # Calculate the vector from A to B
            V_AB = B - A

            # Calculate the normalized vector
            V_AB_normalized = V_AB / np.linalg.norm(V_AB)

            # Calculate the coordinates of point C
            C = B + (612 / 111) * V_AB_normalized * np.linalg.norm(V_AB)

            return C

        led_a = np.array(result[2])
        led_b = np.array(result[1])
        led_c = np.array(result[0])
        B = np.array(led_b)
        C = np.array(led_c)

        mid_bc = np.abs((B + C) / 2)
        return stylus_point(mid_bc, led_a)
