QLabel {
    font-weight: 400;
    text-align: center;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    font-family: 'Poppins-Light', sans-serif;
    color: #fff;
    box-sizing: border-box;
    background: #2F2B41;
    border-radius: 8px;
    font-size: 14px;
    padding: 2px 20px;
    flex: 1;
    line-height: 25px;
}

QLabel b {
    font-size: 24px; /* For the bold title */
}

.date-time-label {
    background: #2F2B41;
    color: #fff;
    font-family: 'Poppins-Light';
    font-weight: 400;
    font-size: 15px;
    line-height: 20px;
    text-align: right;
    padding: 2px 20px;
    border-radius: 8px;
    margin-right: 10px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 360px;
}

QPushButton {
    background-color: #2D2264;
    color: #fff;
    font-family: 'Poppins-Light';
    font-size: 15px;
    border-radius: 8px;
    padding: 5px 10px;
}

QPushButton:hover {
    background-color: #131022;
}
