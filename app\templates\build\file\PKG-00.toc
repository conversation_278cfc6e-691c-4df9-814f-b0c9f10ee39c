('D:\\SpineSurgery\\pythonProject\\app\\templates\\build\\file\\file.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\SpineSurgery\\pythonProject\\app\\templates\\build\\file\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\SpineSurgery\\pythonProject\\app\\templates\\build\\file\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\SpineSurgery\\pythonProject\\app\\templates\\build\\file\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\SpineSurgery\\pythonProject\\app\\templates\\build\\file\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\SpineSurgery\\pythonProject\\app\\templates\\build\\file\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\SpineSurgery\\pythonProject\\app\\templates\\build\\file\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('file',
   'D:\\SpineSurgery\\pythonProject\\app\\templates\\file.py',
   'PYSOURCE'),
  ('python311.dll', 'D:\\anaconda3\\python311.dll', 'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('_decimal.pyd', 'D:\\anaconda3\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\anaconda3\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\anaconda3\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\anaconda3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\anaconda3\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\anaconda3\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\anaconda3\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp311-win_amd64.pyd',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\sip.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\anaconda3\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'D:\\anaconda3\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\anaconda3\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('MSVCP140.dll', 'D:\\anaconda3\\MSVCP140.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\anaconda3\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll', 'D:\\anaconda3\\Library\\bin\\liblzma.dll', 'BINARY'),
  ('LIBBZ2.dll', 'D:\\anaconda3\\Library\\bin\\LIBBZ2.dll', 'BINARY'),
  ('python3.dll', 'D:\\anaconda3\\python3.dll', 'BINARY'),
  ('ucrtbase.dll', 'D:\\anaconda3\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\anaconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\anaconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\anaconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\anaconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\anaconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_FRONT_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_FRONT_1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_FRONT_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_FRONT_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_FRONT_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_FRONT_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_FRONT_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_FRONT_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_FRONT_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_FRONT_5.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_PER_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_PER_1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_PER_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_PER_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_PER_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_PER_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_PER_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_PER_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_PER_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\GREEN\\FEMUR_PER_5.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_FRONT_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_FRONT_1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_FRONT_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_FRONT_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_FRONT_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_FRONT_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_FRONT_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_FRONT_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_FRONT_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_FRONT_5.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_PER_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_PER_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_PER_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_PER_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_PER_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_PER_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_PER_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\RED\\FEMUR_PER_5.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_FRONT_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_FRONT_1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_FRONT_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_FRONT_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_FRONT_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_FRONT_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_FRONT_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_FRONT_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_FRONT_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_FRONT_5.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_PER_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_PER_1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_PER_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_PER_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_PER_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_PER_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_PER_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_PER_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC '
   'CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_PER_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\FEMUR_DUAL_CUTS\\YELLOW\\FEMUR_PER_5.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC CUTS\\POSTERIOR CHAMFER G.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\POSTERIOR CHAMFER G.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC CUTS\\POSTERIOR CHAMFER R.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\POSTERIOR CHAMFER R.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC CUTS\\POSTERIOR CUT G.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\POSTERIOR CUT G.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\PRIMARY ROBOTIC CUTS\\POSTERIOR CUTS R.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\PRIMARY '
   'ROBOTIC CUTS\\POSTERIOR CUTS R.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_1-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_1-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_2-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_2-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_3-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_3-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_4-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_4-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_5-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_5-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\GREEN\\PG_7_ME_Green_5.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_1-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_1-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_2-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_2-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_3-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_3-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_4-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_4-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_5-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_5-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\RED\\PG_7_ME_Red_5.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_1-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_1-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_2-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_2-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_3-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_3-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_4-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_4-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_5-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_5-1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\FEMUR\\YELLOW\\PG_7_ME_Yellow_5.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\PARTIAL_KNEE.rar',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\PARTIAL_KNEE.rar',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_PER_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_PER_1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_PER_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_PER_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_PER_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_PER_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_PER_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_PER_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_PER_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_PER_5.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_TOP_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_TOP_1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_TOP_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_TOP_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_TOP_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_TOP_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_TOP_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_TOP_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_TOP_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\GREEN\\TIBIA_PARTIAL_KNEE_TOP_5.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_PER_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_PER_1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_PER_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_PER_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_PER_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_PER_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_PER_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_PER_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_PER_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_PER_5.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_TOP_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_TOP_1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_TOP_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_TOP_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_TOP_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_TOP_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_TOP_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_TOP_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_TOP_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\RED\\TIBIA_PARTIAL_KNEE_TOP_5.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_PER_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_PER_1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_PER_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_PER_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_PER_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_PER_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_PER_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_PER_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_PER_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_PER_5.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_TOP_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_TOP_1.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_TOP_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_TOP_2.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_TOP_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_TOP_3.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_TOP_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_TOP_4.jpg',
   'DATA'),
  ('images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_TOP_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\NEWLAYERGUIKNEE\\UNIKNEE\\TIBIA\\YELLOW\\TIBIA_PARTIAL_KNEE_TOP_5.jpg',
   'DATA'),
  ('images\\artikon_logo.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\artikon_logo.png',
   'DATA'),
  ('images\\banner.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\banner.png',
   'DATA'),
  ('images\\bone\\Bone-image-1.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\Bone-image-1.png',
   'DATA'),
  ('images\\bone\\Bone-image-10.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\Bone-image-10.png',
   'DATA'),
  ('images\\bone\\Bone-image-11.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\Bone-image-11.png',
   'DATA'),
  ('images\\bone\\Bone-image-12.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\Bone-image-12.png',
   'DATA'),
  ('images\\bone\\Bone-image-13.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\Bone-image-13.png',
   'DATA'),
  ('images\\bone\\Bone-image-2.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\Bone-image-2.png',
   'DATA'),
  ('images\\bone\\Bone-image-3.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\Bone-image-3.png',
   'DATA'),
  ('images\\bone\\Bone-image-4.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\Bone-image-4.png',
   'DATA'),
  ('images\\bone\\Bone-image-5.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\Bone-image-5.png',
   'DATA'),
  ('images\\bone\\Bone-image-6.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\Bone-image-6.png',
   'DATA'),
  ('images\\bone\\Bone-image-7.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\Bone-image-7.png',
   'DATA'),
  ('images\\bone\\Bone-image-8.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\Bone-image-8.png',
   'DATA'),
  ('images\\bone\\Bone-image-9.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\Bone-image-9.png',
   'DATA'),
  ('images\\bone\\ankle-center.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\ankle-center.png',
   'DATA'),
  ('images\\bone\\anterior-cortex.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\anterior-cortex.png',
   'DATA'),
  ('images\\bone\\bone1.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\bone1.png',
   'DATA'),
  ('images\\bone\\bone2.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\bone2.png',
   'DATA'),
  ('images\\bone\\bootm-img-3.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\bootm-img-3.png',
   'DATA'),
  ('images\\bone\\bottom-img-1.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\bottom-img-1.png',
   'DATA'),
  ('images\\bone\\bottom-img-2.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\bottom-img-2.png',
   'DATA'),
  ('images\\bone\\femoral-ap-axis.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\femoral-ap-axis.png',
   'DATA'),
  ('images\\bone\\femur-center.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\femur-center.png',
   'DATA'),
  ('images\\bone\\femur-first.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\femur-first.jpg',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00000.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00000.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00001.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00001.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00002.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00002.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00003.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00003.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00004.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00004.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00005.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00005.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00006.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00006.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00007.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00007.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00008.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00008.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00009.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00009.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00010.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00010.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00011.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00011.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00012.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00012.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00013.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00013.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00014.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00014.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00015.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00015.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00016.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00016.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00017.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00017.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00018.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00018.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00019.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00019.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00020.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00020.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00021.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00021.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00022.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00022.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00023.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00023.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00024.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00024.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00025.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00025.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00026.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00026.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00027.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00027.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00028.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00028.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00029.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00029.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00030.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00030.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00031.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00031.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00032.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00032.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00033.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00033.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00034.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00034.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00035.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00035.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00036.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00036.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00037.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00037.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00038.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00038.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00039.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00039.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00040.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00040.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00041.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00041.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00042.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00042.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00043.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00043.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00044.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00044.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00045.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00045.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00046.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00046.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00047.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00047.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00048.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00048.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00049.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00049.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00050.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00050.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00051.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00051.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00052.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00052.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00053.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00053.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00054.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00054.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00055.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00055.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00056.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00056.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00057.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00057.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00058.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00058.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00059.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00059.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00060.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00060.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00061.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00061.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00062.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00062.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00063.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00063.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00064.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00064.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00065.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00065.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00066.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00066.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00067.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00067.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00068.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00068.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00069.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00069.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00070.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00070.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00071.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00071.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00072.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00072.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00073.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00073.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00074.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00074.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00075.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00075.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00076.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00076.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00077.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00077.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00078.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00078.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00079.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00079.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00080.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00080.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00081.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00081.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00082.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00082.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00083.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00083.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00084.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00084.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00085.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00085.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00086.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00086.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00087.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00087.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00088.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00088.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00089.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00089.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00090.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00090.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00091.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00091.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00092.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00092.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00093.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00093.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00094.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00094.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00095.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00095.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00096.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00096.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00097.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00097.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00098.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00098.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00099.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00099.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00100.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00100.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00101.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00101.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00102.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00102.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00103.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00103.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00104.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00104.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00105.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00105.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00106.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00106.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00107.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00107.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00108.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00108.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00109.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00109.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00110.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00110.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00111.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00111.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00112.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00112.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00113.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00113.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00114.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00114.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00115.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00115.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00116.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00116.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00117.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00117.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00118.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00118.png',
   'DATA'),
  ('images\\bone\\front\\LEG_BONE_V3_FRONT_00119.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\front\\LEG_BONE_V3_FRONT_00119.png',
   'DATA'),
  ('images\\bone\\hip-center.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\hip-center.png',
   'DATA'),
  ('images\\bone\\hip-in-flexion.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\hip-in-flexion.png',
   'DATA'),
  ('images\\bone\\lateral-distal-condyle.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\lateral-distal-condyle.png',
   'DATA'),
  ('images\\bone\\lateral-epicondyle.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\lateral-epicondyle.png',
   'DATA'),
  ('images\\bone\\lateral-malleolus.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\lateral-malleolus.png',
   'DATA'),
  ('images\\bone\\lateral-posterior-condyle.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\lateral-posterior-condyle.png',
   'DATA'),
  ('images\\bone\\medial-compartment.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\medial-compartment.png',
   'DATA'),
  ('images\\bone\\medial-distal-condyle.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\medial-distal-condyle.png',
   'DATA'),
  ('images\\bone\\medial-epicondyle.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\medial-epicondyle.png',
   'DATA'),
  ('images\\bone\\medial-malleolus.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\medial-malleolus.png',
   'DATA'),
  ('images\\bone\\medial-posterior-condyle.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\medial-posterior-condyle.png',
   'DATA'),
  ('images\\bone\\register-tibia.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\register-tibia.png',
   'DATA'),
  ('images\\bone\\resect-distal-femur1.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\resect-distal-femur1.png',
   'DATA'),
  ('images\\bone\\resect-distal-femur2.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\resect-distal-femur2.png',
   'DATA'),
  ('images\\bone\\resect-proximal-tibia-img1.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\resect-proximal-tibia-img1.png',
   'DATA'),
  ('images\\bone\\resect-proximal-tibia-img2.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\resect-proximal-tibia-img2.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00000.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00000.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00001.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00001.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00002.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00002.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00003.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00003.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00004.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00004.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00005.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00005.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00006.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00006.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00007.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00007.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00008.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00008.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00009.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00009.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00010.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00010.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00011.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00011.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00012.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00012.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00013.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00013.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00014.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00014.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00015.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00015.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00016.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00016.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00017.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00017.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00018.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00018.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00019.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00019.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00020.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00020.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00021.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00021.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00022.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00022.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00023.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00023.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00024.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00024.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00025.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00025.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00026.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00026.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00027.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00027.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00028.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00028.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00029.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00029.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00030.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00030.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00031.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00031.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00032.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00032.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00033.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00033.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00034.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00034.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00035.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00035.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00036.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00036.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00037.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00037.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00038.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00038.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00039.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00039.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00040.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00040.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00041.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00041.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00042.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00042.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00043.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00043.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00044.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00044.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00045.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00045.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00046.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00046.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00047.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00047.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00048.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00048.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00049.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00049.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00050.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00050.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00051.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00051.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00052.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00052.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00053.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00053.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00054.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00054.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00055.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00055.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00056.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00056.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00057.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00057.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00058.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00058.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00059.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00059.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00060.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00060.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00061.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00061.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00062.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00062.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00063.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00063.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00064.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00064.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00065.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00065.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00066.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00066.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00067.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00067.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00068.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00068.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00069.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00069.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00070.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00070.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00071.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00071.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00072.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00072.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00073.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00073.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00074.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00074.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00075.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00075.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00076.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00076.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00077.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00077.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00078.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00078.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00079.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00079.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00080.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00080.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00081.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00081.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00082.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00082.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00083.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00083.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00084.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00084.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00085.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00085.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00086.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00086.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00087.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00087.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00088.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00088.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00089.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00089.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00090.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00090.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00091.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00091.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00092.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00092.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00093.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00093.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00094.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00094.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00095.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00095.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00096.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00096.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00097.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00097.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00098.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00098.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00099.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00099.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00100.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00100.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00101.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00101.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00102.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00102.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00103.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00103.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00104.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00104.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00105.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00105.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00106.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00106.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00107.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00107.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00108.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00108.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00109.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00109.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00110.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00110.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00111.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00111.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00112.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00112.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00113.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00113.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00114.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00114.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00115.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00115.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00116.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00116.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00117.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00117.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00118.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00118.png',
   'DATA'),
  ('images\\bone\\side\\LEG_BONE_V3_SIDE_00119.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\side\\LEG_BONE_V3_SIDE_00119.png',
   'DATA'),
  ('images\\bone\\tibia-cut-verification1.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\tibia-cut-verification1.png',
   'DATA'),
  ('images\\bone\\tibia-cut-verification2.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\tibia-cut-verification2.png',
   'DATA'),
  ('images\\bone\\tibia-cut-verification3.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\tibia-cut-verification3.png',
   'DATA'),
  ('images\\bone\\tibia-cut-verification4.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\tibia-cut-verification4.png',
   'DATA'),
  ('images\\bone\\tibia-first.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\tibia-first.jpg',
   'DATA'),
  ('images\\bone\\tibial-ap-axis.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\tibial-ap-axis.png',
   'DATA'),
  ('images\\bone\\tkr-Bone-image-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\tkr-Bone-image-1.jpg',
   'DATA'),
  ('images\\bone\\tkr-Bone-image-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\tkr-Bone-image-2.jpg',
   'DATA'),
  ('images\\bone\\tkr-Bone-image-3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\bone\\tkr-Bone-image-3.jpg',
   'DATA'),
  ('images\\calendar.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\calendar.png',
   'DATA'),
  ('images\\camera-video.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\camera-video.png',
   'DATA'),
  ('images\\femur\\ANTERIOR_CHAMFER_G.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\ANTERIOR_CHAMFER_G.jpg',
   'DATA'),
  ('images\\femur\\ANTERIOR_CHAMFER_R.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\ANTERIOR_CHAMFER_R.jpg',
   'DATA'),
  ('images\\femur\\ANTERIOR_CUTS_G.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\ANTERIOR_CUTS_G.jpg',
   'DATA'),
  ('images\\femur\\ANTERIOR_CUTS_R.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\ANTERIOR_CUTS_R.jpg',
   'DATA'),
  ('images\\femur\\POSTERIOR_CHAMFER_G.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\POSTERIOR_CHAMFER_G.jpg',
   'DATA'),
  ('images\\femur\\POSTERIOR_CHAMFER_R.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\POSTERIOR_CHAMFER_R.jpg',
   'DATA'),
  ('images\\femur\\POSTERIOR_CUT_G.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\POSTERIOR_CUT_G.jpg',
   'DATA'),
  ('images\\femur\\POSTERIOR_CUT_R.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\POSTERIOR_CUT_R.jpg',
   'DATA'),
  ('images\\femur\\accp\\InitImage.jpeg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\accp\\InitImage.jpeg',
   'DATA'),
  ('images\\femur\\accp\\PG_7_ME_Green.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\accp\\PG_7_ME_Green.jpg',
   'DATA'),
  ('images\\femur\\accp\\PG_7_ME_Red_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\accp\\PG_7_ME_Red_1.jpg',
   'DATA'),
  ('images\\femur\\accp\\PG_7_ME_Red_10.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\accp\\PG_7_ME_Red_10.jpg',
   'DATA'),
  ('images\\femur\\accp\\PG_7_ME_Red_11.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\accp\\PG_7_ME_Red_11.jpg',
   'DATA'),
  ('images\\femur\\accp\\PG_7_ME_Red_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\accp\\PG_7_ME_Red_2.jpg',
   'DATA'),
  ('images\\femur\\accp\\PG_7_ME_Red_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\accp\\PG_7_ME_Red_3.jpg',
   'DATA'),
  ('images\\femur\\accp\\PG_7_ME_Red_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\accp\\PG_7_ME_Red_4.jpg',
   'DATA'),
  ('images\\femur\\accp\\PG_7_ME_Red_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\accp\\PG_7_ME_Red_5.jpg',
   'DATA'),
  ('images\\femur\\accp\\PG_7_ME_Red_6.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\accp\\PG_7_ME_Red_6.jpg',
   'DATA'),
  ('images\\femur\\accp\\PG_7_ME_Red_7.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\accp\\PG_7_ME_Red_7.jpg',
   'DATA'),
  ('images\\femur\\accp\\PG_7_ME_Red_8.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\accp\\PG_7_ME_Red_8.jpg',
   'DATA'),
  ('images\\femur\\accp\\PG_7_ME_Red_9.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\accp\\PG_7_ME_Red_9.jpg',
   'DATA'),
  ('images\\femur\\anterior-chamfer.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\anterior-chamfer.jpg',
   'DATA'),
  ('images\\femur\\anterior-cut-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\anterior-cut-2.jpg',
   'DATA'),
  ('images\\femur\\anterior-cut.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\anterior-cut.jpg',
   'DATA'),
  ('images\\femur\\distal-femur-cut.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\distal-femur-cut.jpg',
   'DATA'),
  ('images\\femur\\distal-femur-cut_green.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\distal-femur-cut_green.jpg',
   'DATA'),
  ('images\\femur\\distal-femur-cut_red.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\distal-femur-cut_red.jpg',
   'DATA'),
  ('images\\femur\\femur-cut-planning-1.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\femur-cut-planning-1.png',
   'DATA'),
  ('images\\femur\\femur-cut-planning-2.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\femur-cut-planning-2.png',
   'DATA'),
  ('images\\femur\\femur-cut-planning-3.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\femur-cut-planning-3.png',
   'DATA'),
  ('images\\femur\\flexion.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\flexion.jpg',
   'DATA'),
  ('images\\femur\\posterior-chamfer.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\posterior-chamfer.jpg',
   'DATA'),
  ('images\\femur\\posterior-cut.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\posterior-cut.jpg',
   'DATA'),
  ('images\\femur\\tibia-cut-planning-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\tibia-cut-planning-1.jpg',
   'DATA'),
  ('images\\femur\\tibia-cut-planning-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\tibia-cut-planning-2.jpg',
   'DATA'),
  ('images\\femur\\tibia-cut-planning-3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\tibia-cut-planning-3.jpg',
   'DATA'),
  ('images\\femur\\tibia-cut-planning-4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\tibia-cut-planning-4.jpg',
   'DATA'),
  ('images\\femur\\tibia-cut-verification-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\tibia-cut-verification-1.jpg',
   'DATA'),
  ('images\\femur\\tibia-cut-verification-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\tibia-cut-verification-2.jpg',
   'DATA'),
  ('images\\femur\\tibia-cut-verification-3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\tibia-cut-verification-3.jpg',
   'DATA'),
  ('images\\femur\\varus-valgus.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\femur\\varus-valgus.jpg',
   'DATA'),
  ('images\\gallery\\Thr.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\gallery\\Thr.png',
   'DATA'),
  ('images\\gallery\\Tkr.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\gallery\\Tkr.png',
   'DATA'),
  ('images\\gallery\\UKA.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\gallery\\UKA.png',
   'DATA'),
  ('images\\gallery\\hip.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\gallery\\hip.png',
   'DATA'),
  ('images\\gallery\\re-knee.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\gallery\\re-knee.png',
   'DATA'),
  ('images\\hip-bone\\1-register-left-asis.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\1-register-left-asis.jpg',
   'DATA'),
  ('images\\hip-bone\\2-register-right-asis.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\2-register-right-asis.jpg',
   'DATA'),
  ('images\\hip-bone\\3-register-pubic-symphysis.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3-register-pubic-symphysis.jpg',
   'DATA'),
  ('images\\hip-bone\\3D - Copy\\BONE_CUT_TEXTURE-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D - '
   'Copy\\BONE_CUT_TEXTURE-2.jpg',
   'DATA'),
  ('images\\hip-bone\\3D - Copy\\FEMUR_CUT.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D - '
   'Copy\\FEMUR_CUT.mtl',
   'DATA'),
  ('images\\hip-bone\\3D - Copy\\FEMUR_CUT.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D - '
   'Copy\\FEMUR_CUT.obj',
   'DATA'),
  ('images\\hip-bone\\3D - Copy\\FEMUR_FULL.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D - '
   'Copy\\FEMUR_FULL.mtl',
   'DATA'),
  ('images\\hip-bone\\3D - Copy\\FEMUR_FULL.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D - '
   'Copy\\FEMUR_FULL.obj',
   'DATA'),
  ('images\\hip-bone\\3D - Copy\\OBJ_WITH_TEXTURE.rar',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D - '
   'Copy\\OBJ_WITH_TEXTURE.rar',
   'DATA'),
  ('images\\hip-bone\\3D - Copy\\Skeleton_diffuse-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D - '
   'Copy\\Skeleton_diffuse-2.jpg',
   'DATA'),
  ('images\\hip-bone\\3D - Copy\\TIBIA_CUT.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D - '
   'Copy\\TIBIA_CUT.mtl',
   'DATA'),
  ('images\\hip-bone\\3D - Copy\\TIBIA_CUT.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D - '
   'Copy\\TIBIA_CUT.obj',
   'DATA'),
  ('images\\hip-bone\\3D - Copy\\TIBIA_FULL.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D - '
   'Copy\\TIBIA_FULL.mtl',
   'DATA'),
  ('images\\hip-bone\\3D - Copy\\TIBIA_FULL.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D - '
   'Copy\\TIBIA_FULL.obj',
   'DATA'),
  ('images\\hip-bone\\3D1\\Femur Bone.stl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D1\\Femur '
   'Bone.stl',
   'DATA'),
  ('images\\hip-bone\\3D1\\Femur_Bone.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D1\\Femur_Bone.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip.zip',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip.zip',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE 1.stl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE '
   '1.stl',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE 2A.stl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE '
   '2A.stl',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE 2B.stl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE '
   '2B.stl',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE 2C.stl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE '
   '2C.stl',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE 3A.stl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE '
   '3A.stl',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE 3B.stl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE '
   '3B.stl',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE1.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE1.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE1.obj.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE1.obj.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE2A.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE2A.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE2A.obj.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE2A.obj.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE2B.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE2B.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE2C.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE2C.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE2C.obj.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE2C.obj.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE3A.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE3A.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE3A.obj.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE3A.obj.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE3B.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE3B.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE3B.obj.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE3B.obj.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\GRADE_2B.obj.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\GRADE_2B.obj.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\pelvis.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\pelvis.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\pelvis.obj.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\pelvis.obj.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\3Dhip\\pelvis.stl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\3Dhip\\pelvis.stl',
   'DATA'),
  ('images\\hip-bone\\3D\\BONE_CUT_TEXTURE-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\BONE_CUT_TEXTURE-2.jpg',
   'DATA'),
  ('images\\hip-bone\\3D\\BONE_CUT_TEXTURE-2_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\BONE_CUT_TEXTURE-2_1.jpg',
   'DATA'),
  ('images\\hip-bone\\3D\\FEMUR_CUT.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\FEMUR_CUT.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\FEMUR_CUT.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\FEMUR_CUT.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\FEMUR_CUT1.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\FEMUR_CUT1.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\FEMUR_CUT1.obj.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\FEMUR_CUT1.obj.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\FEMUR_CUT2.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\FEMUR_CUT2.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\FEMUR_CUT2.obj.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\FEMUR_CUT2.obj.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\FEMUR_CUT3.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\FEMUR_CUT3.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\FEMUR_CUT3.obj.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\FEMUR_CUT3.obj.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\FEMUR_FULL.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\FEMUR_FULL.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\FEMUR_FULL.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\FEMUR_FULL.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\FEMUR_FULL.obj.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\FEMUR_FULL.obj.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\OBJ_WITH_TEXTURE.rar',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\OBJ_WITH_TEXTURE.rar',
   'DATA'),
  ('images\\hip-bone\\3D\\Skeleton_diffuse-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\Skeleton_diffuse-2.jpg',
   'DATA'),
  ('images\\hip-bone\\3D\\Skeleton_diffuse-2_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\Skeleton_diffuse-2_1.jpg',
   'DATA'),
  ('images\\hip-bone\\3D\\TIBIA_CUT.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\TIBIA_CUT.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\TIBIA_CUT.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\TIBIA_CUT.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\TIBIA_FULL.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\TIBIA_FULL.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\TIBIA_FULL.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\TIBIA_FULL.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\TIBIA_FULL.obj.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\TIBIA_FULL.obj.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\TIBIA_FULL1.obj.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\TIBIA_FULL1.obj.mtl',
   'DATA'),
  ('images\\hip-bone\\3D\\TIBIA_FULL2.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\TIBIA_FULL2.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\snapshot00.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\snapshot00.png',
   'DATA'),
  ('images\\hip-bone\\3D\\test.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\test.obj',
   'DATA'),
  ('images\\hip-bone\\3D\\tibiaF.mlp',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\tibiaF.mlp',
   'DATA'),
  ('images\\hip-bone\\3D\\transformed_points.ply',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\3D\\transformed_points.ply',
   'DATA'),
  ('images\\hip-bone\\Acetabularcup.stl.glb',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Acetabularcup.stl.glb',
   'DATA'),
  ('images\\hip-bone\\FEMUR_CUT.stl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\FEMUR_CUT.stl',
   'DATA'),
  ('images\\hip-bone\\Femur_Registratio.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Femur_Registratio.jpg',
   'DATA'),
  ('images\\hip-bone\\Final_Cup_Position-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Final_Cup_Position-1.jpg',
   'DATA'),
  ('images\\hip-bone\\Final_Cup_Position-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Final_Cup_Position-2.jpg',
   'DATA'),
  ('images\\hip-bone\\Final_Cup_Position-2_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Final_Cup_Position-2_1.jpg',
   'DATA'),
  ('images\\hip-bone\\HIP.obj',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\HIP.obj',
   'DATA'),
  ('images\\hip-bone\\HIP.obj.mtl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\HIP.obj.mtl',
   'DATA'),
  ('images\\hip-bone\\HIPS.stl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\HIPS.stl',
   'DATA'),
  ('images\\hip-bone\\HIP_7.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\HIP_7.png',
   'DATA'),
  ('images\\hip-bone\\Joystick.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Joystick.jpg',
   'DATA'),
  ('images\\hip-bone\\Joystick.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Joystick.png',
   'DATA'),
  ('images\\hip-bone\\PAPROSKY WOTH TREATMENT.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\PAPROSKY '
   'WOTH TREATMENT.png',
   'DATA'),
  ('images\\hip-bone\\Re-Register_Acetabulum_0.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Re-Register_Acetabulum_0.jpg',
   'DATA'),
  ('images\\hip-bone\\Re-Register_Acetabulum_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Re-Register_Acetabulum_1.jpg',
   'DATA'),
  ('images\\hip-bone\\Re-Register_Acetabulum_1_G.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Re-Register_Acetabulum_1_G.jpg',
   'DATA'),
  ('images\\hip-bone\\Re-Register_Acetabulum_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Re-Register_Acetabulum_2.jpg',
   'DATA'),
  ('images\\hip-bone\\Re-Register_Acetabulum_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Re-Register_Acetabulum_3.jpg',
   'DATA'),
  ('images\\hip-bone\\Re-Register_Acetabulum_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Re-Register_Acetabulum_4.jpg',
   'DATA'),
  ('images\\hip-bone\\Re-Register_Acetabulum_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Re-Register_Acetabulum_5.jpg',
   'DATA'),
  ('images\\hip-bone\\Register_Acetabulum_0.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Register_Acetabulum_0.jpg',
   'DATA'),
  ('images\\hip-bone\\Register_Acetabulum_1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Register_Acetabulum_1.jpg',
   'DATA'),
  ('images\\hip-bone\\Register_Acetabulum_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Register_Acetabulum_2.jpg',
   'DATA'),
  ('images\\hip-bone\\Register_Acetabulum_3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Register_Acetabulum_3.jpg',
   'DATA'),
  ('images\\hip-bone\\Register_Acetabulum_4.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Register_Acetabulum_4.jpg',
   'DATA'),
  ('images\\hip-bone\\Register_Acetabulum_5.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Register_Acetabulum_5.jpg',
   'DATA'),
  ('images\\hip-bone\\Register_Acetabulum_6.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\Register_Acetabulum_6.jpg',
   'DATA'),
  ('images\\hip-bone\\TIBIA_CUT.stl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\TIBIA_CUT.stl',
   'DATA'),
  ('images\\hip-bone\\acetabulum-component-placement.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\acetabulum-component-placement.png',
   'DATA'),
  ('images\\hip-bone\\anterior-pelvic-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\anterior-pelvic-1.jpg',
   'DATA'),
  ('images\\hip-bone\\anterior-pelvic-1.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\anterior-pelvic-1.png',
   'DATA'),
  ('images\\hip-bone\\anterior-pelvic-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\anterior-pelvic-2.jpg',
   'DATA'),
  ('images\\hip-bone\\anterior-pelvic-2.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\anterior-pelvic-2.png',
   'DATA'),
  ('images\\hip-bone\\anterior-pelvic-3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\anterior-pelvic-3.jpg',
   'DATA'),
  ('images\\hip-bone\\camp.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\camp.png',
   'DATA'),
  ('images\\hip-bone\\cup-position.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\cup-position.png',
   'DATA'),
  ('images\\hip-bone\\femur-registration.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\femur-registration.png',
   'DATA'),
  ('images\\hip-bone\\hip-scr.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\hip-scr.png',
   'DATA'),
  ('images\\hip-bone\\hip-scr45.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\hip-scr45.png',
   'DATA'),
  ('images\\hip-bone\\hipfinalImage.jpeg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\hipfinalImage.jpeg',
   'DATA'),
  ('images\\hip-bone\\hipfinalImage.jpg.jpeg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\hipfinalImage.jpg.jpeg',
   'DATA'),
  ('images\\hip-bone\\latera.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\latera.png',
   'DATA'),
  ('images\\hip-bone\\leg-length-assessment.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\leg-length-assessment.jpg',
   'DATA'),
  ('images\\hip-bone\\leg-length-assessment.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\leg-length-assessment.png',
   'DATA'),
  ('images\\hip-bone\\mid-axial-body-plane.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\mid-axial-body-plane.png',
   'DATA'),
  ('images\\hip-bone\\output.ply',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\output.ply',
   'DATA'),
  ('images\\hip-bone\\pelvis-registration-1.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\pelvis-registration-1.png',
   'DATA'),
  ('images\\hip-bone\\pelvis-registration-2.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\pelvis-registration-2.png',
   'DATA'),
  ('images\\hip-bone\\pelvis-registration-DQRF.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\pelvis-registration-DQRF.jpg',
   'DATA'),
  ('images\\hip-bone\\reamer.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\reamer.jpg',
   'DATA'),
  ('images\\hip-bone\\reamer1.JPG',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\reamer1.JPG',
   'DATA'),
  ('images\\hip-bone\\register-acetabulum-1.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\register-acetabulum-1.png',
   'DATA'),
  ('images\\hip-bone\\register-acetabulum-2.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\register-acetabulum-2.png',
   'DATA'),
  ('images\\hip-bone\\register-acetabulum-3.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\register-acetabulum-3.png',
   'DATA'),
  ('images\\hip-bone\\register-acetabulum-4.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\register-acetabulum-4.png',
   'DATA'),
  ('images\\hip-bone\\register-acetabulum-a.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\register-acetabulum-a.png',
   'DATA'),
  ('images\\hip-bone\\register-acetabulum.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\register-acetabulum.png',
   'DATA'),
  ('images\\hip-bone\\remure_position.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\remure_position.jpg',
   'DATA'),
  ('images\\hip-bone\\remure_position1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\remure_position1.jpg',
   'DATA'),
  ('images\\hip-bone\\result\\PAPROSKY_2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\result\\PAPROSKY_2.jpg',
   'DATA'),
  ('images\\hip-bone\\revision\\ASIS.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\revision\\ASIS.jpg',
   'DATA'),
  ('images\\hip-bone\\revision\\IT.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\revision\\IT.jpg',
   'DATA'),
  ('images\\hip-bone\\revision\\PS.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\revision\\PS.jpg',
   'DATA'),
  ('images\\hip-bone\\revision\\PSIS.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\revision\\PSIS.jpg',
   'DATA'),
  ('images\\hip-bone\\revision\\TAL.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\revision\\TAL.jpg',
   'DATA'),
  ('images\\hip-bone\\revision\\Tf.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\revision\\Tf.jpg',
   'DATA'),
  ('images\\hip-bone\\revision\\image.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\revision\\image.jpg',
   'DATA'),
  ('images\\hip-bone\\statement0.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\statement0.jpg',
   'DATA'),
  ('images\\hip-bone\\statement1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\statement1.jpg',
   'DATA'),
  ('images\\hip-bone\\statement2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\statement2.jpg',
   'DATA'),
  ('images\\hip-bone\\supine.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\supine.png',
   'DATA'),
  ('images\\hip-bone\\test.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\test.jpg',
   'DATA'),
  ('images\\hip-bone\\updated_mesh.ply',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\updated_mesh.ply',
   'DATA'),
  ('images\\hip-bone\\updated_model.stl',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\hip-bone\\updated_model.stl',
   'DATA'),
  ('images\\home.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\home.png',
   'DATA'),
  ('images\\icon\\check.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\icon\\check.png',
   'DATA'),
  ('images\\icon\\cross.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\icon\\cross.png',
   'DATA'),
  ('images\\icon\\icon-1.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\icon\\icon-1.png',
   'DATA'),
  ('images\\icon\\icon-2.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\icon\\icon-2.png',
   'DATA'),
  ('images\\icon\\icon-3.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\icon\\icon-3.png',
   'DATA'),
  ('images\\icon\\icon-4.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\icon\\icon-4.png',
   'DATA'),
  ('images\\icon\\minus-button.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\icon\\minus-button.png',
   'DATA'),
  ('images\\icon\\minus.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\icon\\minus.png',
   'DATA'),
  ('images\\icon\\plus-button.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\icon\\plus-button.png',
   'DATA'),
  ('images\\icon\\plus.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\icon\\plus.png',
   'DATA'),
  ('images\\icon\\repeat.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\icon\\repeat.png',
   'DATA'),
  ('images\\icon\\select.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\icon\\select.png',
   'DATA'),
  ('images\\knee-bone\\femur-points\\1-ac.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\femur-points\\1-ac.jpg',
   'DATA'),
  ('images\\knee-bone\\femur-points\\1-hip-in-flexion(hif).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\femur-points\\1-hip-in-flexion(hif).jpg',
   'DATA'),
  ('images\\knee-bone\\femur-points\\10-lateral-posterior-condyle(lpc).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\femur-points\\10-lateral-posterior-condyle(lpc).jpg',
   'DATA'),
  ('images\\knee-bone\\femur-points\\2-hip-centre(hc).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\femur-points\\2-hip-centre(hc).jpg',
   'DATA'),
  ('images\\knee-bone\\femur-points\\3-medial-epicondyle(me).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\femur-points\\3-medial-epicondyle(me).jpg',
   'DATA'),
  ('images\\knee-bone\\femur-points\\4-lateral-epicondyle(le).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\femur-points\\4-lateral-epicondyle(le).jpg',
   'DATA'),
  ('images\\knee-bone\\femur-points\\5-femur-center(fc).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\femur-points\\5-femur-center(fc).jpg',
   'DATA'),
  ('images\\knee-bone\\femur-points\\6-femur-ap-axis(fap).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\femur-points\\6-femur-ap-axis(fap).jpg',
   'DATA'),
  ('images\\knee-bone\\femur-points\\7-medial-distal-condyle(mdc).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\femur-points\\7-medial-distal-condyle(mdc).jpg',
   'DATA'),
  ('images\\knee-bone\\femur-points\\8-lateral-distal-condyle(ldc).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\femur-points\\8-lateral-distal-condyle(ldc).jpg',
   'DATA'),
  ('images\\knee-bone\\femur-points\\9-medial-posteror-condylr(mpc).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\femur-points\\9-medial-posteror-condylr(mpc).jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer002.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer002.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer005.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer005.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer008.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer008.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer011.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer011.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer014.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer014.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer017.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer017.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer020.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer020.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer023.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer023.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer026.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer026.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer029.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer029.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer032.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer032.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer035.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer035.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer038.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer038.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer041.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer041.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer044.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer044.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer047.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer047.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer050.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer050.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer053.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer053.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer056.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer056.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer059.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer059.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer062.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer062.jpg',
   'DATA'),
  ('images\\knee-bone\\sequence\\Buffer074.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\sequence\\Buffer074.jpg',
   'DATA'),
  ('images\\knee-bone\\tibia-points\\1-tibia-centre(tc).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\tibia-points\\1-tibia-centre(tc).jpg',
   'DATA'),
  ('images\\knee-bone\\tibia-points\\2-tibial-ap-axis(tap).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\tibia-points\\2-tibial-ap-axis(tap).jpg',
   'DATA'),
  ('images\\knee-bone\\tibia-points\\3-medial-compartment(mc).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\tibia-points\\3-medial-compartment(mc).jpg',
   'DATA'),
  ('images\\knee-bone\\tibia-points\\4-lateral-compartment(lc).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\tibia-points\\4-lateral-compartment(lc).jpg',
   'DATA'),
  ('images\\knee-bone\\tibia-points\\5-medial-malleolus(mm).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\tibia-points\\5-medial-malleolus(mm).jpg',
   'DATA'),
  ('images\\knee-bone\\tibia-points\\6-lateral-malleolus(lm).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\tibia-points\\6-lateral-malleolus(lm).jpg',
   'DATA'),
  ('images\\knee-bone\\tibia-points\\7-ankle-center(anc).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\knee-bone\\tibia-points\\7-ankle-center(anc).jpg',
   'DATA'),
  ('images\\left-arrow.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\left-arrow.png',
   'DATA'),
  ('images\\profile.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\profile.png',
   'DATA'),
  ('images\\revision-tkr\\balance-in-extensiion\\femur_cut_verify_front.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\balance-in-extensiion\\femur_cut_verify_front.jpg',
   'DATA'),
  ('images\\revision-tkr\\balance-in-flexion\\balance-in-flexion.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\balance-in-flexion\\balance-in-flexion.jpg',
   'DATA'),
  ('images\\revision-tkr\\classification\\image1.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\classification\\image1.png',
   'DATA'),
  ('images\\revision-tkr\\classification\\image2.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\classification\\image2.png',
   'DATA'),
  ('images\\revision-tkr\\femur-im-canal-reaam\\femur-im-canal-reaam.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\femur-im-canal-reaam\\femur-im-canal-reaam.jpg',
   'DATA'),
  ('images\\revision-tkr\\femur-points\\1-HIF.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\femur-points\\1-HIF.jpg',
   'DATA'),
  ('images\\revision-tkr\\femur-points\\10-LPC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\femur-points\\10-LPC.jpg',
   'DATA'),
  ('images\\revision-tkr\\femur-points\\11-AC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\femur-points\\11-AC.jpg',
   'DATA'),
  ('images\\revision-tkr\\femur-points\\2-HC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\femur-points\\2-HC.jpg',
   'DATA'),
  ('images\\revision-tkr\\femur-points\\3-ME.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\femur-points\\3-ME.jpg',
   'DATA'),
  ('images\\revision-tkr\\femur-points\\4-LE.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\femur-points\\4-LE.jpg',
   'DATA'),
  ('images\\revision-tkr\\femur-points\\5-FC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\femur-points\\5-FC.jpg',
   'DATA'),
  ('images\\revision-tkr\\femur-points\\6-FAP.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\femur-points\\6-FAP.jpg',
   'DATA'),
  ('images\\revision-tkr\\femur-points\\7-MDC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\femur-points\\7-MDC.jpg',
   'DATA'),
  ('images\\revision-tkr\\femur-points\\8-LDC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\femur-points\\8-LDC.jpg',
   'DATA'),
  ('images\\revision-tkr\\femur-points\\9-MPC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\femur-points\\9-MPC.jpg',
   'DATA'),
  ('images\\revision-tkr\\guidance-distal-femur-cut\\leg_bone_v2_front__00000.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\guidance-distal-femur-cut\\leg_bone_v2_front__00000.jpg',
   'DATA'),
  ('images\\revision-tkr\\guidance-distal-femur-cut\\leg_bone_v2_side__00072.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\guidance-distal-femur-cut\\leg_bone_v2_side__00072.jpg',
   'DATA'),
  ('images\\revision-tkr\\guidance-proximal-tibia-cut\\tibia_cut_guidance_valgus.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\guidance-proximal-tibia-cut\\tibia_cut_guidance_valgus.jpg',
   'DATA'),
  ('images\\revision-tkr\\guidance-proximal-tibia-cut\\tibia_cut_guidance_valgus_slope.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\guidance-proximal-tibia-cut\\tibia_cut_guidance_valgus_slope.jpg',
   'DATA'),
  ('images\\revision-tkr\\guidance-proximal-tibia-cut\\tibia_cut_guidance_varus.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\guidance-proximal-tibia-cut\\tibia_cut_guidance_varus.jpg',
   'DATA'),
  ('images\\revision-tkr\\ml-size-acquisition\\ml-size-acquisition.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\ml-size-acquisition\\ml-size-acquisition.jpg',
   'DATA'),
  ('images\\revision-tkr\\planning-distal-femur-cut\\femur_cut_plan1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\planning-distal-femur-cut\\femur_cut_plan1.jpg',
   'DATA'),
  ('images\\revision-tkr\\planning-distal-femur-cut\\femur_cut_planning_front.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\planning-distal-femur-cut\\femur_cut_planning_front.jpg',
   'DATA'),
  ('images\\revision-tkr\\planning-distal-femur-cut\\femur_cut_planning_side-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\planning-distal-femur-cut\\femur_cut_planning_side-1.jpg',
   'DATA'),
  ('images\\revision-tkr\\planning-distal-femur-cut\\femur_cut_planning_side.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\planning-distal-femur-cut\\femur_cut_planning_side.jpg',
   'DATA'),
  ('images\\revision-tkr\\planning-proximal-tibia-cut\\TIBIA_CUT_PLANNING_FULL_FRONT_IM_CANAL.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\planning-proximal-tibia-cut\\TIBIA_CUT_PLANNING_FULL_FRONT_IM_CANAL.jpg',
   'DATA'),
  ('images\\revision-tkr\\planning-proximal-tibia-cut\\TIBIA_CUT_PLANNING_FULL_FRONT_MECHENICAL_AXIS.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\planning-proximal-tibia-cut\\TIBIA_CUT_PLANNING_FULL_FRONT_MECHENICAL_AXIS.jpg',
   'DATA'),
  ('images\\revision-tkr\\planning-proximal-tibia-cut\\TIBIA_CUT_PLANNING_SIDE.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\planning-proximal-tibia-cut\\TIBIA_CUT_PLANNING_SIDE.jpg',
   'DATA'),
  ('images\\revision-tkr\\planning-proximal-tibia-cut\\refernce image.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\planning-proximal-tibia-cut\\refernce '
   'image.png',
   'DATA'),
  ('images\\revision-tkr\\planning-proximal-tibia-cut\\tc.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\planning-proximal-tibia-cut\\tc.jpg',
   'DATA'),
  ('images\\revision-tkr\\remove-primary-implants\\femur_component_removal.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\remove-primary-implants\\femur_component_removal.jpg',
   'DATA'),
  ('images\\revision-tkr\\remove-primary-implants\\tibia_component_removal.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\remove-primary-implants\\tibia_component_removal.jpg',
   'DATA'),
  ('images\\revision-tkr\\tibia-im-canal-ream\\tibia-im-cana- ream.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\tibia-im-canal-ream\\tibia-im-cana- '
   'ream.jpg',
   'DATA'),
  ('images\\revision-tkr\\tibia-points\\1-TC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\tibia-points\\1-TC.jpg',
   'DATA'),
  ('images\\revision-tkr\\tibia-points\\2-TIBIA-AP-AXIS(TAP).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\tibia-points\\2-TIBIA-AP-AXIS(TAP).jpg',
   'DATA'),
  ('images\\revision-tkr\\tibia-points\\3-MC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\tibia-points\\3-MC.jpg',
   'DATA'),
  ('images\\revision-tkr\\tibia-points\\4-LC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\tibia-points\\4-LC.jpg',
   'DATA'),
  ('images\\revision-tkr\\tibia-points\\5-MEDIAL-MALLEOLUS(MM).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\tibia-points\\5-MEDIAL-MALLEOLUS(MM).jpg',
   'DATA'),
  ('images\\revision-tkr\\tibia-points\\6-LATERAL-MALLEOLUS(LM).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\tibia-points\\6-LATERAL-MALLEOLUS(LM).jpg',
   'DATA'),
  ('images\\revision-tkr\\tibia-points\\7-ANKLE-CENTRE(ANC).jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\tibia-points\\7-ANKLE-CENTRE(ANC).jpg',
   'DATA'),
  ('images\\revision-tkr\\validate-points\\actual-image-to-be-used-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\validate-points\\actual-image-to-be-used-1.jpg',
   'DATA'),
  ('images\\revision-tkr\\validate-points\\actual-image-to-be-used-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\validate-points\\actual-image-to-be-used-2.jpg',
   'DATA'),
  ('images\\revision-tkr\\validate-points\\actual-image-to-be-used-3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\validate-points\\actual-image-to-be-used-3.jpg',
   'DATA'),
  ('images\\revision-tkr\\verification-distal-femur-cut\\flexion.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\verification-distal-femur-cut\\flexion.jpg',
   'DATA'),
  ('images\\revision-tkr\\verification-distal-femur-cut\\varus-valgus.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\verification-distal-femur-cut\\varus-valgus.jpg',
   'DATA'),
  ('images\\revision-tkr\\verification-proximal-tibia-cut\\tibia-cut-verification-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\verification-proximal-tibia-cut\\tibia-cut-verification-1.jpg',
   'DATA'),
  ('images\\revision-tkr\\verification-proximal-tibia-cut\\tibia-cut-verification-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\verification-proximal-tibia-cut\\tibia-cut-verification-2.jpg',
   'DATA'),
  ('images\\revision-tkr\\verification-proximal-tibia-cut\\tibia-cut-verification-3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\revision-tkr\\verification-proximal-tibia-cut\\tibia-cut-verification-3.jpg',
   'DATA'),
  ('images\\right-arrow.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\right-arrow.png',
   'DATA'),
  ('images\\robo\\C_045449.JPG',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\robo\\C_045449.JPG',
   'DATA'),
  ('images\\robo\\D_045457.JPG',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\robo\\D_045457.JPG',
   'DATA'),
  ('images\\robo\\robo_position.JPG',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\robo\\robo_position.JPG',
   'DATA'),
  ('images\\robo\\robo_position_updated.jpeg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\robo\\robo_position_updated.jpeg',
   'DATA'),
  ('images\\robo\\robopositioncheck.mp4',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\robo\\robopositioncheck.mp4',
   'DATA'),
  ('images\\robo\\robopositioncheck1.mp4',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\robo\\robopositioncheck1.mp4',
   'DATA'),
  ('images\\robo\\robot_intro.jpeg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\robo\\robot_intro.jpeg',
   'DATA'),
  ('images\\setting-bottom.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\setting-bottom.png',
   'DATA'),
  ('images\\settings.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\settings.png',
   'DATA'),
  ('images\\tibia\\FLAT_TIBIA_G1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\FLAT_TIBIA_G1.jpg',
   'DATA'),
  ('images\\tibia\\FLAT_TIBIA_G2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\FLAT_TIBIA_G2.jpg',
   'DATA'),
  ('images\\tibia\\FLAT_TIBIA_R1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\FLAT_TIBIA_R1.jpg',
   'DATA'),
  ('images\\tibia\\FLAT_TIBIA_R2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\FLAT_TIBIA_R2.jpg',
   'DATA'),
  ('images\\tibia\\anterior-chamfer.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\anterior-chamfer.jpg',
   'DATA'),
  ('images\\tibia\\anterior-cut-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\anterior-cut-2.jpg',
   'DATA'),
  ('images\\tibia\\anterior-cut.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\anterior-cut.jpg',
   'DATA'),
  ('images\\tibia\\distal-femur-cut.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\distal-femur-cut.jpg',
   'DATA'),
  ('images\\tibia\\femur-cut-planning-1.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\femur-cut-planning-1.png',
   'DATA'),
  ('images\\tibia\\femur-cut-planning-2.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\femur-cut-planning-2.png',
   'DATA'),
  ('images\\tibia\\femur-cut-planning-3.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\femur-cut-planning-3.png',
   'DATA'),
  ('images\\tibia\\flexion.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\flexion.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00000.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00000.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00001.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00001.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00002.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00002.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00003.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00003.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00004.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00004.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00005.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00005.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00006.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00006.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00007.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00007.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00008.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00008.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00009.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00009.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00010.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00010.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00011.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00011.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00012.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00012.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00013.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00013.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00014.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00014.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00015.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00015.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00016.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00016.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00017.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00017.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00018.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00018.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00019.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00019.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00020.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00020.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00021.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00021.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00022.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00022.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00023.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00023.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00024.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00024.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00025.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00025.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00026.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00026.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00027.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00027.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00028.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00028.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00029.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00029.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00030.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00030.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00031.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00031.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00032.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00032.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00033.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00033.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00034.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00034.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00035.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00035.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00036.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00036.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00037.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00037.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00038.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00038.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00039.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00039.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00040.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00040.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00041.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00041.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00042.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00042.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00043.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00043.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00044.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00044.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00045.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00045.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00046.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00046.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00047.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00047.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00048.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00048.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00049.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00049.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00050.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00050.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00051.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00051.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00052.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00052.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00053.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00053.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00054.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00054.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00055.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00055.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00056.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00056.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00057.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00057.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00058.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00058.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00059.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00059.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00060.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00060.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00061.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00061.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00062.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00062.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00063.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00063.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00064.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00064.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00065.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00065.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00066.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00066.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00067.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00067.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00068.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00068.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00069.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00069.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00070.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00070.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00071.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00071.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00072.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00072.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00073.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00073.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00074.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00074.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00075.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00075.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00076.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00076.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00077.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00077.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00078.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00078.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00079.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00079.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00080.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00080.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00081.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00081.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00082.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00082.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00083.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00083.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00084.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00084.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00085.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00085.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00086.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00086.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00087.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00087.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00088.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00088.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00089.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00089.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00090.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00090.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00091.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00091.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00092.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00092.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00093.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00093.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00094.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00094.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00095.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00095.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00096.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00096.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00097.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00097.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00098.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00098.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00099.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00099.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00100.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00100.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00101.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00101.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00102.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00102.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00103.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00103.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00104.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00104.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00105.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00105.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00106.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00106.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00107.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00107.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00108.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00108.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00109.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00109.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00110.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00110.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00111.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00111.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00112.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00112.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00113.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00113.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00114.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00114.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00115.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00115.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00116.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00116.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00117.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00117.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00118.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00118.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00119.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\front\\LEG_BONE_V2_FRONT__00119.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00000.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00000.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00001.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00001.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00002.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00002.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00003.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00003.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00004.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00004.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00005.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00005.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00006.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00006.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00007.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00007.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00008.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00008.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00009.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00009.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00010.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00010.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00011.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00011.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00012.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00012.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00013.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00013.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00014.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00014.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00015.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00015.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00016.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00016.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00017.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00017.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00018.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00018.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00019.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00019.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00020.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00020.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00021.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00021.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00022.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00022.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00023.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00023.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00024.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00024.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00025.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00025.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00026.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00026.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00027.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00027.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00028.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00028.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00029.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00029.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00030.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00030.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00031.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00031.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00032.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00032.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00033.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00033.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00034.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00034.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00035.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00035.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00036.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00036.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00037.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00037.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00038.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00038.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00039.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00039.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00040.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00040.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00041.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00041.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00042.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00042.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00043.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00043.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00044.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00044.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00045.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00045.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00046.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00046.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00047.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00047.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00048.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00048.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00049.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00049.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00050.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00050.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00051.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00051.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00052.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00052.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00053.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00053.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00054.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00054.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00055.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00055.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00056.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00056.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00057.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00057.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00058.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00058.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00059.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00059.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00060.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00060.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00061.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00061.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00062.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00062.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00063.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00063.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00064.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00064.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00065.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00065.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00066.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00066.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00067.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00067.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00068.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00068.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00069.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00069.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00070.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00070.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00071.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00071.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00072.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00072.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00073.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00073.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00074.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00074.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00075.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00075.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00076.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00076.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00077.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00077.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00078.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00078.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00079.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00079.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00080.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00080.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00081.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00081.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00082.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00082.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00083.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00083.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00084.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00084.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00085.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00085.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00086.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00086.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00087.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00087.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00088.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00088.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00089.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00089.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00090.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00090.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00091.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00091.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00092.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00092.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00093.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00093.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00094.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00094.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00095.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00095.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00096.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00096.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00097.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00097.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00098.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00098.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00099.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00099.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00100.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00100.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00101.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00101.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00102.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00102.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00103.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00103.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00104.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00104.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00105.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00105.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00106.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00106.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00107.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00107.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00108.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00108.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00109.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00109.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00110.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00110.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00111.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00111.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00112.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00112.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00113.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00113.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00114.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00114.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00115.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00115.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00116.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00116.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00117.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00117.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00118.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00118.jpg',
   'DATA'),
  ('images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00119.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\post-op-kinematics\\side\\LEG_BONE_V2_SIDE__00119.jpg',
   'DATA'),
  ('images\\tibia\\posterior-chamfer.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\posterior-chamfer.jpg',
   'DATA'),
  ('images\\tibia\\posterior-cut.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\posterior-cut.jpg',
   'DATA'),
  ('images\\tibia\\tibia - Shortcut.lnk',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\tibia - '
   'Shortcut.lnk',
   'DATA'),
  ('images\\tibia\\tibia-cut-planning-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\tibia-cut-planning-1.jpg',
   'DATA'),
  ('images\\tibia\\tibia-cut-planning-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\tibia-cut-planning-2.jpg',
   'DATA'),
  ('images\\tibia\\tibia-cut-planning-3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\tibia-cut-planning-3.jpg',
   'DATA'),
  ('images\\tibia\\tibia-cut-verification-1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\tibia-cut-verification-1.jpg',
   'DATA'),
  ('images\\tibia\\tibia-cut-verification-2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\tibia-cut-verification-2.jpg',
   'DATA'),
  ('images\\tibia\\tibia-cut-verification-3.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\tibia-cut-verification-3.jpg',
   'DATA'),
  ('images\\tibia\\varus-valgus.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\tibia\\varus-valgus.jpg',
   'DATA'),
  ('images\\time.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\time.png',
   'DATA'),
  ('images\\uni-knee\\FC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FC.jpg',
   'DATA'),
  ('images\\uni-knee\\FC_TM.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FC_TM.jpg',
   'DATA'),
  ('images\\uni-knee\\FC_TS1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FC_TS1.jpg',
   'DATA'),
  ('images\\uni-knee\\FC_TS2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FC_TS2.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00000.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00000.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00001.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00001.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00002.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00002.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00003.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00003.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00004.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00004.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00005.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00005.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00006.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00006.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00007.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00007.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00008.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00008.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00009.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00009.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00010.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00010.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00011.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00011.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00012.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00012.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00013.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00013.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00014.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00014.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00015.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00015.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00016.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00016.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00017.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00017.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00018.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00018.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00019.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00019.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00020.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00020.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00021.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00021.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00022.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00022.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00023.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00023.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00024.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00024.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00025.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00025.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00026.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00026.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00027.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00027.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00028.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00028.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00029.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00029.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00030.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00030.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00031.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00031.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00032.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00032.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00033.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00033.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00034.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00034.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00035.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00035.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00036.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00036.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00037.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00037.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00038.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00038.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00039.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00039.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00040.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00040.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00041.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00041.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00042.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00042.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00043.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00043.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00044.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00044.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00045.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00045.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00046.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00046.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00047.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00047.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00048.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00048.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00049.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00049.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00050.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00050.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00051.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00051.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00052.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00052.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00053.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00053.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00054.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00054.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00055.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00055.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00056.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00056.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00057.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00057.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00058.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00058.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00059.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00059.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00060.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00060.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00061.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00061.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00062.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00062.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00063.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00063.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00064.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00064.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00065.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00065.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00066.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00066.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00067.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00067.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00068.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00068.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00069.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00069.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00070.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00070.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00071.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00071.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00072.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00072.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00073.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00073.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00074.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00074.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00075.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00075.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00076.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00076.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00077.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00077.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00078.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00078.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00079.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00079.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00080.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00080.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00081.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00081.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00082.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00082.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00083.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00083.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00084.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00084.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00085.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00085.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00086.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00086.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00087.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00087.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00088.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00088.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00089.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00089.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00090.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00090.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00091.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00091.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00092.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00092.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00093.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00093.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00094.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00094.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00095.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00095.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00096.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00096.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00097.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00097.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00098.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00098.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00099.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00099.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00100.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00100.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00101.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00101.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00102.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00102.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00103.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00103.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00104.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00104.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00105.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00105.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00106.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00106.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00107.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00107.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00108.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00108.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00109.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00109.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00110.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00110.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00111.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00111.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00112.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00112.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00113.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00113.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00114.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00114.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00115.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00115.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00116.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00116.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00117.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00117.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00118.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00118.jpg',
   'DATA'),
  ('images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00119.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\FRONT\\PG-09_FC_FRONT_00119.jpg',
   'DATA'),
  ('images\\uni-knee\\MC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\MC.jpg',
   'DATA'),
  ('images\\uni-knee\\MDC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\MDC.jpg',
   'DATA'),
  ('images\\uni-knee\\ME.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\ME.jpg',
   'DATA'),
  ('images\\uni-knee\\MPC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\MPC.jpg',
   'DATA'),
  ('images\\uni-knee\\PARTIAL_KNEE_120_FRAMES.rar',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\PARTIAL_KNEE_120_FRAMES.rar',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00000.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00000.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00001.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00001.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00002.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00002.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00003.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00003.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00004.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00004.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00005.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00005.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00006.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00006.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00007.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00007.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00008.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00008.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00009.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00009.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00010.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00010.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00011.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00011.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00012.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00012.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00013.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00013.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00014.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00014.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00015.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00015.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00016.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00016.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00017.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00017.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00018.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00018.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00019.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00019.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00020.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00020.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00021.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00021.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00022.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00022.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00023.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00023.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00024.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00024.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00025.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00025.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00026.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00026.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00027.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00027.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00028.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00028.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00029.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00029.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00030.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00030.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00031.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00031.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00032.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00032.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00033.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00033.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00034.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00034.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00035.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00035.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00036.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00036.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00037.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00037.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00038.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00038.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00039.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00039.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00040.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00040.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00041.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00041.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00042.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00042.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00043.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00043.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00044.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00044.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00045.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00045.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00046.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00046.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00047.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00047.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00048.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00048.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00049.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00049.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00050.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00050.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00051.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00051.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00052.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00052.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00053.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00053.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00054.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00054.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00055.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00055.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00056.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00056.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00057.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00057.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00058.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00058.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00059.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00059.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00060.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00060.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00061.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00061.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00062.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00062.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00063.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00063.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00064.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00064.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00065.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00065.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00066.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00066.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00067.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00067.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00068.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00068.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00069.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00069.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00070.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00070.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00071.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00071.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00072.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00072.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00073.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00073.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00074.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00074.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00075.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00075.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00076.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00076.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00077.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00077.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00078.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00078.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00079.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00079.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00080.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00080.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00081.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00081.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00082.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00082.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00083.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00083.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00084.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00084.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00085.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00085.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00086.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00086.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00087.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00087.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00088.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00088.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00089.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00089.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00090.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00090.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00091.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00091.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00092.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00092.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00093.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00093.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00094.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00094.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00095.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00095.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00096.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00096.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00097.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00097.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00098.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00098.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00099.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00099.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00100.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00100.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00101.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00101.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00102.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00102.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00103.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00103.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00104.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00104.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00105.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00105.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00106.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00106.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00107.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00107.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00108.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00108.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00109.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00109.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00110.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00110.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00111.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00111.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00112.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00112.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00113.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00113.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00114.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00114.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00115.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00115.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00116.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00116.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00117.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00117.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00118.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00118.jpg',
   'DATA'),
  ('images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00119.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\SIDE\\PG-09_FC_SIDE__00119.jpg',
   'DATA'),
  ('images\\uni-knee\\TC.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\TC.jpg',
   'DATA'),
  ('images\\uni-knee\\TC_AL1.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\TC_AL1.jpg',
   'DATA'),
  ('images\\uni-knee\\TC_AL2.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\TC_AL2.jpg',
   'DATA'),
  ('images\\uni-knee\\TC_MP.jpg',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\uni-knee\\TC_MP.jpg',
   'DATA'),
  ('images\\union.png',
   'D:\\SpineSurgery\\pythonProject\\app\\static\\images\\union.png',
   'DATA'),
  ('base_library.zip',
   'D:\\SpineSurgery\\pythonProject\\app\\templates\\build\\file\\base_library.zip',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\SpineSurgery\\pythonProject\\venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA')],
 'python311.dll',
 False,
 True,
 False,
 [],
 None,
 None,
 None)
