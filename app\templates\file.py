import time

from PyQt5.QtCore import Qt, QTimer, QTime, QSize
from PyQt5.QtGui import QPixmap, QTransform, QIcon
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QGridLayout, QSizePolicy
)
import os.path
import pickle
import queue
import sys
import numpy as np
from scipy.optimize import least_squares
from pythonProject.general.camera.CameraParams_header import MV_FRAME_OUT_INFO_EX
from pythonProject.general.camera.MvCameraControl_class import *
from pythonProject.general.camera.CaptureCameraImage import DualCameraOperation
from pythonProject.general.common import utils
from pythonProject.general.common.tracker_object import TriangleTracker, pixel_to_point
from pythonProject.general.common import calibration
import math


class kneeMarking(DualCameraOperation):
    def __init__(self):
        self.captured_points = None
        self.tibia_plane = None
        self.femure_plane = None
        self.last_updated_point = None
        self.transformation_matrix = None
        self.g_bExit = False
        super().__init__()
        self.dual_cam = DualCameraOperation()
        self.dual_cam.initialize_cameras()
        self.dual_cam.start_grabbing()
        self.Camera_left = self.dual_cam.cam
        self.Camera_right = self.dual_cam.cam2
        self.data_buf_left = (c_ubyte * self.dual_cam.nPayloadSize)()
        self.data_buf_left = byref(self.data_buf_left)
        self.data_buf_right = (c_ubyte * self.dual_cam.nPayloadSize2)()
        self.data_buf_right = byref(self.data_buf_right)
        self.nPayloadSize_left = self.dual_cam.nPayloadSize
        self.nPayloadSize_right = self.dual_cam.nPayloadSize2
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        self.point_queue = queue.Queue()
        self.transformed_points = []

    def calculate_centroid(self, vertices):
        """
        Calculate the centroid of a triangle given its vertices.

        Parameters:
        vertices (list): A list of three vertices, each a tuple or list of three coordinates (x, y, z).

        Returns:
        tuple: The centroid of the triangle as a tuple (x, y, z).
        """
        x1, y1, z1 = vertices[0]
        x2, y2, z2 = vertices[1]
        x3, y3, z3 = vertices[2]

        centroid = (
            (x1 + x2 + x3) / 3,
            (y1 + y2 + y3) / 3,
            (z1 + z2 + z3) / 3
        )

        return centroid

    def kneeMarking_procedure(self):
        self.point_reg = ['HIF'] + [f'HC{i}' for i in range(1, 21)] + ['HC']
        first = False
        femur_kneeMarkinglist = []
        framecount = 0
        iteration = 1
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        while True:
            value = utils.checkLoopExitCondition()
            if value.lower() not in ['kneemarking', 'unikneemarking']:
                return
            if iteration > 0:
                ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                                500)
                ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                                  stFrameInfo, 500)

                if ret == 0 and ret2 == 0:
                    # Name the images left and right taking into consideration the opening streams.

                    img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                        (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                    img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                        (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

                    img_right_rectified, img_left_rectified = calibration.rectify_images(img_right, img_left)
                try:
                    detections_right = utils.find_contours_Kmeans(img_right_rectified)
                    detections_left = utils.find_contours_Kmeans(img_left_rectified)

                    if len(detections_right) == 6 and 6 == len(detections_left):
                        detections_right = sorted(detections_right, key=lambda detection: detection[0])
                        detections_left = sorted(detections_left, key=lambda detection: detection[0])
                        femure = TriangleTracker(tuple(detections_right[:3]), tuple(detections_left[:3]),
                                                 img_right_rectified,
                                                 img_left_rectified)
                        femure.find_depth()
                        F_points = femure.getLEDcordinates()
                        centroid = self.calculate_centroid(F_points)
                        point_A = (F_points[1] + F_points[0]) / 2.0
                        vector = F_points[2] - point_A

                        angle = math.degrees(math.atan2(vector[1], vector[0]))
                        if framecount == 0 and first == False:
                            if angle <= 88:
                                angle = math.degrees(math.atan2(vector[1], vector[0]))
                                print(
                                    f'Hold the LEG in single position atmost 90  current angle:{angle} !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!')
                                time.sleep(0.1)
                                continue
                            first = True
                            try:
                                utils.writeRegisteredPoint(f'{self.point_reg[framecount]}')
                            except Exception as e:
                                print(f"Error while openfile")
                            # websocket_endpoint(websocket=WebSocket, data='HIF')
                            known_point_location = centroid
                            print(f' known_point_location {known_point_location}')
                            femur_kneeMarkinglist.append(centroid)
                            print(f' Go ahead')
                            utils.play_notification_sound()
                            continue
                        print(f"centroid {centroid} ")
                        femur_kneeMarkinglist.append(centroid)
                        time.sleep(0.1)
                        try:
                            utils.writeRegisteredPoint(f'{self.point_reg[framecount]}')
                        except Exception as e:
                            print(f"Error while openfile")
                        framecount += 1
                        time.sleep(0.02)
                    else:
                        print(f"no data[0x%x] {len(detections_right)} {len(detections_left)} ")
                        # retfeed = self.dual_cam.open_combined_camera_feed()
                        # if retfeed == 0:
                        #     continue

                    if framecount == 21:
                        utils.writeRegisteredPoint(f'{self.point_reg[framecount]}')
                        iteration -= 1
                        if iteration == 0:
                            utils.play_notification_sound()
                            break
                        framecount = 0
                except Exception as e:
                    print(f'Error {e}')

        points_all2 = np.array(femur_kneeMarkinglist, dtype=float)
        try:
            def sphere_residuals(p, x, y, z):
                """
                Calculates the residuals for sphere fitting.

                Args:
                    p: Parameters of the sphere (x_c, y_c, z_c, r)
                    x, y, z: Coordinates of the points

                Returns:
                    Residuals for each point
                """
                x_c, y_c, z_c, r = p
                return np.sqrt((x - x_c) ** 2 + (y - y_c) ** 2 + (z - z_c) ** 2) - r

            x, y, z = points_all2[:, 0], points_all2[:, 1], points_all2[:, 2]

            # Initial guess for sphere parameters
            p0 = [0, 0, 0, 1]  # Initial guess for x_c, y_c, z_c, and radius

            # Perform least squares fitting
            result = least_squares(sphere_residuals, p0, args=(x, y, z))

            # Extract sphere center
            sphere_center = result.x[:3]
        except Exception as e:
            print(f'ERROR: {e}')
        pickle_path = os.path.join(self.current_dir, '..', '..', 'registration_data', 'hip_center1.pickle')
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(pickle_path), exist_ok=True)
        # Save the pickle file
        with open(pickle_path, 'wb') as handle:
            pickle.dump(sphere_center, handle, protocol=pickle.HIGHEST_PROTOCOL)
        print(f'center_ls {sphere_center}')
        return 0

    def __exit__(self):
        # utils.SetLoopExitCondition(True)
        self.g_bExit = False
        self.dual_cam.stop_grabbing()
        self.dual_cam.stop_grabbing()


class RotatedGalleryImage(QLabel):
    def __init__(self, image_path, label, parent=None):
        super().__init__(parent)
        self.image_path = image_path
        self.label = label
        pixmap = QPixmap(image_path)
        pixmap.scaled(50, 50, Qt.KeepAspectRatio)  # Scale to a fixed size
        transform = QTransform().rotate(45)  # Rotate 45 degrees

        rotated_pixmap = pixmap.transformed(transform, Qt.SmoothTransformation)

        # Resize the pixmap to make the images smaller (optional, adjust as needed)
        rotated_pixmap = rotated_pixmap.scaled(240, 240, Qt.KeepAspectRatio)  # Scale to a fixed size

        self.setPixmap(rotated_pixmap)
        #
        # # rotated_pixmap = pixmap.transformed(transform, Qt.SmoothTransformation)
        # self.setPixmap(pixmap)
        # Apply the style
        self.setStyleSheet("""
                     QLabel {
                        font-size: var(--bs-body-font-size);
                        font-weight: var(--bs-body-font-weight);
                        line-height: var(--bs-body-line-height);
                        font-family: 'Poppins-Light';
                        text-align: right;
                        width: 100%;
                        transform: rotate(316deg);
                        color: #fff;
                        text-align: center;
                    }
                    QWidget {
                        background-color: #fff;
                        color: #212529;
                        font-family: 'Poppins-Light';
                        font-size: 1rem;
                        font-weight: 400;
                        line-height: 1.5;
                        text-align: right;
                        padding-right: 10%;
                        overflow: hidden;
                    }
                   
                    QGraphicsView {
                        border: 1px solid #dee2e6;
                        border-radius: 0.375rem;
                        background-color: #f8f9fa;
                        padding: 5px;
                    }
                    QGraphicsPixmapItem {
                        border-radius: 5px;
                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    }
                """)
        self.setAlignment(Qt.AlignCenter)
        self.setToolTip(self.label)  # Show the label on hover

    def mousePressEvent(self, event):
        # Redirect to another window or show relevant information
        if event.button() == Qt.LeftButton:
            self.redirect()

    # def clear_window(self):
    #     """Removes all widgets from the main layout before adding new content."""
    #     while self.main_layout.count():
    #         item = self.main_layout.takeAt(0)
    #         if item.widget():
    #             item.widget().deleteLater()
    #         elif item.layout():
    #             self.clear_layout(item.layout())
    #
    # def clear_layout(self, layout):
    #     """Recursively delete all widgets inside a layout."""
    #     while layout.count():
    #         item = layout.takeAt(0)
    #         if item.widget():
    #             item.widget().deleteLater()
    #         elif item.layout():
    #             self.clear_layout(item.layout())
    def thr_window(self):
        """Creates a new window for 'THR'."""
        layout = QVBoxLayout()

        title = QLabel("<b>THR Window</b>")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        back_button = QPushButton("Back to Main", self)
        # back_button.clicked.connect(self.main_window)
        layout.addWidget(back_button)

        self.setLayout(layout)

    def redirect(self):
        # You can handle the redirection logic here
        print(f"Redirecting to {self.label} window...")  # Placeholder for actual redirection
        if self.label == 'THR':
            print('Entering in to new window')
            self.thr_window()


class FooterWidget(QWidget):
    def __init__(self):
        super().__init__()

        # Create the main footer layout
        self.layout = QVBoxLayout()
        # QLabel for "Auto Guided Surgery"
        self.label = QLabel("Auto Guided Surgery")
        self.label.setStyleSheet("""
            QLabel {
                font-weight: 600; /* Equivalent to --bs-body-font-weight */
                line-height: 0.1; /* Equivalent to --bs-body-line-height */
                font-family: Montserrat, Arial, sans-serif; /* Add Montserrat if available */
                text-transform: uppercase;
                text-shadow: -1px 0 rgba(0, 158, 178, 0.15), 
                             0 1px rgba(0, 158, 178, 0.15), 
                             1px 0 rgba(0, 158, 178, 0.15), 
                             0 -1px rgba(0, 158, 178, 0.15);
                color: #1a1729;
                font-size: 36px;
                margin-bottom: 0.2px; /* Add space between labels */
            }
        """)
        self.layout.addWidget(self.label)
        # QLabel for "Artikon AGS" footer content
        self.footer_content = QLabel("© 2023 Artikon AGS")
        self.footer_content.setStyleSheet("""
                    QLabel {
                        font-weight: 400;
                        text-align: center;
                        -webkit-text-size-adjust: 100%;
                        -webkit-tap-highlight-color: transparent;
                        font-family: 'Poppins-Light', sans-serif;
                        color: #fff;
                        box-sizing: border-box;
                        margin-right: 10px;
                        background: #2F2B41;
                        border-radius: 8px;
                        font-size: 14px;
                        padding: 2px 20px;
                        flex: 0;
                        line-height: 5px;
                    }
                    QLabel b {
                        font-size: 24px; /* For the bold title */
                    }
                """)
        self.footer_layout = QHBoxLayout()
        self.footer_layout.addWidget(self.footer_content, stretch=1)  # stretch=1 ensures it takes available space

        # Add a spacer to push the button to the right
        self.footer_layout.addStretch()

        self.home_btn = QPushButton("Main Menu", self)
        self.home_btn.setIcon(QIcon('../static/images/home.png'))

        # Set the icon size
        self.home_btn.setIconSize(QSize(24, 24))  # Adjust the size as needed
        self.home_btn.setFixedSize(200, 40)  # Adjusted width to fit both icon and text
        self.home_btn.setStyleSheet(
            """QPushButton {
                background-color: #2D2264;  /* A solid color for now */
                color: #fff;
                font-family: 'Poppins-Light';
                font-size: 15px;
                border-radius: 8px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #131022;  /* A solid color for hover state */
            }"""
        )

        # Add the home button to the right
        self.footer_layout.addWidget(self.home_btn)
        self.power_btn = QPushButton("", self)
        self.power_btn.setIcon(QIcon('../static/images/union.png'))
        self.power_btn.setIconSize(QSize(48, 48))  # Adjust the size as needed
        self.power_btn.setFixedSize(70, 70)  # Adjusted width to fit both icon and text
        self.power_btn.setStyleSheet("""
                    background-color: #14216A;
                    color: white;
                    font-family: 'Poppins-Light';
                    font-size: 20px;
                    text-align: center;
                    border-radius: 35px;
                """)
        self.footer_layout.addWidget(self.power_btn)
        # Add the footer layout to the main layout
        self.layout.addLayout(self.footer_layout)
        self.setLayout(self.layout)


class ArtikonApp(QWidget):
    def __init__(self):
        super().__init__()

        # Main window setup
        self.setWindowTitle('Artikon')
        self.setGeometry(100, 100, 1200, 800)

        # Set the main layout
        self.main_layout = QVBoxLayout()

        # Header section
        self.header = QHBoxLayout()
        # First header (Title)
        self.first_head = QLabel('<b>Welcome To Artikon AGS</b>', self)
        # self.first_head.setStyleSheet("font-size: 24px;")
        self.first_head.setStyleSheet("""
            QLabel {
                font-weight: 400;
                text-align: center;
                -webkit-text-size-adjust: 100%;
                -webkit-tap-highlight-color: transparent;
                font-family: 'Poppins-Light', sans-serif;
                color: #fff;
                box-sizing: border-box;
                margin-right: 10px;
                background: #2F2B41;
                border-radius: 8px;
                font-size: 14px;
                padding: 2px 20px;
                flex: 1;
                line-height: 25px;
            }
            QLabel b {
                font-size: 24px; /* For the bold title */
            }
        """)
        self.header.addWidget(self.first_head)

        # Second header (Date & Time)
        self.second_head = QHBoxLayout()
        self.date_label = QLabel('<img src="../static/images/calendar.png" /> <b>24/11/2022</b>', self)
        self.time_label = QLabel('<img src="../static/images/time.png" /> <b>18:37:05</b>', self)
        self.time_label.setStyleSheet(
            """QLabel {
                background: #2F2B41;
                color: #fff;
                font-family: 'Poppins-Light';
                font-weight: 400;
                font-size: 15px;
                line-height: 20px;
                text-align: right;
                padding: 2px 20px;
                border-radius: 8px;
                margin-right: 10px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 360px;
            }"""
        )
        self.date_label.setStyleSheet(
            """QLabel {
                background: #2F2B41;
                color: #fff;
                font-family: 'Poppins-Light';
                font-weight: 400;
                font-size: 15px;
                line-height: 20px;
                text-align: right;
                padding: 2px 20px;
                border-radius: 8px;
                margin-right: 10px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 360px;
            }"""
        )

        self.second_head.addWidget(self.date_label)
        self.second_head.addWidget(self.time_label)

        self.header.addLayout(self.second_head)

        self.settings_btn = QPushButton(self)

        # Set the stylesheet for the settings button

        # Set the icon for the button
        self.settings_btn.setIcon(QIcon('../static/images/settings.png'))

        # Set the icon size
        self.settings_btn.setIconSize(QSize(24, 24))  # Adjust the size as needed

        # Set the button's text (label) beside the icon
        self.settings_btn.setText("Settings")

        # Set the button's size and policy
        self.settings_btn.setFixedSize(200, 40)  # Adjusted width to fit both icon and text
        self.settings_btn.setStyleSheet(
            """QPushButton {
                background-color: #2D2264;  /* A solid color for now */
                color: #fff;
                font-family: 'Poppins-Light';
                font-size: 15px;
                border-radius: 8px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #131022;  /* A solid color for hover state */
            }"""
        )

        # self.settings_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        # Set the layout for the button to ensure the icon and text are aligned properly
        layout = QHBoxLayout(self.settings_btn)  # Creating a horizontal layout for the button
        layout.addWidget(self.settings_btn)  # Adding the button to the layout
        self.header.addWidget(self.settings_btn)
        self.main_layout.addLayout(self.header)
        self.main_layout.addLayout(self.header)
        self.time_timer = QTimer(self)
        self.time_timer.timeout.connect(self.update_time)

    def window_header(self):
        self.footer = QHBoxLayout()
        footer = FooterWidget()
        self.main_layout.addWidget(footer)
        self.main_layout.addLayout(self.footer)
        self.setLayout(self.main_layout)
        # Timer to update time

        self.time_timer.start(100)  # Update every second

    def middle_section_window(self):
        # Add header to main layout

        # Middle section
        self.middle_section = QHBoxLayout()

        # Logo
        self.logo = QLabel(self)
        self.logo.setPixmap(QPixmap("../static/images/artikon_logo.png"))
        self.logo.setAlignment(Qt.AlignCenter)
        self.middle_section.addWidget(self.logo)

        # Rotated Gallery Grid
        self.gallery_grid = QGridLayout()

        self.gallery_grid.setSpacing(0)  # Remove spacing between grid items
        self.gallery_grid.setContentsMargins(0, 0, 0, 0)  # Remove margins around the grid
        gallery_images = [
            ("../static/images/gallery/Thr.png", "THR"),
            ("../static/images/gallery/Tkr.png", "TKR"),
            ("../static/images/gallery/UKA.png", "UKA"),
            ("../static/images/gallery/hip.png", "rTHR"),
            ("../static/images/gallery/re-knee.png", "rTKR")
        ]

        # Add one central image in the middle
        center_image = RotatedGalleryImage(gallery_images[2][0], gallery_images[2][1])  # Center image (UKA)
        self.gallery_grid.addWidget(center_image, 1, 1)  # Place in the center (row 1, col 1)

        # Place the remaining images in the four corners
        top_left_image = RotatedGalleryImage(gallery_images[0][0], gallery_images[0][1])  # THR
        self.gallery_grid.addWidget(top_left_image, 0, 0)  # Top-left corner

        top_right_image = RotatedGalleryImage(gallery_images[1][0], gallery_images[1][1])  # TKR
        self.gallery_grid.addWidget(top_right_image, 0, 2)  # Top-right corner

        bottom_left_image = RotatedGalleryImage(gallery_images[3][0], gallery_images[3][1])  # rTHR
        self.gallery_grid.addWidget(bottom_left_image, 2, 0)  # Bottom-left corner

        bottom_right_image = RotatedGalleryImage(gallery_images[4][0], gallery_images[4][1])  # rTKR
        self.gallery_grid.addWidget(bottom_right_image, 2, 2)  # Bottom-right corner

        # Add the gallery grid to the middle section (to the right of the logo)
        self.middle_section.addLayout(self.gallery_grid)

        # Add middle section to main layout
        self.main_layout.addLayout(self.middle_section)

    def go_home(self):
        print("Redirecting to the main menu...")  # Placeholder for redirection logic



    def update_time(self):
        current_time = QTime.currentTime().toString('hh:mm:ss')
        self.time_label.setText(f'<img src="static/images/time.png" /> {current_time}')



if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setStyleSheet("QWidget { background-color: black; color: white; }")
    window = ArtikonApp()
    # time.sleep(5)
    window.middle_section_window()
    window.window_header()
    window.show()
    sys.exit(app.exec_())
