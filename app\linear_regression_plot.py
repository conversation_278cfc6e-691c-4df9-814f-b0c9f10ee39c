import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
import re
from mpl_toolkits.mplot3d import Axes3D

# --- 1. Load Data from Text File ---
file_path = r"D:\SpineSurgery\pythonProject\dataset.txt"

led_list = []
robot_list = []
rpy_list = []

with open(file_path, 'r') as f:
    for line in f:
        led_match = re.search(r'LED\s+\[([^\]]+)\]', line)
        robot_match = re.search(r'robot\s+\(([^)]+)\)', line)
        rpy_match = re.search(r'axis roll pitch yaw\s+\(([^)]+)\)', line)
        
        if led_match and robot_match:
            led_vals = list(map(float, led_match.group(1).split()))
            robot_vals = list(map(float, robot_match.group(1).split(',')))
            led_list.append(led_vals)
            robot_list.append(robot_vals)
            if rpy_match:
                rpy_vals = list(map(float, rpy_match.group(1).split(',')))
                rpy_list.append(rpy_vals)

LED = np.array(led_list)
robot = np.array(robot_list)
rpy = np.array(rpy_list) if rpy_list else None

print("LED shape:", LED.shape)
print("Robot shape:", robot.shape)
if rpy is not None:
    print("Roll-Pitch-Yaw shape:", rpy.shape)

# --- 2. Fit Linear Regression Model ---
model = LinearRegression()
model.fit(LED, robot)
robot_pred = model.predict(LED)

fig, axes = plt.subplots(2, 3, figsize=(18, 10))
ax1, ax2, ax3, ax4, ax5, _ = axes.flatten()  # 6th subplot can be left empty or reused

# 1. 3D Plot

fig.delaxes(ax1)  # Remove 2D ax to add a 3D one
ax1_3d = fig.add_subplot(2, 3, 1, projection='3d')
ax1_3d.scatter(robot[:, 0], robot[:, 1], robot[:, 2], color='blue', label='Actual')
ax1_3d.scatter(robot_pred[:, 0], robot_pred[:, 1], robot_pred[:, 2], color='red', label='Predicted')
ax1_3d.set_title("3D Actual vs Predicted Robot Positions")
ax1_3d.set_xlabel('X')
ax1_3d.set_ylabel('Y')
ax1_3d.set_zlabel('Z')
ax1_3d.legend()

# 2. 2D X/Y/Z Comparison
ax2.plot(robot[:, 0], 'bo-', label='Actual X')
ax2.plot(robot_pred[:, 0], 'r--', label='Predicted X')
ax2.plot(robot[:, 1], 'go-', label='Actual Y')
ax2.plot(robot_pred[:, 1], 'g--', label='Predicted Y')
ax2.plot(robot[:, 2], 'ko-', label='Actual Z')
ax2.plot(robot_pred[:, 2], 'k--', label='Predicted Z')
ax2.set_title("2D Actual vs Predicted (X, Y, Z)")
ax2.set_xlabel("Sample Index")
ax2.set_ylabel("Value")
ax2.legend(loc='best')

# 3. Euclidean Error Plot
error = np.linalg.norm(robot - robot_pred, axis=1)
ax3.plot(error, marker='o', color='purple')
ax3.set_title("Prediction Error (Euclidean Distance)")
ax3.set_xlabel("Sample Index")
ax3.set_ylabel("Error")

# 4. Residual Z Plot
residual_z = robot[:, 2] - robot_pred[:, 2]
ax4.bar(np.arange(len(residual_z)), residual_z, color='orange')
ax4.set_title("Residuals (Z: Actual - Predicted)")
ax4.set_xlabel("Sample Index")
ax4.set_ylabel("Residual (Z)")

# 5. LED X vs Robot X Linear Regression
x = LED[:, 0].reshape(-1, 1)
y = robot[:, 0]
simple_model = LinearRegression()
simple_model.fit(x, y)
y_pred = simple_model.predict(x)

ax5.scatter(x, y, color='blue', s=10, label='Data')
ax5.plot(x, y_pred, color='red', linewidth=2, label='Regression Line')
ax5.set_title("LED-X vs Robot-X Linear Fit")
ax5.set_xlabel("LED X")
ax5.set_ylabel("Robot X")
ax5.legend()

# Optional: Hide 6th unused subplot (if any)
fig.delaxes(axes[1][2])  # or reuse it for another plot

plt.tight_layout()
plt.show()
