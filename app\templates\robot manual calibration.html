<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Robot Calibration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            text-align: center;
        }
        .point-indicator {
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #ddd;
            margin: 10px;
            transition: background-color 0.3s;
        }
        .active {
            background-color: #4CAF50;
        }
        .completed {
            background-color: #2196F3;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
            background-color: #f5f5f5;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            margin: 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Manual Robot Calibration</h1>
        <p>Move the robot to 4 different positions and hold steady for 2 seconds at each position.</p>
        
        <div class="points-container">
            <div class="point-indicator" id="pos1"></div>
            <div class="point-indicator" id="pos2"></div>
            <div class="point-indicator" id="pos3"></div>
            <div class="point-indicator" id="pos4"></div>
        </div>
        
        <div class="status" id="status">Ready to start calibration</div>
        
        <button id="startCalibration">Start Calibration</button>
    </div>

    <script>
        let socket;
        let pointsCollected = 0;
        const totalPositions = 4;
        
        document.getElementById('startCalibration').addEventListener('click', function() {
            connectWebSocket();
            this.disabled = true;
            document.getElementById('status').textContent = 'Calibration started. Move robot to position 1 and hold steady.';
            document.getElementById('pos1').classList.add('active');
        });
        
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/robot_manual_calibration`;
            
            socket = new WebSocket(wsUrl);
            
            socket.onopen = function(e) {
                console.log("WebSocket connection established");
            };
            
            socket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                
                if (data.status === "captured") {
                    // Update UI to show point captured
                    document.getElementById(`pos${data.point}`).classList.remove('active');
                    document.getElementById(`pos${data.point}`).classList.add('completed');
                    
                    if (data.point < totalPositions) {
                        document.getElementById(`pos${data.point + 1}`).classList.add('active');
                        document.getElementById('status').textContent = 
                            `Position ${data.point} captured! Move robot to position ${data.point + 1} and hold steady.`;
                    } else {
                        document.getElementById('status').textContent = 'All positions captured. Processing...';
                    }
                    
                    pointsCollected = data.point;
                }
                else if (data.status === "completed") {
                    document.getElementById('status').textContent = 
                        `Calibration completed successfully! Data saved to: ${data.file}`;
                }
            };
            
            socket.onclose = function(event) {
                console.log("WebSocket connection closed");
            };
            
            socket.onerror = function(error) {
                console.error("WebSocket error:", error);
                document.getElementById('status').textContent = 'WebSocket error occurred. Please refresh the page.';
            };
        }
    </script>
</body>
</html>