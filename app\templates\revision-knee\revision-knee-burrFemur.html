<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Artikon</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>

    <!-- 3D Model Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128/examples/js/loaders/OBJLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128/examples/js/controls/OrbitControls.js"></script>

    <style>
        /* Hide the container when showing static image */
        .grid.position-relative {
            border: none;
            background: none;
            overflow: visible;
        }

        /* Full viewport canvas when model is shown */
            #canvas-container {
            width: 100vw; /* Full viewport width */
            height: 100vh; /* Full viewport height */
            background-color: black; /* Background color */
            position: absolute; /* Positioning to cover the entire viewport */
            top: 0; /* Align to the top */
            left: 0; /* Align to the left */
            display: none; /* Initially hidden */
        }

        /* When model is active, show canvas and hide everything else */
        .main_block.model-active .row.text-center {
            display: none;
        }

        /* Hide notes section always
        .note {
            display: none !important;
        }
        */

        /* Static image style - make it stand alone */
        #static-image {
            display: block;
            max-width: 100%;
            height: auto;
            margin: 0 auto;
        }

        /* Remove padding/margins from grid */
        .grid.position-relative {
            padding: 0;
            margin: 0;
        }

        /* Remove sub-grid styles */
        .sub-grid {
            display: none;
        }
    </style>
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                         <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">07/01/2025</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut resect_proximal">
                        <div class="profile">
                            <ul>
                                <li>Femure Cut</li>
                               <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center justify-content-center">
                                        <div class="col-6">
<!--                                            <div class="grid position-relative">-->
<!--                                                <div class="sub-grid">-->
<!--                                                    <div class="depth">-->
<!--                                                    </div>-->
<!--                                                </div>-->
                                                <!-- 3D Model Container -->
                                                <img id="static-image" src="../static/images/revision-tkr/proximalTibiacut/sun.jpg" alt="Static Image">
                                                <div id="canvas-container"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="note">
                                                <p class="note_txt">Notes:</p>
                                                <p>Press Foot Pedal to Activate Burr</p>
                                            </div>
                                        </div>

                                    </div>
                                <div class="bottom_btn">
                                    <div class="blank blank-none"></div>
                                    <div class="btn">
                                        <a id="backBtn" href="guidance-proximal-tibia-cut.html">
                                            <span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back
                                        </a>
                                    </div>
                                    <div class="btn">
                                        <a id="nextBtn" href="verification-distal-femur-cut.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
                                    </div>
                                </div>

                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#" id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '/landing-page.html';  // Redirect to root path
										});
								</script>
                            </li>
                            <li class="footer_btn_three">
								<a href="#" id="powerbuttonLink">
									<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
								</a>
								<script>
									document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
										event.preventDefault();  // Prevent default anchor behavior
										window.location.href = '/';  // Redirect to root path
									});
								</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>

    <script>
    let scene, camera, renderer, controls, modelGroup, modelCenter;
    let socket;
    let staticImage = document.getElementById("static-image");
    let canvasContainer = document.getElementById("canvas-container");
    let modelLoaded = false;
    let pendingMessages = [];
    let dynamicMarker = null;
    let markerGroup = null; // Group for holding the marker (sphere + shaft)
    const BONE_COLOR = 0xf5deb3; // Natural bone color (wheat) - made constant to prevent changes
    let lastModelMeshes = []; // Store references to model meshes
    let paintedElements = []; // Track elements created by painting for easy removal
    let paintedPoints = {}; // Track painted points by position to manage color updates

    function init() {
        scene = new THREE.Scene();
        camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(-70, 0, -10);

        renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x000000, 1);
        canvasContainer.appendChild(renderer.domElement);

        controls = new THREE.OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05; // Smoother camera movement
        controls.minDistance = 60; // Higher minimum distance (prevents zooming in too far)
        controls.maxDistance = 80; // Lower maximum distance (prevents zooming out too far)
        controls.enablePan = false; // Disable panning to keep model centered
        controls.target.set(0, 0, 0); // Ensure the orbit target is at the origin

        // Add a stronger directional light from above to highlight the metallic shaft
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.4); // Reduced ambient light intensity
        scene.add(ambientLight);

        const dirLight = new THREE.DirectionalLight(0xffffff, 0.5); // Reduced directional light intensity
        dirLight.position.set(5, 15, 5); // Position higher to cast light from above
        scene.add(dirLight);

        // Create modelGroup first just like the reference
        modelGroup = new THREE.Group();
        scene.add(modelGroup);

        // Load OBJ model
        const loader = new THREE.OBJLoader();
        loader.load('/static/images/hip-bone/3D/FEMUR_FULL.obj', (obj) => {
                    const box = new THREE.Box3().setFromObject(obj);
                    modelCenter = box.getCenter(new THREE.Vector3());

            // Position the model exactly like in the reference
                    obj.position.sub(modelCenter);

            // Store mesh references for interactions
            lastModelMeshes = [];
                    obj.traverse(child => {
                        if (child.isMesh) {
                    // Use more realistic yellowish bone color for the model
                            child.material = new THREE.MeshStandardMaterial({
                                color: BONE_COLOR, // Use constant bone color
                                side: THREE.DoubleSide,
                                roughness: 0.85, // Increased roughness for more bone-like texture
                                metalness: 0.02 // Reduced metalness for more natural appearance
                            });
                    lastModelMeshes.push(child);
                }
            });

            modelGroup.add(obj);
            console.log("Model loaded successfully, center at:", modelCenter);

            // Create marker group (will be positioned with incoming points)
            createMarkerWithShaft();

                    modelLoaded = true;

            // Process any pending messages
                    pendingMessages.forEach(data => handleSocketMessage(data));
                    pendingMessages = [];
                },
                // Progress callback
        (xhr) => {
            console.log((xhr.loaded / xhr.total * 100) + '% loaded');
                },
                // Error callback
        (error) => {
            console.error("Error loading model:", error);
        });

        window.addEventListener("resize", onWindowResize);

        // Start the inactivity checker to hide marker when points stop coming
        setInterval(checkMarkerVisibility, 500); // Check every half second

        animate();
    }

    function createMarkerWithShaft() {
        // Create a group for the marker
        markerGroup = new THREE.Group();
        modelGroup.add(markerGroup);

        // Create sphere for the marker head (will be at the point)
        // Use a smaller radius for more precision (0.3 instead of 0.5)
        const sphereGeometry = new THREE.SphereGeometry(0.3, 16, 16);
        const sphereMaterial = new THREE.MeshBasicMaterial({
            color: 0x00ff00, // Always start with green marker
            transparent: true,
            opacity: 0.9 // Make it more visible
        });
        const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
        sphere.position.set(0, 0, 0);
        markerGroup.add(sphere);

        // Create cylinder for the shaft with metallic appearance - more professional
        const cylinderGeometry = new THREE.CylinderGeometry(0.2, 0.2, 15, 12);
        const cylinderMaterial = new THREE.MeshStandardMaterial({
            color: 0xbfbfbf, // Silver metallic color (0.75, 0.75, 0.75) in RGB
            metalness: 0.95,
            roughness: 0.05,
            emissive: 0x222222,
            emissiveIntensity: 0.2
        });
        const cylinder = new THREE.Mesh(cylinderGeometry, cylinderMaterial);

        // Position the shaft
        cylinder.rotation.z = Math.PI / 2; // Rotate to align with x-axis

        // Position shaft with slight upward offset to ensure tip is visible
        cylinder.position.set(7.5, 0.3, 0);

        markerGroup.add(cylinder);

        // Add a small metallic tip at the end for precision and visibility
        const tipGeometry = new THREE.ConeGeometry(0.15, 0.5, 12);
        const tipMaterial = new THREE.MeshStandardMaterial({
            color: 0xd4d4d4,
            metalness: 0.95,
            roughness: 0.05,
            emissive: 0x444444,
            emissiveIntensity: 0.3
        });
        const tip = new THREE.Mesh(tipGeometry, tipMaterial);
        tip.rotation.z = -Math.PI / 2; // Point toward the end of shaft
        tip.position.set(-0.2, 0, 0); // Position at the tip of marker
        markerGroup.add(tip);

        // Initially hide marker
        markerGroup.visible = false;
    }

    // Create a more refined, professional colored point on the bone
    function createColoredPoint(point, color, isRed, isBlue) {
        // Create mesh-based painting effect
        paintMeshRegion(point, color, isRed, isBlue);
        return point;
    }

    // Add this new function to handle mesh-based painting
    function paintMeshRegion(point, color, isRed, isBlue) {
        if (!modelCenter || !lastModelMeshes || lastModelMeshes.length === 0) {
            console.error("Model meshes not available for painting");
            return;
        }

        // Calculate position relative to model center
        const localX = point[0] - modelCenter.x;
        const localY = point[1] - modelCenter.y;
        const localZ = point[2] - modelCenter.z;
        const paintPosition = new THREE.Vector3(localX, localY, localZ);

        // Generate a unique key for this point position (rounded to 2 decimal places for tolerance)
        const pointKey = `${point[0].toFixed(2)},${point[1].toFixed(2)},${point[2].toFixed(2)}`;

        // Check if this point was already painted
        if (paintedPoints[pointKey]) {
            // Remove the previous elements for this specific point
            paintedPoints[pointKey].forEach(element => {
                if (element && element.parent) {
                    element.parent.remove(element);
                    // Also remove from the paintedElements array
                    const index = paintedElements.indexOf(element);
                    if (index > -1) {
                        paintedElements.splice(index, 1);
                    }
                }
            });
            // Clear the array for this point after removing all elements
            paintedPoints[pointKey] = [];
        } else {
            // Create a new array to store elements for this point if it didn't exist
            paintedPoints[pointKey] = [];
        }

        // Define paint radius (area of effect)
        const paintRadius = 0.12; // Smaller radius for more precise, professional marking

        // Make colors richer/more saturated by creating a more vibrant color
        const enhancedColor = new THREE.Color(color);
        // Increase saturation for better visibility
        const hsl = {};
        enhancedColor.getHSL(hsl);

        // Special treatment for all colors to make them bolder and more saturated
        if (isRed) {
            enhancedColor.setHSL(0, 1.0, 0.55); // Pure red with optimal brightness
        } else if (isBlue) {
            enhancedColor.setHSL(0.6, 1.0, 0.5); // Pure saturated blue
        } else {
            // Get the hue from the original color
            const hue = hsl.h;

            // For yellow (approximately hue 0.15-0.2 in THREE.js)
            if (hue > 0.1 && hue < 0.25) {
                enhancedColor.setHSL(hue, 1.0, 0.5); // Pure saturated yellow
            }
            // For blue (approximately hue 0.6-0.7 in THREE.js)
            else if (hue > 0.55 && hue < 0.75) {
                enhancedColor.setHSL(hue, 1.0, 0.5); // Pure saturated blue
            }
            // For other colors
            else {
                enhancedColor.setHSL(hsl.h, Math.min(1.0, hsl.s * 1.5), Math.min(0.7, hsl.l * 1.2));
            }
        }

        // Create a visual indicator to show where we're painting with enhanced visibility
        const indicatorGeometry = new THREE.RingGeometry(paintRadius - 0.02, paintRadius, 32); // More segments for smoother appearance
        const indicatorMaterial = new THREE.MeshBasicMaterial({
            color: enhancedColor,
            transparent: true,
            opacity: isRed ? 1.0 : 0.95, // Much higher opacity for professional look
            side: THREE.DoubleSide
        });

        // Track the indicator in both arrays
        const indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
        indicator.position.copy(paintPosition);
        indicator.lookAt(0, 0, 0);
        indicator.rotateX(Math.PI/2);
        modelGroup.add(indicator);
        paintedElements.push(indicator);
        paintedPoints[pointKey].push(indicator); // Track this element for this specific point

        // Add a more visible marker at the exact point
        const dotGeometry = new THREE.CircleGeometry(0.06, 24); // Smaller, more precise dot with higher detail
        const dotMaterial = new THREE.MeshBasicMaterial({
            color: enhancedColor,
            transparent: true,
            opacity: 1.0, // Full opacity for all colors
            side: THREE.DoubleSide
        });
        const dot = new THREE.Mesh(dotGeometry, dotMaterial);
        dot.position.copy(paintPosition.clone().add(new THREE.Vector3(0, 0.02, 0))); // Higher above surface
        dot.lookAt(0, 0, 0);
        dot.rotateX(Math.PI/2);
        modelGroup.add(dot);
        paintedElements.push(dot);
        paintedPoints[pointKey].push(dot); // Track this element for this specific point

        // After adding the dot, for red color:
        if (isRed) {
            // Add a subtle glow effect around red dots for enhanced visibility
            const glowGeometry = new THREE.CircleGeometry(0.18, 16); // Larger than the dot
            const glowMaterial = new THREE.MeshBasicMaterial({
                color: 0xff3333, // Slightly different red for the glow
                transparent: true,
                opacity: 0.11, // Increased opacity for red glow
                side: THREE.DoubleSide
            });

            const glow = new THREE.Mesh(glowGeometry, glowMaterial);
            glow.position.copy(paintPosition.clone().add(new THREE.Vector3(0, 0.015, 0)));
            glow.lookAt(0, 0, 0);
            glow.rotateX(Math.PI/2);
            modelGroup.add(glow);
            paintedElements.push(glow);
            paintedPoints[pointKey].push(glow); // Track this element for this specific point

            // Add a second, larger glow for more dramatic effect
            const outerGlowGeometry = new THREE.CircleGeometry(0.25, 16);
            const outerGlowMaterial = new THREE.MeshBasicMaterial({
                color: 0xff0000,
                transparent: true,
                opacity: 0.2,
                side: THREE.DoubleSide
            });

            const outerGlow = new THREE.Mesh(outerGlowGeometry, outerGlowMaterial);
            outerGlow.position.copy(paintPosition.clone().add(new THREE.Vector3(0, 0.01, 0)));
            outerGlow.lookAt(0, 0, 0);
            outerGlow.rotateX(Math.PI/2);
            modelGroup.add(outerGlow);
            paintedElements.push(outerGlow);
            paintedPoints[pointKey].push(outerGlow); // Track this element for this specific point
        }
        // Add glow for other colors
        else {
            // Get color values to determine glow color
            const colorHex = enhancedColor.getHex();

            // Create glow for yellow or blue
            const glowGeometry = new THREE.CircleGeometry(0.15, 16);

            // Determine glow color and intensity based on the color
            let glowColor, glowOpacity;
            if (colorHex === 0xffcc00 || (hsl.h > 0.1 && hsl.h < 0.25)) { // Yellow
                glowColor = 0xffdd44; // Brighter yellow for glow
                glowOpacity = 0.35;
            } else { // Blue or other colors
                glowColor = colorHex;
                glowOpacity = 0.3;
            }

            const glowMaterial = new THREE.MeshBasicMaterial({
                color: glowColor,
                transparent: true,
                opacity: glowOpacity,
                side: THREE.DoubleSide
            });

            const glow = new THREE.Mesh(glowGeometry, glowMaterial);
            glow.position.copy(paintPosition.clone().add(new THREE.Vector3(0, 0.012, 0)));
            glow.lookAt(0, 0, 0);
            glow.rotateX(Math.PI/2);
            modelGroup.add(glow);
            paintedElements.push(glow);
            paintedPoints[pointKey].push(glow); // Track this element for this specific point
        }

        // Variable to track if we found edges to paint
        let foundEdges = false;

        // Process each mesh to find edge geometry
        lastModelMeshes.forEach(mesh => {
            if (!mesh.geometry) return;

            const geometry = mesh.geometry;
            const positionAttribute = geometry.getAttribute('position');
            if (!positionAttribute) return;

            // Detect edges of the mesh
            const edgeGeometry = new THREE.EdgesGeometry(geometry);
            const worldMatrix = mesh.matrixWorld;

            // Create buffers for our custom edge geometry
            const edgePositions = [];
            const edgeIndices = [];

            // Process the edge geometry from the EdgesGeometry
            const edgePositionAttribute = edgeGeometry.getAttribute('position');

            // Sample points along the edges at a finer interval - INCREASED step size to reduce density
            const step = 0.12; // Larger step means fewer points

            for (let i = 0; i < edgePositionAttribute.count; i += 2) {
                // Get start and end points of this edge
                const startVert = new THREE.Vector3().fromBufferAttribute(edgePositionAttribute, i);
                const endVert = new THREE.Vector3().fromBufferAttribute(edgePositionAttribute, i + 1);

                // Transform to world space
                startVert.applyMatrix4(worldMatrix);
                endVert.applyMatrix4(worldMatrix);

                // Calculate direction vector
                const edgeDir = new THREE.Vector3().subVectors(endVert, startVert);
                const edgeLength = edgeDir.length();
                edgeDir.normalize();

                // Sample points along this edge
                for (let t = 0; t <= edgeLength; t += step) {
                    const pointOnEdge = new THREE.Vector3().copy(startVert)
                        .add(edgeDir.clone().multiplyScalar(t));

                    // Check distance to paint position
                    const dist = pointOnEdge.distanceTo(paintPosition);

                    if (dist <= paintRadius) {
                        // Get normalized direction from bone center to edge point
                        const surfaceDir = new THREE.Vector3().copy(pointOnEdge).normalize();

                        // Create a precise disc facing outward from the bone
                        const discGeometry = new THREE.CircleGeometry(0.02 + Math.random() * 0.03, 16); // Smaller, more precise discs
                        const paintOpacity = Math.max(0.85, 1.0 - (dist / paintRadius)); // Higher minimum opacity

                        const discMaterial = new THREE.MeshBasicMaterial({
                            color: enhancedColor,
                            transparent: true,
                            opacity: Math.min(1.0, paintOpacity * 1.8), // Much higher opacity for all colors
                            side: THREE.DoubleSide
                        });

                        const disc = new THREE.Mesh(discGeometry, discMaterial);

                        // Position the disc just slightly beyond the bone surface
                        const offset = 0.01 + Math.random() * 0.01; // More prominent offset
                        disc.position.copy(pointOnEdge.clone().add(surfaceDir.multiplyScalar(offset)));

                        // Ensure the disc faces outward from the bone surface
                        disc.lookAt(disc.position.clone().add(surfaceDir));

                        // Random rotation around normal for variety
                        disc.rotateZ(Math.random() * Math.PI * 2);

                        // Add this disc to the model group
                        modelGroup.add(disc);
                        paintedElements.push(disc);
                        paintedPoints[pointKey].push(disc); // Track this element for this specific point

                        // If this disc is very close to the paint position, add a small highlight
                        if (dist < paintRadius * 0.25) { // Smaller highlight region
                            const highlightGeometry = new THREE.CircleGeometry(0.015 + Math.random() * 0.01, 12); // Smaller, more precise highlights
                            const highlightMaterial = new THREE.MeshBasicMaterial({
                                color: enhancedColor.clone().offsetHSL(0, -0.1, 0.25), // Lighter version of the color
                                transparent: true,
                                opacity: 0.7 + Math.random() * 0.3, // Higher opacity for better visibility
                                side: THREE.DoubleSide
                            });

                            const highlight = new THREE.Mesh(highlightGeometry, highlightMaterial);
                            highlight.position.copy(disc.position.clone().add(surfaceDir.clone().multiplyScalar(0.002)));
                            highlight.lookAt(highlight.position.clone().add(surfaceDir));
                            highlight.rotateZ(Math.random() * Math.PI * 2);
                            modelGroup.add(highlight);
                            paintedElements.push(highlight);
                            paintedPoints[pointKey].push(highlight); // Track this element for this specific point
                        }

                        foundEdges = true;
                    }
                }
            }
        });

        // If no edges were found within the paint radius
        if (!foundEdges) {
            // Create a controlled circular layer that extends slightly beyond the bone's surface
            // This is our fallback if we didn't find edges to paint

            // Find the nearest surface point on any mesh
            let nearestSurfacePoint = null;
            let nearestSurfaceNormal = new THREE.Vector3(0, 1, 0);
            let minDistance = Infinity;

            lastModelMeshes.forEach(mesh => {
                if (!mesh.geometry) return;

                const geometry = mesh.geometry;
                const positionAttribute = geometry.getAttribute('position');
                if (!positionAttribute) return;

                // Calculate the transformation matrix
                const worldMatrix = mesh.matrixWorld;

                // Create a raycaster to find the nearest surface
                const raycaster = new THREE.Raycaster();
                raycaster.far = 50;

                // Create multiple rays in different directions to find the surface
                for (let angleY = 0; angleY < Math.PI*2; angleY += Math.PI/8) {
                    for (let angleX = -Math.PI/2; angleX <= Math.PI/2; angleX += Math.PI/8) {
                        const direction = new THREE.Vector3(
                            Math.cos(angleY) * Math.cos(angleX),
                            Math.sin(angleX),
                            Math.sin(angleY) * Math.cos(angleX)
                        );

                        raycaster.set(paintPosition, direction);
                        const intersects = raycaster.intersectObject(mesh);

                        if (intersects.length > 0) {
                            const hitPoint = intersects[0].point;
                            const hitDistance = paintPosition.distanceTo(hitPoint);

                            if (hitDistance < minDistance) {
                                minDistance = hitDistance;
                                nearestSurfacePoint = hitPoint.clone();
                                nearestSurfaceNormal = intersects[0].face.normal.clone()
                                    .applyMatrix3(new THREE.Matrix3().getNormalMatrix(worldMatrix));
                            }
                        }
                    }
                }
            });

            // If we found a surface point
            if (nearestSurfacePoint) {
                // Create a highly visible, precise layer along the surface
                const layerGeometry = new THREE.CircleGeometry(paintRadius * 0.5, 24); // Smaller, higher quality size
                const layerMaterial = new THREE.MeshBasicMaterial({
                    color: enhancedColor,
                    transparent: true,
                    opacity: 1.0, // Full opacity for professional look
                    side: THREE.DoubleSide
                });

                const layer = new THREE.Mesh(layerGeometry, layerMaterial);

                // Position the layer slightly above the surface along the normal
                layer.position.copy(nearestSurfacePoint.clone().add(
                    nearestSurfaceNormal.clone().multiplyScalar(0.02) // Higher above surface
                ));

                // Orient the layer to follow the surface
                layer.lookAt(nearestSurfacePoint.clone().add(nearestSurfaceNormal));

                modelGroup.add(layer);
                paintedElements.push(layer);
                paintedPoints[pointKey].push(layer); // Track this element for this specific point

                // Add a few smaller circular layers for visual effect
                for (let i = 0; i < 3; i++) {
                    const smallLayerRadius = paintRadius * (0.1 + Math.random() * 0.1); // Smaller, more precise varied sizes
                    const smallLayerGeometry = new THREE.CircleGeometry(smallLayerRadius, 16);

                    // Vary the color slightly
                    const variationColor = new THREE.Color(enhancedColor);
                    variationColor.offsetHSL(0, 0, (Math.random() - 0.5) * 0.1);

                    const smallLayerMaterial = new THREE.MeshBasicMaterial({
                        color: variationColor,
                        transparent: true,
                        opacity: Math.min(1.0, 0.9 + Math.random() * 0.1), // Higher opacity (near 100%) for all colors
                        side: THREE.DoubleSide
                    });

                    const smallLayer = new THREE.Mesh(smallLayerGeometry, smallLayerMaterial);

                    // Random position within the main layer area, but following the surface contour
                    const angle = Math.random() * Math.PI * 2;
                    const radius = Math.random() * paintRadius * 0.4;
                    const offsetX = Math.cos(angle) * radius;
                    const offsetZ = Math.sin(angle) * radius;

                    // Create a tangent vector to the surface
                    const tangent = new THREE.Vector3(1, 0, 0);
                    if (Math.abs(nearestSurfaceNormal.x) > 0.99) {
                        tangent.set(0, 1, 0);
                    }
                    tangent.crossVectors(tangent, nearestSurfaceNormal).normalize();
                    const bitangent = new THREE.Vector3().crossVectors(nearestSurfaceNormal, tangent);

                    // Offset position using the tangent and bitangent
                    smallLayer.position.copy(nearestSurfacePoint.clone()
                        .add(tangent.clone().multiplyScalar(offsetX))
                        .add(bitangent.clone().multiplyScalar(offsetZ))
                        .add(nearestSurfaceNormal.clone().multiplyScalar(0.03 + Math.random() * 0.01))); // Higher offset

                    // Orient to surface normal
                    smallLayer.lookAt(smallLayer.position.clone().add(nearestSurfaceNormal));

                    // Random rotation around normal
                    smallLayer.rotateZ(Math.random() * Math.PI * 2);

                    modelGroup.add(smallLayer);
                    paintedElements.push(smallLayer);
                    paintedPoints[pointKey].push(smallLayer); // Track this element for this specific point
                }

                foundEdges = true;
            }
        }

        if (!foundEdges) {
            console.warn("No suitable surfaces found for painting");
        }

        return foundEdges;
    }

    function updateModelColor(colorCode, point) {
        // We don't want to clear any previous painted elements
        // Just add new colored points to the model

        // Determine the paint color based on colorCode
        let paintColor;
        let isRed = false;
        let isBlue = false;

        switch(parseInt(colorCode)) {
            case 0:
                paintColor = 0xffcc00; // Darker/richer yellow for better visibility
                break;
            case 1:
                paintColor = 0x0055ff; // Richer blue
                isBlue = true; // Flag for special blue handling
                break;
            case 2:
                paintColor = 0xff0000; // Red
                isRed = true; // Flag for special red handling
                break;
            default:
                console.warn("Invalid color code received:", colorCode);
                paintColor = 0xffcc00; // Default to yellow instead of white for invalid codes
        }

        // Keep marker sphere color constant as green
        if (markerGroup && markerGroup.children[0] && markerGroup.children[0].material) {
            markerGroup.children[0].material.color.setHex(0x00ff00); // Keep marker always green
        }

        // Create a colored point at the current position if we have a valid point
        if (point && point.length === 3) {
            // Pass both flags to handle both colors with special effects
            createColoredPoint(point, paintColor, isRed, isBlue);
        }
    }

    function setupWebSocket() {
        socket = new WebSocket("ws://localhost:8000/ws");

        socket.addEventListener("open", () => {
            console.log("WebSocket connection opened.");
            const currentUrl = window.location.pathname;
            const pageName = "rev" + currentUrl.substring(currentUrl.lastIndexOf('/') + 1).split('?')[0];
            socket.send(JSON.stringify({ file: pageName }));
        });

        socket.addEventListener("message", (event) => {
            const data = event.data.trim();
            // Only update timestamp for valid point data (that could be coordinates)
            if (data &&
                data !== "null" &&
                data !== "no detection" &&
                !data.toLowerCase().startsWith("exit") &&
                !data.toLowerCase().startsWith("color:")) {
                lastPointTimestamp = Date.now();
            }

            if (!modelLoaded) {
                pendingMessages.push(event.data);
            } else {
                handleSocketMessage(event.data);
            }
        });

        socket.addEventListener("close", () => {
            console.log("WebSocket closed. Reconnecting in 3s...");
            setTimeout(setupWebSocket, 3000);
        });

        socket.addEventListener("error", (err) => {
            console.error("WebSocket error:", err);
            socket.close();
        });
    }

    function handleSocketMessage(data) {
        try {
            data = data.trim();
            if (!data || data.toLowerCase() === "null" || data.toLowerCase() === "no detection") {
                // Show static image, hide 3D model
                document.querySelector('.main_block').classList.remove('model-active');
                staticImage.style.display = "block";
                canvasContainer.style.display = "none";
                hideMarker();
                return;
            }

            // Handle "exit" command
            if (data.toLowerCase().startsWith("exit")) {
                window.location.href = "verification-proximal-tibia-cut.html";
                return;
            }

            // Check for color command format "color:X" where X is 0, 1, or 2
            if (data.toLowerCase().startsWith("color:")) {
                const colorCode = data.split(":")[1].trim();
                // Don't send point as we just want to update the marker color
                updateModelColor(colorCode);
                return;
            }

            let point = null;
            let colorCode = null;

            // Format directly from backend: X,Y,Z,C where C is the color code 0, 1 or 2
            if (/^-?\d+(\.\d+)?,-?\d+(\.\d+)?,-?\d+(\.\d+)?,[0-2](\.[0-9]*)?$/.test(data)) {
                const parts = data.split(",").map(Number);
                point = [parts[0], parts[1], parts[2]];
                colorCode = parts[3];
                console.log("Parsed point with color code:", point, colorCode);
            }
            // Handle regular coordinate format
            else if (/^-?\d+(\.\d+)?,-?\d+(\.\d+)?,-?\d+(\.\d+)?$/.test(data)) {
                point = data.split(",").map(Number);
                // Derive color from coordinates for demo purposes if no color provided
                const sum = Math.abs(point[0]) + Math.abs(point[1]) + Math.abs(point[2]);
                colorCode = Math.floor(sum % 3); // Will give 0, 1, or 2
                console.log("Parsed point with derived color:", point, colorCode);
            } else {
                try {
                    console.log("Received data:", data);
                    // Try to parse as array [x,y,z,colorCode]
                    if (data.startsWith('[') && data.endsWith(']')) {
                        const parts = JSON.parse(data);
                        if (Array.isArray(parts) && parts.length === 4) {
                            point = [parts[0], parts[1], parts[2]];
                            colorCode = parts[3];
                            console.log("Parsed array data with color:", point, colorCode);
                            // Skip the rest of the parsing
                            if (point && point.length === 3) {
                                // Show 3D model and process the point
                                showModelAndProcessPoint(point, colorCode);
                            }
                            return;
                        }
                    }
                    const obj = JSON.parse(data);
                    console.log("Parsed JSON:", obj);
                    if (typeof obj === "object") {
                        if ("x" in obj && "y" in obj && "z" in obj) {
                            point = [obj.x, obj.y, obj.z];
                            // If color is included in the JSON
                            if ("color" in obj) {
                                colorCode = Number(obj.color); // Ensure colorCode is a number
                                console.log("Using color from JSON:", colorCode);
                            } else {
                                // Derive color from coordinates if not provided
                                const sum = Math.abs(obj.x) + Math.abs(obj.y) + Math.abs(obj.z);
                                colorCode = Math.floor(sum % 3); // Will give 0, 1, or 2
                                console.log("Derived color from coordinates:", colorCode);
                            }
                            console.log("Parsed point from JSON with color:", point, colorCode);
                        }
                    }
                } catch (err) {
                    console.warn("Invalid JSON format:", err);
                    hideMarker();
                    return;
                }
            }

            if (point && point.length === 3) {
                showModelAndProcessPoint(point, colorCode);
            } else {
                hideMarker();
            }
        } catch (error) {
            console.error("Error processing WebSocket message:", error);
            hideMarker();
        }
    }

    // Helper function to process points consistently
    function showModelAndProcessPoint(point, colorCode) {
        // Show 3D model, hide static image
        document.querySelector('.main_block').classList.add('model-active');
        staticImage.style.display = "none";
        canvasContainer.style.display = "block";

        // Move canvas container to the top level
        const mainBlock = document.querySelector('.tbl');
        if (canvasContainer.parentElement !== mainBlock) {
            mainBlock.appendChild(canvasContainer);
        }

        // Force resize on display
        setTimeout(function() {
            onWindowResize();
        }, 10);

        // Update marker position and ensure it's visible
        updateMarkerPosition(point);
        showMarker(); // Explicitly make sure the marker is visible when points arrive

        // Add a minimal highlight splash effect at the contact point - use subtle blue highlight
        createPersistentPoint(point, 0x77aaff, 0.025); // Smaller, more subtle highlight

        // Update point color without changing bone model
        if (colorCode !== null && colorCode !== undefined) {
            updateModelColor(colorCode, point);
        }
    }

    function updateMarkerPosition(point) {
        if (!markerGroup) {
            createMarkerWithShaft();
        }

        // Calculate position for marker
        const markerX = point[0] - modelCenter.x;
        const markerY = point[1] - modelCenter.y;
        const markerZ = point[2] - modelCenter.z;

        // Calculate normal vector to bone surface (approximated)
        // This will be used to properly orient the marker
        const normalVector = new THREE.Vector3(
            markerX, markerY, markerZ
        ).normalize();

        // Position marker at the received point with slight elevation
        // This ensures the shaft doesn't go into the bone
        markerGroup.position.set(
            markerX + normalVector.x * 0.15, // Increased offset for better visibility
            markerY + normalVector.y * 0.15,
            markerZ + normalVector.z * 0.15
        );

        // Orient the marker to point slightly away from the bone surface
        // This ensures the shaft's tip is always visible
        if (Math.abs(normalVector.y) < 0.9) { // Don't reorient if normal is too vertical
            const upVector = new THREE.Vector3(0, 1, 0);
            const axis = new THREE.Vector3().crossVectors(upVector, normalVector).normalize();
            const angle = Math.acos(upVector.dot(normalVector));

            // Create and apply rotation based on axis and angle
            const quaternion = new THREE.Quaternion().setFromAxisAngle(axis, angle);
            markerGroup.quaternion.copy(quaternion);

            // Apply additional rotation to align shaft horizontally
            markerGroup.rotateX(Math.PI / 2);
        }

        // Log the updated position
        console.log("Updated marker position:", markerGroup.position);
    }

    function showMarker() {
        if (markerGroup) {
            markerGroup.visible = true;
            console.log("Marker is now visible");
        }
    }

    function hideMarker() {
        if (markerGroup) {
            markerGroup.visible = false;
            console.log("Marker is now hidden");
        }
    }

    // Add a periodic check for inactivity to hide the marker when no points are received
    let lastPointTimestamp = 0;
    const INACTIVITY_TIMEOUT = 100; // 100ms without new points will hide the marker

    function checkMarkerVisibility() {
        const currentTime = Date.now();
        if (markerGroup && markerGroup.visible && (currentTime - lastPointTimestamp > INACTIVITY_TIMEOUT)) {
            console.log("No points received for " + INACTIVITY_TIMEOUT + "ms, hiding marker");
            hideMarker();
        }
    }

    function createPersistentPoint(point, color = 0x00ff00, size = 0.01) {
        // Create a precise, high-quality persistent point
        const geometry = new THREE.SphereGeometry(size * 1.5, 24, 24); // Smaller but higher quality with more segments
        const material = new THREE.MeshBasicMaterial({
            color: color,
            transparent: true,
            opacity: 0.9 // Higher opacity for professional look
        });
        const trailPoint = new THREE.Mesh(geometry, material);

        // Position with minimal offset for precision
        trailPoint.position.set(
            point[0] - modelCenter.x,
            point[1] - modelCenter.y + 0.008, // Smaller offset for more precise positioning
            point[2] - modelCenter.z
        );

        modelGroup.add(trailPoint);
        paintedElements.push(trailPoint); // Track this element
        return trailPoint;
    }

    function animate() {
        requestAnimationFrame(animate);
        controls.update();
        renderer.render(scene, camera);
    }

    function onWindowResize() {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    }
	
	function handleNavigation(event) {
    event.preventDefault();
    const targetUrl = event.currentTarget.href;

    if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({ file: " " }));
        socket.addEventListener('close', function () {
            window.location.href = targetUrl;
        }, { once: true });

        socket.close();
    } else {
        window.location.href = targetUrl;
    }
};
	document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
    document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);
	
    document.addEventListener('DOMContentLoaded', function() {
        staticImage = document.getElementById("static-image");
        canvasContainer = document.getElementById("canvas-container");

        // Hide the notes section
        const notes = document.querySelectorAll('.note');
        notes.forEach(note => {
            note.style.display = 'none';
        });

        // Remove border from container
        const container = document.querySelector('.grid.position-relative');
        if (container) {
            container.style.border = 'none';
            container.style.minHeight = 'auto';
        }

        // Make sure canvas container starts hidden
        canvasContainer.style.display = 'none';

        setupWebSocket();
        init();
    });

    // Add the clearPreviousPaint function after the init function
    // We're modifying this to not actually clear anything
    function clearPreviousPaint() {
        // This function now intentionally does nothing to preserve all colored points
        console.log(`Preserving all ${paintedElements.length} painted elements`);
        // We don't remove any elements
    }
    </script>

    <!-- Common scripts -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="../static/js/common.js"></script>

    <!-- Camera functionality -->
    <script>
	
		document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
		document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);
	
        document.addEventListener('DOMContentLoaded', function() {
            const cameraButton = document.querySelector('.footer_btn_one .btn.first');
            const cameraModal = document.getElementById('cameraModal');
            const closeCamera = document.querySelector('.close-camera');
            const videoStream = document.getElementById('videoStream');
            let mediaStream = null;

            // Function to start the camera
            async function startCamera() {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({
                        video: {
                            width: { ideal: 1280 },
                            height: { ideal: 720 }
                        }
                    });
                    mediaStream = stream;
                    videoStream.srcObject = stream;
                    videoStream.play();
                } catch (err) {
                    console.error('Error accessing camera:', err);
                }
            }

            // Function to stop the camera
            function stopCamera() {
                if (mediaStream) {
                    mediaStream.getTracks().forEach(track => track.stop());
                    videoStream.srcObject = null;
                    mediaStream = null;
                }
            }

            // Open camera modal
            cameraButton.addEventListener('click', function(e) {
                e.preventDefault();
                cameraModal.style.display = 'block';
                startCamera();
            });

            // Close camera modal
            closeCamera.addEventListener('click', function() {
                cameraModal.style.display = 'none';
                stopCamera();
            });

            // Close modal when clicking outside
            window.addEventListener('click', function(event) {
                if (event.target === cameraModal) {
                    cameraModal.style.display = 'none';
                    stopCamera();
                }
            });
        });
    </script>
</body>
</html>
