import numpy as np
import pickle
import time
from test import Hip<PERSON>arking

def collect_led_points_cam_list(num_frames=10, save_path="led_points_cam_list.pkl"):
    marking = HipMarking()
    led_points_cam_list = []

    print("Starting LED 3D point collection...")
    frames_collected = 0
    while frames_collected < num_frames:
        # Get the latest processed detection results
        results = marking.get_last_n_results(n=1, max_age_ms=500)
        if not results:
            time.sleep(0.1)
            continue

        det_left = results[0]["left"]
        det_right = results[0]["right"]

        # Only proceed if 3 points detected in both images
        if len(det_left) == 3 and len(det_right) == 3:
            # Your code should have a function to triangulate 3D points from stereo pairs
            # For example, pixel_to_point or similar
            led_points_3d = []
            for i in range(3):
                # Replace this with your actual triangulation logic
                # Example: led_3d = pixel_to_point(det_left[i], det_right[i])
                led_3d = np.array([0, 0, 0])  # <-- Replace with real calculation
                led_points_3d.append(led_3d)
            led_points_cam_list.append(np.array(led_points_3d))
            print(f"Frame {frames_collected+1}: {led_points_3d}")
            frames_collected += 1
            time.sleep(0.2)
        else:
            time.sleep(0.1)

    # Save the collected list
    with open(save_path, "wb") as f:
        pickle.dump(led_points_cam_list, f)
    print(f"Saved {len(led_points_cam_list)} frames to {save_path}")

if __name__ == "__main__":
    collect_led_points_cam_list(num_frames=10)