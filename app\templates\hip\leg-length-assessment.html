<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="/static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Final Reduction/ Leg Length Assessment</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center align-items-start">
                                        <div class="col-6">
                                            <div class="grid">
                                                <img src="../static/images/hip-bone/hipfinalImage.jpeg" class="img-fluid" alt="">
                                            </div>
                                        </div>
                                        <div class="col-6">
											<style>
												body {
													font-family: Arial, sans-serif;
													color: white;
													background-color: black;
													display: flex;
													justify-content: center;
													align-items: center;
													height: 100vh;
													margin: 0;
												}
												.container {
													display: grid;
													grid-template-columns: 0.8fr 0.4fr 0.4fr; /* Reducing second and third column size */
													gap: 10px;
													text-align: center;
													width: 100%;
												}
												.header {
													grid-column: span 3;
													font-weight: bold;
													text-align: center; /* Center the text */
													font-size: 20px; /* Optional: Adjust font size */
													padding: 10px;
													color: white;
													background-color: black;
													border-radius: 5px;
												}
												.label, .value {
													padding: 10px;
													border: 2px solid white;
													border-radius: 5px;
												}
												.label {
													background-color: transparent;
												}
												.value {
													background-color: black;
													display: flex; /* Flexbox to center content */
													justify-content: center; /* Centers horizontally */
													align-items: center; /* Centers vertically */
													font-weight: bold;
												}
												.decreased {
													color: lightblue;
													font-weight: bold;
												}
											</style>
	
											<div id="container" class="container">
												<div class="header ">Actual vs Planned Values </div>
												<div class="label">Label</div>
												<div class="label">Actual</div>
												<div class="label">Planned</div>

												<!-- Dynamic data will be injected here -->
											</div>
											<script>
												async function loadData() {
													try {
														// Fetch data from the FastAPI server
														const response = await fetch('http://127.0.0.1:8000/values');
														const data = await response.json();

														// Get the container to insert the rows
														const container = document.getElementById('container');

														// Loop through the data and dynamically create rows
														let rowCount = 0;
														for (const key in data) {
															if (data.hasOwnProperty(key)) {
																const value = data[key];

																// Add label, actual, and planned values
																const labelDiv = document.createElement('div');
																labelDiv.classList.add('label');
																labelDiv.textContent = key;

																const actualDiv = document.createElement('div');
																actualDiv.classList.add('value');
																actualDiv.textContent = value.actual;

																const plannedDiv = document.createElement('div');
																plannedDiv.classList.add('value');
																plannedDiv.textContent = value.planned;

																container.appendChild(labelDiv);
																container.appendChild(actualDiv);
																container.appendChild(plannedDiv);

																// Limit to 8 rows
																rowCount++;
																if (rowCount === 8) {
																	break;
																}
															}
														}
													} catch (error) {
														console.error('Error loading data:', error);
													}
												}

												loadData();
											</script>
										</div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
<!--                                    <div class="btn">-->
<!--                                        RECORD-->
<!--                                    </div>-->
                                    <div class="blank"></div>
<!--                                    <div class="btn">-->
<!--&lt;!&ndash;                                        <a id="backBtn" href="TP2_marking.html"><span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back</a>&ndash;&gt;-->
<!--                                    </div>-->
                                    <div class="btn">
                                        <a id="nextBtn" href="../landing-page.html"><span class="mr-20">Finish</span><img src="../static/images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                               <a id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
                            </li>
                           <li class="footer_btn_three">
								<a id="powerbuttonLink">
									<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
								</a>
								<script>
									document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
										event.preventDefault();  // Prevent default anchor behavior
										window.location.href = '/';  // Redirect to root path
									});
								</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>
</html>