import numpy as np
from xarm.wrapper import XArmAPI
from scipy.stats.qmc import LatinHypercube
import time
import cv2

# Connect to xArm6
arm = XArmAPI('*************')
arm.connect()
arm.motion_enable(enable=True)
arm.set_mode(0)
arm.set_state(0)


def move_to_position(x, y, z, roll, pitch, yaw, speed=80):
    """Move robot arm to the specified position and check success."""
    code = arm.set_position(x=x, y=y, z=z, roll=roll, pitch=pitch, yaw=yaw, speed=speed, mvacc=75, wait=True)
    return code == 0


# Define cube parameters
center_x, center_y, center_z = 0, -500, 200
center_roll, center_pitch, center_yaw = 180, 0, 0
half_range = 300  # 600mm total, so ±300mm
angle_variation = 10  # ±10° for roll, pitch, yaw

# Define corner positions
corners = [
    (center_x - half_range, center_y - half_range, center_z - half_range),
    (center_x + half_range, center_y - half_range, center_z - half_range),
    (center_x - half_range, center_y + half_range, center_z - half_range),
    (center_x + half_range, center_y + half_range, center_z - half_range),
    (center_x - half_range, center_y - half_range, center_z + half_range),
    (center_x + half_range, center_y - half_range, center_z + half_range),
    (center_x - half_range, center_y + half_range, center_z + half_range),
    (center_x + half_range, center_y + half_range, center_z + half_range)
]

# Move to each corner to verify the workspace
print("Checking cube corner positions...")
for i, (x, y, z) in enumerate(corners):
    print(f"Moving to corner {i + 1}: X={x}, Y={y}, Z={z}")
    if move_to_position(x, y, z, center_roll, center_pitch, center_yaw):
        print(f"Reached corner {i + 1} successfully.")
    else:
        print(f"Failed to reach corner {i + 1}!")
    time.sleep(2)

# Generate LHS samples
num_samples = 10
lhs = LatinHypercube(d=6).random(n=num_samples)
lower_bounds = np.array([center_x, center_y, center_z, center_roll, center_pitch, center_yaw]) - np.array(
    [half_range] * 3 + [angle_variation] * 3)
upper_bounds = np.array([center_x, center_y, center_z, center_roll, center_pitch, center_yaw]) + np.array(
    [half_range] * 3 + [angle_variation] * 3)
sampled_points = lower_bounds + lhs * (upper_bounds - lower_bounds)


# Stereo vision setup
def capture_stereo_images():
    """Simulate stereo image capture and LED detection."""
    left_camera = cv2.VideoCapture(0)
    right_camera = cv2.VideoCapture(1)

    ret_left, frame_left = left_camera.read()
    ret_right, frame_right = right_camera.read()

    left_camera.release()
    right_camera.release()

    if ret_left and ret_right:
        return frame_left, frame_right
    else:
        return None, None


print("Starting Latin Hypercube Sampling...")
with open('LED_data_6ft_x.txt', 'a') as file:
    for i, point in enumerate(sampled_points):
        x, y, z, roll, pitch, yaw = point
        print(f"Moving to LHS Point {i + 1}: X={x}, Y={y}, Z={z}")
        if move_to_position(x, y, z, roll, pitch, yaw):
            print(f"Reached Point {i + 1}, capturing LED data...")

            # Capture stereo images
            img_left, img_right = capture_stereo_images()
            if img_left is not None and img_right is not None:
                # Simulate LED detection
                led_x, led_y, led_z = np.random.uniform(-5, 5, 3)  # Replace with actual stereo vision processing
                file.write(f'LED {led_x, led_y, led_z}   robot ({x}, {y}, {z})\n')
                print(f"Saved LED data for Point {i + 1}")
            else:
                print("Failed to capture stereo images.")
        else:
            print(f"Failed to reach Point {i + 1}!")
        time.sleep(2)

# Return robot to center position
print("Returning to center position...")
move_to_position(center_x, center_y, center_z, center_roll, center_pitch, center_yaw)

# Disconnect
arm.disconnect()
