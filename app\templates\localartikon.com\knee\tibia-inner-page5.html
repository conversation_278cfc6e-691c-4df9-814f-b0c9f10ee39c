<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Artikon</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="../css/bootstrap.min.css">
	<link rel="stylesheet" type="text/css" href="../css/style.css">
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul>
                                <li>Distal Femur Cut Verification</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../images/profile.png" /></span>Johen Mark
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="Bone-box-style pb-80">
                                                <div class="Bone-text">
                                                    <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                        <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                        <span id="textbox1">0.0</span>
                                                        <sup class="fs-14">0</sup>
                                                        <span class="input-btn-text text-white">ext</span>
                                                        <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                    </div>
                                                </div> 
                                                <img src="../images/tibia/flexion.jpg" class="img-fluid  distal-femur-cut-img" alt="">
                                                <div class="Bone-text bottom-input">
                                                    <div class="d-flex align-items-center">
                                                        <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between br-rounded">
                                                            <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                            <span id="textbox2">0.0</span>
                                                            <sup class="fs-14">0</sup>
                                                            <span class="input-btn-text text-white">mm</span>
                                                            <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                        </div>
                                                        <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between bl-rounded">
                                                            <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                            <span id="textbox3">0.0</span>
                                                            <sup class="fs-14">0</sup>
                                                            <span class="input-btn-text text-white">mm</span>
                                                            <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                        </div>
                                                    </div>
                                                </div> 
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="Bone-box-style pb-80">
                                                <div class="Bone-text">
                                                    <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                        <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                        <span id="textbox4">0.0</span>
                                                        <sup class="fs-14">0</sup>
                                                        <span class="input-btn-text text-white">ext</span>
                                                        <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                    </div>
                                                </div> 
                                                <img src="../images/tibia/varus-valgus.jpg" class="img-fluid distal-femur-cut-img" alt="">
                                                <div class="Bone-text bottom-input">
                                                    <div class="d-flex align-items-center">
                                                        <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between br-rounded">
                                                            <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                            <span id="textbox5">0.0</span>
                                                            <sup class="fs-14">0</sup>
                                                            <span class="input-btn-text text-white">mm</span>
                                                            <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                        </div>
                                                        <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between bl-rounded">
                                                            <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                            <span id="textbox6">0.0</span>
                                                            <sup class="fs-14">0</sup>
                                                            <span class="input-btn-text text-white">mm</span>
                                                            <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                        </div>
                                                    </div>
                                                </div> 
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="note">
                                                <p class="note_txt">Notes:</p>
                                                <p>Place cut verification guide over resectea surface and Push button to record</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="btn">
                                        Auto Align
                                    </div>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a href="tibia-distal-femur-cut.html">
                                            <span class="mr-20"><img src="../images/left-arrow.png" /></span>Back
                                        </a>
                                    </div>
                                    <div class="btn">
                                        <a href="tibia-anterior-resection.html"><span class="mr-20">Next</span><img src="../images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#">
                                    <span><img src="../images/home.png" /></span>Main Menu
                                </a>
                            </li>
                            <li class="footer_btn_three">
                                <a href="#">
                                    <span><img src="../images/union.png" /></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../js/common.js"></script>
<script type="text/javascript">

    let socket;
    let delay = 1000; // Initial delay of 1 second
    let startFetching = false; // Flag to indicate when to start fetching data

    function startServer(index) {

        if (!socket || socket.readyState !== WebSocket.OPEN) {
        // socket = new WebSocket('ws://**************:8080/' + index);
        socket = new WebSocket('ws://**************:8080/' + index);

        socket.onopen = function(event) {
          console.log('Socket opened for ' + index);
        };

        socket.onmessage = function(event) {
            const values = event.data.split(',');
            const abbreviation = values[0];
            const data = values[1];
        
            if (startFetching && ['tibia-inner-page5-1', 'tibia-inner-page5-2', 
                                  'tibia-inner-page5-3', 'tibia-inner-page5-4', 
                                  'tibia-inner-page5-5', 'tibia-inner-page5-6'].includes(abbreviation)) 
            {
                
                setTimeout(() => {

                    // Get the current URL
                    var currentUrl = window.location.href;
                    // Remove the page name from the URL
                    var projectUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);
                    if (abbreviation == 'tibia-inner-page5-6') {
                        window.location.href = projectUrl + 'tibia-anterior-resection.html';
                    }
                
                    // Original points assiging code
                    if (abbreviation === 'tibia-inner-page5-1') {
                        $('#textbox1').text(data);
                    } else if (abbreviation === 'tibia-inner-page5-2') {
                        $('#textbox2').text(data);
                    } else if (abbreviation === 'tibia-inner-page5-3') {
                        $('#textbox3').text(data);
                    } else if (abbreviation === 'tibia-inner-page5-4') {
                        $('#textbox4').text(data);
                    } else if (abbreviation === 'tibia-inner-page5-5') {
                        $('#textbox5').text(data);
                    } else if (abbreviation === 'tibia-inner-page5-6') {
                        $('#textbox6').text(data);
                    } 
                }, delay);

                delay += 1000; // Increment the delay for the next message
            }

            if (abbreviation === 'tibia-distal-femur-cut-3') {
                startFetching = true; // Set flag to start fetching data
                console.log("start fetching");
            }
        };

        socket.onerror = function(error) {
          console.error('Socket error:', error);
        };
      }
    };

    // Click event for minus button
    $('.minus-button').on('click', function() {
        var span = $(this).closest('.input-btn').find('span').first();
        var currentValue = parseFloat(span.text());
        if (!isNaN(currentValue)) {
            span.text((currentValue - 0.5).toFixed(1));
        }
    });

    // Click event for plus button
    $('.plus-button').on('click', function() {
        var span = $(this).closest('.input-btn').find('span').first();
        var currentValue = parseFloat(span.text());
        if (!isNaN(currentValue)) {
            span.text((currentValue + 0.5).toFixed(1));
        }
    });

    // Start the server automatically on page load
     window.onload = function() {
      startServer('Distal_Femur_Cut_Verification');
    };

</script>
</html>