import numpy as np

def fit_plane(point_cloud):
    """
    input
        point_cloud : list of xyz values　numpy.array
    output
        plane_v : (normal vector of the best fit plane)
        com : center of mass
    """

    com = np.sum(point_cloud, axis=0) / len(point_cloud)
    # calculate the center of mass
    q = point_cloud - com
    # move the com to the origin and translate all the points (use numpy broadcasting)
    Q = np.dot(q.T, q)
    # calculate 3x3 matrix. The inner product returns total sum of 3x3 matrix
    la, vectors = np.linalg.eig(Q)
    # Calculate eigenvalues and eigenvectors
    plane_v = vectors.T[np.argmin(la)]
    # Extract the eigenvector of the minimum eigenvalue

    return plane_v, com

# driver code
point_cloud = np.array(
    [
        # [491.57414294, 478.36344737, 330.494887  ],
        # [492.71010316, 478.06105514, 328.4178777 ],
        # [493.19972092, 477.74553665, 327.98383648],
        # [493.64982419, 477.38127549, 325.39846691],
        # [493.46315718, 477.1180931,  325.23284912],
        # [493.42829747, 477.06644465, 324.3705764 ],
        # [493.17252411, 476.69655633, 322.87663835],
        # [492.38264835, 476.18051927, 320.46791037],
        # [491.1147751,  476.10930259, 320.21609757],
        # [489.96249669, 475.60456244, 318.68075322],
        # [488.68486165, 475.52141543, 317.37369956],
        # [486.35250519, 475.89175767, 318.16025368],
        # [484.86759965, 476.04390916, 319.21711877],
        # [483.29161235, 476.56497112, 322.48258715],
        # [482.93218361, 477.04675936, 324.31126917],
        # [483.27350993, 477.77014428, 327.74083129],
        # [484.20219846, 478.18111898, 329.2902227 ],
        # [484.95980409, 478.3544214, 329.55156078],
        [499.09312726, 501.57795314, 328.57800323],		
        [505.66707563, 498.33288213, 330.54854752],		
        [497.40240745, 490.28314562, 330.18453617]		
        ,		
                
        [500.41589256, 501.32043404, 327.39779179],		
        [506.98447402, 498.00296913, 329.18166668],		
        [498.71074699, 489.92989486, 328.86839815]		
        ,		
                
        [500.46672618, 501.01563309, 324.6912552],		
        [507.17162247, 497.89892112, 326.08683883],		
        [499.22517356, 489.63900676, 325.77481951]		
        ,		
                
        [500.1973407 , 500.71019659, 322.64225066],		
        [506.9914064 , 497.7740663 , 323.78949654],		
        [499.19887949, 489.31056316, 324.24201766]		
        ,		
                
        [500.74948775, 500.07020357, 320.16214891],		
        [507.43138118, 496.88894746, 321.70273107],		
        [499.35350082, 488.70831448, 322.61960293]		
        ,		
                
        [500.12107676, 499.51788923, 318.74519836],		
        [506.84207309, 496.41971078, 319.88732264],		
        [498.75084579, 488.22949421, 320.71214724]		
        ,		
                
        [500.52494022, 498.83141529, 317.18068558],		
        [506.94918894, 495.42499986, 319.38056873],		
        [498.55400918, 487.66765011, 319.13667514]		
        ,		
                
        [499.61679888, 498.24352755, 314.78578715],		
        [506.0926964 , 494.76330521, 316.31249292],		
        [497.4864473 , 487.15803605, 317.0563205 ]		
        ,		
                
        [499.20654447, 497.63785441, 313.71660163],		
        [505.45805144, 493.89333768, 316.02128925],		
        [496.5359261 , 486.605546  , 316.82385879]		
        ,		
                
        [471.19754459, 498.36918624, 325.03249401],		
        [477.90263452, 501.66444027, 325.72835732],		
        [479.41133414, 490.37243536, 327.56218535],				
        [470.21705663, 498.06903163, 322.66072587],		
        [476.94651329, 501.24030244, 323.86043728],		
        [478.2327052 , 489.85923975, 324.97839369],
        [471.05870679, 497.71590812, 317.70288444],		
        [478.01781889, 500.46643497, 318.19810337],		
        [478.61421822, 489.11913992, 320.59811987],		
        [472.87837653, 496.75724675, 312.88870641],		
        [479.76201506, 499.57821991, 313.95855455],		
        [480.41692608, 488.2525434 , 316.28549839],		
        [473.52591846, 494.94006543, 308.97646136],		
        [480.18061784, 498.37891627, 309.96678688],		
        [481.68100941, 487.30341414, 313.42041109]		
    ]
)

plane_v, com = fit_plane(point_cloud)
print('plane_v = ', plane_v)
print('center of mass = ', com)
print('\nHardcoded base = [488.41700569, 475.94188174, 325.51975659]')