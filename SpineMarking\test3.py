import numpy as np


def bezier_point(C, t):
    """Calculate the point on a Bézier curve given control points and t."""
    C0, C1, C2 = C
    P_t = (1 - t) ** 2 * np.array(C0) + 2 * (1 - t) * t * np.array(C1) + t ** 2 * np.array(C2)
    return P_t


def bezier_tangent(C, t):
    """Calculate the tangent vector to the Bézier curve at t."""
    C0, C1, C2 = C
    tangent = 2 * (1 - t) * (np.array(C1) - np.array(C0)) + 2 * t * (np.array(C2) - np.array(C1))
    return tangent / np.linalg.norm(tangent)


def bezier_normal(tangent):
    """Calculate the normal vector given the tangent."""
    normal = np.array([-tangent[1], tangent[0], tangent[2]])  # 3D normal assuming tangent is in x, y plane
    return normal / np.linalg.norm(normal)


# Control points
C_values = np.array([[-19.996576175000005, -56.205279282404206, -1388.8983723723723],
                     [-19.996576175000005, -60.6827567598817, -1419.8675915915915],
                     [-19.996576175000005, -65.53335736048228, -1447.8518258258257]])

# A = np.array([-19.996576175000005, -75.98080480792976, -1393.0027267267265])
# B = np.array([-19.996576175000005, -102.09942342654836, -1391.8833573573572])


A = np.array([-19.996576175000005, -103.21879279591775, -1385.5402642642641])
B = np.array([-19.996576175000005, -80.0851591622841, -1385.9133873873873])
vector = np.subtract(B, A)
# Calculate tangent and normal at t = 0.5 (midpoint of the curve)
t_values = [0, 0.5, 1]

# Calculate tangents and normals for each t value
tangents = []
normals = []
points = []

for t in t_values:
    point_on_curve = bezier_point(C_values, t)
    tangent = bezier_tangent(C_values, t)
    normal = bezier_normal(tangent)

    # Store results
    points.append(point_on_curve)
    tangents.append(tangent)
    normals.append(normal)

# Display the results
for i, t in enumerate(t_values):
    print(f"At t = {t}:")
    print(f"Point on curve: {points[i]}")
    print(f"Tangent: {tangents[i]}")
    print(f"Normal: {normals[i]}\n")


    def angle_between_vectors(v1, v2):
        # Project both vectors into the xz-plane (ignore the y-component)
        # v1_proj = np.array([v1[0], 0, v1[2]])
        # v2_proj = np.array([v2[0], 0, v2[2]])

        # Calculate the dot product and magnitudes
        dot_product = np.dot(v1, v2)
        mag_v1 = np.linalg.norm(v1)
        mag_v2 = np.linalg.norm(v2)

        # Calculate the angle in radians and convert to degrees
        angle_rad = np.arccos(dot_product / (mag_v1 * mag_v2))
        angle_deg = np.degrees(angle_rad)

        return angle_deg


    Angle = angle_between_vectors(normals[i], vector)

    # Output results
    print(f'{normal[i]}  angle {Angle}')




import numpy as np

# Define the two points
point1 = np.array([-3.5636437425675638, -42.25791441753936, -1407.5402642642641])
point2 = np.array([-23.00044554436937, -66.28886937249432, -1397.5402642642641])

# Calculate the Euclidean distance
distance = np.linalg.norm(point2 - point1)
print(f'distnace {distance}')

