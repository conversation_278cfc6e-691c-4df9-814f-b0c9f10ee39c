<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
    	<link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>

<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span
                                class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">07/01/2025</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut resect_proximal">
                        <div class="profile">
                            <ul class="border-0">
                                <li>Assess initial leg alignment and balance</li>
								<li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center asses-box">
                                        <div class="col-8">
                                            <div class="row">
                                                <div class="col-6 position-relative">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00000.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00001.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00002.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00003.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00004.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00005.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00006.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00007.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00008.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00009.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00010.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00011.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00012.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00013.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00014.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00015.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00016.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00017.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00018.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00019.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00020.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00021.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00022.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00023.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00024.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00025.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00026.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00027.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00028.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00029.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00030.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00031.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00032.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00033.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00034.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00035.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00036.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00037.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00038.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00039.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00040.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00041.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00042.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00043.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00044.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00045.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00046.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00047.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00048.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00049.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00050.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00051.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00052.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00053.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00054.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00055.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00056.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00057.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00058.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00059.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00060.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00061.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00062.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00063.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00064.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00065.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00066.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00067.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00068.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00069.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00070.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00071.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00072.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00073.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00074.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00075.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00076.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00077.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00078.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00079.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00080.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00081.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00082.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00083.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00084.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00085.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00086.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00087.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00088.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00089.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00090.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00091.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00092.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00093.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00094.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00095.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00096.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00097.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00098.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00099.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00100.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00101.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00102.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00103.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00104.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00105.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00106.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00107.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00108.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00109.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00110.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00111.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00112.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00113.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00114.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00115.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00116.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00117.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00118.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/front/LEG_BONE_V2_FRONT__00119.jpg" alt="" class="d-none">
                                                    <div class="Bone-text d-none">
                                                        <button class="input-btn d-flex align-items-center justify-content-between">
                                                            <span id="textbox1"></span>
                                                            <sup class="fs-14">0</sup>
                                                            <span class="input-btn-text">flex</span>
                                                        </button>
                                                        <!-- <div class="input-group">
                                                            <input type="text" placeholder="3.0&deg" aria-label="" aria-describedby="" id="textbox1">
                                                            <span class="input-group-text">var</span>
                                                        </div> -->
                                                    </div>
                                                </div>
                                                <div class="col-6 position-relative">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00000.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00001.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00002.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00003.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00004.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00005.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00006.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00007.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00008.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00009.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00010.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00011.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00012.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00013.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00014.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00015.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00016.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00017.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00018.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00019.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00020.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00021.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00022.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00023.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00024.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00025.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00026.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00027.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00028.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00029.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00030.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00031.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00032.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00033.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00034.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00035.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00036.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00037.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00038.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00039.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00040.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00041.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00042.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00043.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00044.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00045.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00046.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00047.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00048.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00049.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00050.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00051.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00052.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00053.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00054.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00055.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00056.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00057.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00058.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00059.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00060.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00061.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00062.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00063.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00064.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00065.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00066.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00067.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00068.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00069.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00070.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00071.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00072.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00073.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00074.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00075.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00076.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00077.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00078.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00079.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00080.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00081.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00082.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00083.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00084.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00085.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00086.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00087.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00088.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00089.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00090.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00091.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00092.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00093.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00094.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00095.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00096.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00097.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00098.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00099.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00100.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00101.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00102.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00103.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00104.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00105.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00106.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00107.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00108.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00109.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00110.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00111.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00112.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00113.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00114.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00115.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00116.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00117.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00118.jpg" alt="" class="d-none">
													<img src="../static/images/tibia/post-op-kinematics/side/LEG_BONE_V2_SIDE__00119.jpg" alt="" class="d-none">
                                                    <div class="Bone-text d-none">
                                                            <button class="input-btn d-flex align-items-center justify-content-between">
                                                                <span id="textbox2"></span>
                                                                <sup class="fs-14">0</sup>
                                                                <span class="input-btn-text">flex</span>
                                                            </button>
                                                        <!-- <div class="input-group">
                                                            <input type="text" placeholder="0.0&deg" aria-label="" aria-describedby="" id="textbox2">
                                                            <span class="input-group-text">flex</span>
                                                        </div> -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-4">
										<canvas id="safeZoneChart" width="100" height="100"></canvas>
										<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
										<script>
										  const ctx = document.getElementById('safeZoneChart').getContext('2d');

										  // Initialize the chart
										  const chart = new Chart(ctx, {
											type: 'scatter',
											data: {
											  datasets: [
												// Dataset for left-side values (Points)
												{
												  label: 'Left Side Points',
												  data: [], // Left side values
												  backgroundColor: 'blue',
												  borderColor: 'blue',
												  borderWidth: 2,
												  pointRadius: 3,
												  fill: false,
												  showLine: false, // Disable line for the points
												},
												// Dataset for left-side lines (Lines)
												{
												  //label: 'Left Side Line',
												  label: '',
												  data: [], // Left side values
												  backgroundColor: 'blue',
												  borderColor: 'blue',
												  borderWidth: 2,
												  fill: false, // Disable filling
												  showLine: true, // Enable line
												  tension: 0, // Optional: controls the line smoothness
												},
												// Dataset for right-side values (Points)
												{
												  label: 'Right Side Points',
												  data: [], // Right side values
												  backgroundColor: '#b7410e',
												  borderColor: '#b7410e',
												  borderWidth: 2,
												  pointRadius: 3,
												  fill: false,
												  showLine: false, // Disable line for the points
												},
												// Dataset for right-side lines (Lines)
												{
												  //label: 'Right Side Line',
												  label: '',
												  data: [], // Right side values
												  backgroundColor: '#b7410e',
												  borderColor: '#b7410e',
												  borderWidth: 2,
												  fill: false, // Disable filling
												  showLine: true, // Enable line
												  tension: 0, // Optional: controls the line smoothness
												},
											  ],
											},
											options: {
											  scales: {
												x: {
												  title: {
													display: true,
													text: '    L            Gaps (mm)               M   ',
													color: 'white',
												  },
												  ticks: {
													color: '#fff',
													stepSize: 5,
												  },
												  min: -30,
												  max: 30,
												  grid: {
													drawOnChartArea: true,
													drawTicks: true,
												  },
												},
												y: {
												  title: {
													display: true,
													text: 'ROM (degrees)',
													color: 'white',
												  },
												  ticks: {
													color: '#fff',
													stepSize: 5,
												  },
												  position: 'center',
												  min: -10,
												  max: 120,
												  grid: {
													drawTicks: true,
												  },
												},
											  },
											  plugins: {
												legend: {
												  position: 'bottom',
												  labels: {
													color: 'white',
												  },
												},
											  },
											  responsive: true,
											  maintainAspectRatio: true,
											  layout: {
												padding: {
												  left: 5,
												  right: 5,
												  top: 5,
												  bottom: 5,
												},
											  },
											  interaction: {
												mode: 'nearest',
												axis: 'xy',
											  },
											},
										  });

										  // Function to fetch data from the backend
										  //async function fetchData() {
											//const response = await fetch('http://127.0.0.1:8000/get-graph_data');
											//const data = await response.json();
											//return data;
										  //}
											/*
										  // Function to update the chart
										  async function updateChart() {
											const data = await fetchData();
											 
											// Clear previous data in the chart datasets
											chart.data.datasets[0].data = []; // Clear left side points
											chart.data.datasets[1].data = []; // Clear left side line
											chart.data.datasets[2].data = []; // Clear right side points
											chart.data.datasets[3].data = []; // Clear right side line

											// Prepare left and right data arrays
											let leftData = [];
											let rightData = [];

											// Populate new data for left side and right side
											data.left.forEach(item => {
												leftData.push({ x: item.x, y: item.y });
											});

											data.right.forEach(item => {
												rightData.push({ x: item.x, y: item.y });
											});

											// Sort data by x value to avoid spider web effect
											leftData.sort((a, b) => a.y - b.y);
											rightData.sort((a, b) => a.y - b.y);

											// Update chart datasets with sorted data
											chart.data.datasets[0].data = leftData; // Left side points
											chart.data.datasets[1].data = leftData; // Left side line (same as points)
											chart.data.datasets[2].data = rightData; // Right side points
											chart.data.datasets[3].data = rightData; // Right side line (same as points)
											// Update the chart
											chart.update();
										  }
										  // Trigger chart resize when window is resized
										  window.addEventListener('resize', function () {
											chart.resize();
										  });
										  // Update chart every second
										  setInterval(updateChart, 1000);*/
										</script>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
						
						<div class="bottom_btn">
											<div class="blank blank-none"></div>
											<div class="btn">
												<a id="backBtn" href="tkr-screen-2.html">
													<span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back
												</a>
											</div>
											<div class="btn">
												<a id="nextBtn" href="remove-primary-implants.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
											</div>
									</div>
									
                        <ul class="align-items-center">
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li>
                                <ul class="bottom-icon">
                                    <li><img src="../static/images/icon/icon-1.png" class="img-fluid icon-img" alt=""></li>
                                    <li><img src="../static/images/icon/icon-2.png" class="img-fluid icon-img active" alt=""></li>
                                    <li><img src="../static/images/icon/icon-3.png" class="img-fluid icon-img" alt=""></li>
                                    <li><img src="../static/images/icon/icon-4.png" class="img-fluid icon-img" alt=""></li>
                                </ul>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../static/images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img
                                                src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#" id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
                            </li>
							<li class="footer_btn_three">
								<a href="#" id="powerbuttonLink">
									<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
								</a>
								<script>
									document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
										event.preventDefault();  // Prevent default anchor behavior
										window.location.href = '/';  // Redirect to root path
									});
								</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>
<script>
let socket;
var reconnectInterval = 1000; // Time in ms to wait before attempting reconnection
var lastShownFrontBone = null; // Track last shown image for ROM1
var lastShownSideBone = null;  // Track last shown image for ROM2

function processData(jsonData) {
    if (jsonData.Graph_data) {
        const filteredResults = jsonData.Graph_data.filtered_results;
        const angles = jsonData.angles;
        console.log(filteredResults);
        console.log(angles);

        // Update textboxes with angles
        if (angles) {
            const alignmentText = document.getElementById('textbox1');
            const anteversionText = document.getElementById('textbox2');

            if (alignmentText) {
                alignmentText.innerHTML = angles.Alignement + '°';
            }
            if (anteversionText) {
                anteversionText.innerHTML = angles.Anteversion + '°';
            }

            // Show the textboxes
            $('.Bone-text').removeClass('d-none');
        }

        // Prepare data for the graph
        let leftData = [];
        let rightData = [];

        // Process filtered results
        Object.entries(filteredResults).forEach(([angle, values]) => {
            const y = parseFloat(angle);
            leftData.push({ x: parseFloat(values.left), y: y });
            rightData.push({ x: parseFloat(values.right), y: y });
        });

        // Sort data points by y value
        leftData.sort((a, b) => a.y - b.y);
        rightData.sort((a, b) => a.y - b.y);

        // Update chart datasets
        chart.data.datasets[0].data = leftData;
        chart.data.datasets[1].data = leftData;
        chart.data.datasets[2].data = rightData;
        chart.data.datasets[3].data = rightData;
        chart.update();

        // Update bone images
        const frontImageId = `LEG_BONE_V2_FRONT__${String(angles.Alignement).padStart(5, '0')}`;
        const sideImageId = `LEG_BONE_V2_SIDE__${String(angles.Anteversion).padStart(5, '0')}`;

        if (lastShownFrontBone) {
            $(`img[src*="${lastShownFrontBone}"]`).addClass('d-none');
        }
        $(`img[src*="${frontImageId}"]`).removeClass('d-none');
        lastShownFrontBone = frontImageId;

        if (lastShownSideBone) {
            $(`img[src*="${lastShownSideBone}"]`).addClass('d-none');
        }
        $(`img[src*="${sideImageId}"]`).removeClass('d-none');
        lastShownSideBone = sideImageId;
    }
}

function initWebSocket() {
    if (!socket || socket.readyState === WebSocket.CLOSED || socket.readyState === WebSocket.CLOSING) {
        const currentUrl = window.location.href;
        const pageName = "rev" + currentUrl.substring(currentUrl.lastIndexOf('/') + 1).split('?')[0];
        socket = new WebSocket('ws://127.0.0.1:8000/ws');

        socket.onopen = function () {
            console.log('Socket opened');
            socket.send(JSON.stringify({ file: pageName }));
        };

        socket.onmessage = function (event) {
            const values = JSON.parse(event.data);
            console.log(event.data);
            processData(values);
        };

        socket.onerror = function (error) {
            console.error('Socket error:', error);
        };

        socket.onclose = function (event) {
            console.log('Socket closed:', event);
            setTimeout(function () {
                console.log('Attempting to reconnect...');
                initWebSocket();
            }, reconnectInterval);
        };
    }
}

// Start WebSocket connection when the window loads
window.onload = function () {
    initWebSocket();
};
	
	function handleNavigation(event) {
    event.preventDefault();
    const targetUrl = event.currentTarget.href;

    if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({ file: " " }));
        socket.addEventListener('close', function () {
            window.location.href = targetUrl;
        }, { once: true });

        socket.close();
    } else {
        window.location.href = targetUrl;
    }
};

    document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
    document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);
	
</script>





</html>