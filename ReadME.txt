pointcloudforhipfeaturematching.py
cloudtestprogram.py
--------------------------------------------------
generate and plot the point cloud for hip


testModel.py
--------------------------------------------------
test model fitting in Hip





cloudtestprogram3D1.py
cloudtestprogram3D1_pyvista.py
---------------------------------
3D point cloud mesh





http://127.0.0.1:8000/hip/handle-position.html
Hip anteversion and inclination angles
http://127.0.0.1:8000/hip/final-cup-position.html
Hip anteversion and inclination angles




