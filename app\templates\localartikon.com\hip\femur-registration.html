<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="../css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="../css/style.css">
</head>

<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span
                                    class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Femur Registration</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../images/profile.png" /></span>Johen Mark
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center align-items-start">
                                        <div class="col-6">
                                            <div class="grid">
                                                <img src="../images/hip-bone/Femur_Registratio.jpg" class="img-fluid"
                                                    alt="">
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="note new-note text-start">
                                                <p class="note_txt">Notes:</p>
                                                <ol>
                                                    <li>Place Tracker Post On Trochanter And Record Intitial Leg Length
                                                    </li>
                                                </ol>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="btn green_border pointer_bg">
                                        <span class="mr-20"><img src="../images/icon/check.png" /></span>Pointer
                                    </div>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a href="system-setup.html"><span class="mr-20"><img
                                                    src="../images/left-arrow.png" /></span>Back</a>
                                    </div>
                                    <div class="btn">
                                        <a href="pelvis-registration.html"><span class="mr-20">Next</span><img
                                                src="../images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img
                                                src="../images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#">
                                    <span><img src="../images/home.png" /></span>Main Menu
                                </a>
                            </li>
                            <li class="footer_btn_three">
                                <a href="#">
                                    <span><img src="../images/union.png" /></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../js/common.js"></script>

<script type="text/javascript">

    let socket;
    let delay = 2000; // Initial delay of 3 second

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            // socket = new WebSocket('ws://**************:8080/' + index);
            socket = new WebSocket('ws://**************:8080/' + index);

            socket.onopen = function (event) {
                console.log('Socket opened for ' + index);
            };

            socket.onmessage = function (event) {
                console.log('Received message:', event.data);
                const values = event.data.split(',');
                const abbreviation = values[0];

                setTimeout(() => {
                    // Get the current URL
                    var currentUrl = window.location.href;
                    // Remove the page name from the URL
                    var projectUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);
                    window.location.href = projectUrl + 'pelvis-registration.html';
                }, delay);
                delay += 2000; // Increment the delay for the next message
            }

            if (abbreviation === 'ACT') {
                startFetching = true; // Set flag to start fetching data
                console.log("start fetching");
            }
        };

        socket.onerror = function (error) {
            console.error('Socket error:', error);
        };
    }

    // new js ends

    // Start the server automatically on page load
    window.onload = function () {
        startServer('Femur_Registration');
    };

</script>

</html>