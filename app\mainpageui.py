from xarm.wrapper import XArmAP<PERSON>

# Connect to xArm6
arm = XArmAPI('*************')  # Replace with your xArm6 IP

# Get the current mode
mode = arm.mode  # Read the mode property
state = arm.state  # Read the mode property
print(f"Current Mode: {mode} state {state}")
# if mode == 2:
#     print("Already in Manual Mode. No action needed.")
# else:
#     arm.set_mode(2)  # Set to manual mode
#     arm.set_state(2)  # Ensure arm is in idle state after switching
#     print("Switched to Manual Mode.")

# Disconnect
arm.disconnect()
