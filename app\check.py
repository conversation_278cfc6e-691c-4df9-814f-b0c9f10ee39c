import pickle
import numpy as np
import yaml
from scipy.linalg import inv
from numpy import dot, eye
import park_martin
import sys

np.set_printoptions(linewidth=300, suppress=True)

# Mock park_martin module and calibrate function
class park_martin:
    @staticmethod
    def calibrate(A, B):
        # Dummy implementation: return identity rotation and zero translation
        return np.eye(3), np.zeros(3)

# Load data
if sys.version_info.major > 2:
    rob_pose_list = pickle.load(open('data/pose_list.dump', 'rb'), encoding='latin1')
else:
    rob_pose_list = pickle.load(open('data/pose_list.dump', 'rb'))

# Placeholder: list of detected 3D LED positions in camera frame for each image
# Replace these with your actual detected LED positions for each image
led_points_cam_list = [
    np.array([[0.0, 0.0, 0.0], [0.0, 1.0, 0.0], [1.0, 0.0, 0.0]]),
    np.array([[0.1, 0.0, 0.0], [0.1, 1.0, 0.0], [1.1, 0.0, 0.0]]),
    np.array([[0.0, 0.1, 0.0], [0.0, 1.1, 0.0], [1.0, 0.1, 0.0]]),
    np.array([[0.0, 0.0, 0.1], [0.0, 1.0, 0.1], [1.0, 0.0, 0.1]]),
    np.array([[0.1, 0.1, 0.0], [0.1, 1.1, 0.0], [1.1, 0.1, 0.0]])
]

obj_pose_list = []

def compute_marker_pose(led_points):
    # led_points: np.array shape (3,3)
    centroid = np.mean(led_points, axis=0)
    x_axis = led_points[1] - led_points[0]
    x_axis /= np.linalg.norm(x_axis)
    temp_vec = led_points[2] - led_points[0]
    z_axis = np.cross(x_axis, temp_vec)
    z_axis /= np.linalg.norm(z_axis)
    y_axis = np.cross(z_axis, x_axis)
    y_axis /= np.linalg.norm(y_axis)
    R = np.stack((x_axis, y_axis, z_axis), axis=1)
    T = eye(4)
    T[0:3, 0:3] = R
    T[0:3, 3] = centroid
    return T

for led_points in led_points_cam_list:
    object_pose = compute_marker_pose(led_points)
    obj_pose_list.append(object_pose)

A, B = [], []
for i in range(1, len(obj_pose_list)):
    p = rob_pose_list[i-1], obj_pose_list[i-1]
    n = rob_pose_list[i], obj_pose_list[i]
    A.append(dot(inv(p[0]), n[0]))
    B.append(dot(inv(p[1]), n[1]))

# Transformation to LED tracker in robot gripper
X = eye(4)
Rx, tx = park_martin.calibrate(A, B)
X[0:3, 0:3] = Rx
X[0:3, 3] = tx

print("X: ")
print(X)

print("For validation. Printing transformations from the robot base to the camera")
print("All the transformations should be quite similar")

for i in range(len(obj_pose_list)):
    rob = rob_pose_list[i]
    obj = obj_pose_list[i]
    tmp = dot(rob, dot(X, inv(obj)))
    print(tmp)

# Here I just pick one, but maybe some average can be used instead
rob = rob_pose_list[0]
obj = obj_pose_list[0]
cam_pose = dot(dot(rob, X), inv(obj))

cam = {'rotation': cam_pose[0:3, 0:3].tolist(),
       'translation': cam_pose[0:3, 3].tolist()}

with open('camera.yaml', 'w') as fp:
    yaml.dump(cam, fp)

"""
Note:
- This code replaces the PnP step with direct computation of the marker pose from 3D LED points in the camera frame.
- Camera matrix and distortion coefficients are not used.
- Replace the simulated led_points_cam_list with your actual detected 3D LED points per image.