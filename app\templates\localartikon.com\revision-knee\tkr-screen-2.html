<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Artikon</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="../css/bootstrap.min.css">
	<link rel="stylesheet" type="text/css" href="../css/style.css">
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul>
                                <li>&nbsp</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../images/profile.png" /></span>Johen Mark
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div>
                                                <img src="../images/revision-tkr/validate-points/actual-image-to-be-used-1.jpg" class="img-fluid tkr-Bone-img" alt="">
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div>
                                                <img src="../images/revision-tkr/validate-points/actual-image-to-be-used-2.jpg" class="img-fluid tkr-Bone-img" alt="">
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div>
                                                <img src="../images/revision-tkr/validate-points/actual-image-to-be-used-3.jpg" class="img-fluid tkr-Bone-img" alt="">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-center pt-4">
                                        <p class="fs-18 mb-1">Offset from landmark</p>
                                        <div class="position-relative">
                                            <input type="text" class="input-text" placeholder="0.0" aria-label="" aria-describedby="" id="offsetval">
                                            <sup class="fs-14 deg-custom text-white">0</sup>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="blank blank-none"></div>
                                    <div class="btn">
                                        <a href="multiple-page.html">
                                            <span class="mr-20"><img src="../images/left-arrow.png" /></span>Back
                                        </a>
                                    </div>
                                    <div class="btn">
                                        <a href="inner-page6.html"><span class="mr-20">Next</span><img src="../images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul class="align-items-center">
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li>
                                <ul class="bottom-icon">
                                    <li><img src="../images/icon/icon-1.png" class="img-fluid icon-img" alt=""></li>
                                    <li><img src="../images/icon/icon-2.png" class="img-fluid icon-img active" alt=""></li>
                                    <li><img src="../images/icon/icon-3.png" class="img-fluid icon-img" alt=""></li>
                                    <li><img src="../images/icon/icon-4.png" class="img-fluid icon-img" alt=""></li>
                                </ul>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#">
                                    <span><img src="../images/home.png" /></span>Main Menu
                                </a>
                            </li>
                            <li class="footer_btn_three">
                                <a href="#">
                                    <span><img src="../images/union.png" /></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../js/common.js"></script>

<!-- new js -->

<script>

    let socket;
    let delay = 1000; // Initial delay of 1 second
    let startFetching = false; // Flag to indicate when to start fetching data

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
        // socket = new WebSocket('ws://**************:8080/' + index);
        socket = new WebSocket('ws://**************:8080/' + index);

        socket.onopen = function(event) {
          console.log('Socket opened for ' + index);
        };

        socket.onmessage = function(event) {
           
            const values = event.data.split(',');
            const abbreviation = values[0];
       
            if (startFetching && ['OFL'].includes(abbreviation)) {
                
                setTimeout(() => {
                    // Get the current URL
                    var currentUrl = window.location.href;
                    
                    // Remove the page name from the URL
                    var projectUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);
                    
                    if (abbreviation == 'OFL') {
                        window.location.href = projectUrl + 'inner-page6.html';
                    }

                    $("#offsetval").val(values[1]);
                    if(values[1] >= -0.4 && values[1] <= 0.4){
                        $("#offsetval").addClass('btn-success');
                    }else{
                        $("#offsetval").addClass('btn-danger');
                    }

                    // $("#offsetval").val(abbreviation);
                    // if(abbreviation >= -0.4 && abbreviation <= 0.4){
                    //     $("#offsetval").addClass('btn-success');
                    // }else{
                    //     $("#offsetval").addClass('btn-danger');
                    // }

                },delay);

                delay += 1000; // Increment the delay for the next message

            }
            if (abbreviation === 'ANC') {
                startFetching = true; // Set flag to start fetching data
            }

        };

        socket.onerror = function(error) {
          console.error('Socket error:', error);
        };
      }
    }
    
    // Start the server automatically on page load
    window.onload = function() {
      startServer('knee_validate');
    };

</script>




<!-- old js -->
<!-- <script type="text/javascript">

    let socket;

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
        // socket = new WebSocket('ws://**************:8080/' + index);
        socket = new WebSocket('ws://**************:8080/' + index);

        socket.onopen = function(event) {
          console.log('Socket opened for ' + index);
        };

        socket.onmessage = function(event) {
            console.log('Received message:', event.data);
            const values = event.data.split(',');
            $("#offsetval").val(values[1]);
            if(values[1] >= -0.4 && values[1] <= 0.4){
                $("#offsetval").addClass('btn-success');
            }else{
                $("#offsetval").addClass('btn-danger');
            }
        };

        socket.onerror = function(error) {
          console.error('Socket error:', error);
        };
      }
    };
    

    // Start the server automatically on page load
    window.onload = function() {
      startServer('revision_knee_validate');
    };

</script> -->

</html>