<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="/static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap system-setup-screen pelvis-registration-screen">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Pelvis Registration</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="grid mt-0">
                                                <img src="../static/images/hip-bone/supine.png" class="img-fluid camp-img" alt="">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a id="backBtn" href="femur-registration.html"><span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back</a>
                                    </div>
                                    <div class="btn">
                                        <a id="nextBtn" href="mid-axial-body-plane.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                    <div class="check_box">
						<div class="new">
							<form id="RegForm">
								<div class="form-group">
									<input type="radio" id="coupon_question1" name="plane" class="section-toggle AP" onclick="handleRadioChange()" />
									<label for="coupon_question1" class="rounded-circle">Anatomical plane</label>
								</div>
								<div class="form-group">
									<input type="radio" id="coupon_question2" name="plane" class="section-toggle FP" onclick="handleRadioChange()" />
									<label for="coupon_question2" class="rounded-circle">Functional plane</label>
								</div>
								<div class="form-group">
									<input type="radio" id="coupon_question3" name="plane" class="section-toggle BOTH" onclick="handleRadioChange()" />
									<label for="coupon_question3" class="rounded-circle">Both (anatomical+functional)</label>
								</div>
							</form>
						</div>
					</div>

					<script>
					function handleRadioChange() {
						const selectedOption = document.querySelector('input[name="plane"]:checked');
						
						if (selectedOption) {
							const selectedValue = selectedOption.id; // Get the selected radio button ID

							// Save the selected option to localStorage
							localStorage.setItem('selectedPlane', selectedValue);
							const queryString = '?selected=' + selectedValue;
							// Redirect based on the selected option
							let redirectUrl = '';
							if (selectedValue === 'coupon_question1') {
								redirectUrl = 'pelvis-registration-2.html' + queryString;
							} else if (selectedValue === 'coupon_question2') {
								redirectUrl = 'mid-axial-body-plane.html' + queryString;
							} else if (selectedValue === 'coupon_question3') {
								redirectUrl = 'pelvis-registration-2.html' + queryString;
							}

							if (redirectUrl) {
								window.location.href = redirectUrl; // Perform the redirect
							}
						}
					}

					// Load the selected option from localStorage when the page loads
					window.onload = function() {
						const savedValue = localStorage.getItem('selectedPlane');
						if (savedValue) {
							const radioButton = document.getElementById(savedValue);
							if (radioButton) {
								radioButton.checked = true; // Check the saved radio button
							}
						}
					};
					</script>

					
					
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
							   <a id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
							</li>
						   <li class="footer_btn_three">
								<a id="powerbuttonLink">
									<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
								</a>
								<script>
									document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
										event.preventDefault();  // Prevent default anchor behavior
										window.location.href = '/';  // Redirect to root path
									});
								</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>

<script type="text/javascript">

    let socket;
    let delay = 1000; // Initial delay of 1 second
    let startFetching = false; // Flag to indicate when to start fetching data

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
        // socket = new WebSocket('ws://**************:8080/' + index);
       socket = new WebSocket('ws://127.0.0.1:8000/ws'); // Update this URL for production

        socket.onopen = function() {
            console.log('✅ WebSocket opened for ' + index);
            socket.send(JSON.stringify({ file: window.location.pathname.split('/').pop() }));
        };






        socket.onmessage = function(event) {
            console.log('Received message:', event.data);
            const values = event.data.split(',');
            const abbreviation = values[0];
            const data = values[1]; 

            if (startFetching && ['AP','FP','BOTH','ALR'].includes(abbreviation)) 
            {
                setTimeout(() => {

                    // Get the current URL
                    var currentUrl = window.location.href;
                    // Remove the page name from the URL
                    var projectUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);
                    if (abbreviation == 'ALR') {
                        window.location.href = projectUrl + 'pelvis-registration-2.html' + queryString;;
                    }                  

                    // Hide all sections
                    $('.section-toggle.' + abbreviation).attr( 'checked', true );

                    if(abbreviation == 'LAT'){
                        $('.supine-img').addClass('d-none');
                        $('.LAT').removeClass('d-none');
                    }
                        
                }, delay);

                delay += 1000; // Increment the delay for the next message
            }

     
            if (abbreviation === 'ACT') {
                startFetching = true; // Set flag to start fetching data
                console.log("start fetching");
            }
        };

        socket.onerror = function(error) {
          console.error('Socket error:', error);
        };
      }
    };
	
	function handleNavigation(event) {
		event.preventDefault();
		const targetUrl = event.currentTarget.href;

		if (socket && socket.readyState === WebSocket.OPEN) {
			socket.send(JSON.stringify({ file: " " }));
			socket.addEventListener('close', function () {
				window.location.href = targetUrl;
			}, { once: true });

			socket.close();
		} else {
			window.location.href = targetUrl;
		}
	};
	document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
    document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);
    // Camera functionality
    document.addEventListener('DOMContentLoaded', function() {
        const cameraButton = document.querySelector('.footer_btn_one .btn.first');
        const cameraModal = document.getElementById('cameraModal');
        const closeCamera = document.querySelector('.close-camera');
        const videoStream = document.getElementById('videoStream');
        let mediaStream = null;

        // Function to start the camera
        async function startCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });
                mediaStream = stream;
                videoStream.srcObject = stream;
                videoStream.play();
            } catch (err) {
                console.error('Error accessing camera:', err);
            }
        }

        // Function to stop the camera
        function stopCamera() {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                videoStream.srcObject = null;
                mediaStream = null;
            }
        }

        // Open camera modal
        cameraButton.addEventListener('click', function(e) {
            e.preventDefault();
            cameraModal.style.display = 'block';
            startCamera();
        });

        // Close camera modal
        closeCamera.addEventListener('click', function() {
            cameraModal.style.display = 'none';
            stopCamera();
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === cameraModal) {
                cameraModal.style.display = 'none';
                stopCamera();
            }
        });
    });

    // Start the server automatically on page load
    window.onload = function() {
      startServer('Pelvis_Registration');
    };

</script>

</html>