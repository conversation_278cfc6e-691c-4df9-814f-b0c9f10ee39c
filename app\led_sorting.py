if code == 0:
    print("Robot has reached the desired location.")
    while True:
        if page == "uni-knee/femur-distal-femur-cut.html":
            print("page is femur-distal-femur-cut.html")
            return
        left_camera_detections, right_camera_detections = await marking.handle_irq_average(n=10)
        print(f'{len(right_camera_detections)}    {len(left_camera_detections)}')
        if not (len(right_camera_detections) == 12 and len(left_camera_detections) == 12):
            await asyncio.sleep(0.1)
            continue
            # return

        # detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
        # detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)
        detections_right = right_camera_detections
        detections_left = left_camera_detections

        # Robot tracker plane
        detections_right = sorted(detections_right, key=lambda x: x[0])
        detections_left = sorted(detections_left, key=lambda x: x[0])

        robot_detection_right = detections_right[:3]
        robot_detection_left = detections_left[:3]

        robot = TriangleTracker(
            robot_detection_right, robot_detection_left
        )

        Robot_points = robot.getLEDcordinates()
        print(f'R_points   {Robot_points}')

        if  Robot_points[1][1] >= Robot_points[0][1]:
            tracker_plane = [Robot_points[2], Robot_points[0], Robot_points[1]]
        else:
            tracker_plane = [Robot_points[2], Robot_points[1], Robot_points[0]]

        # remove the robot points
        detections_right = detections_right[3:]
        detections_left = detections_left[3:]
        
        # Tool tracker plane
        detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
        detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)

        tool_detection_right = detections_right[:3]
        tool_detection_left = detections_left[:3]

        tool = TriangleTracker(
            tool_detection_right,
            tool_detection_left,
            "TOOL"
        )

        Tool_points = tool.getLEDcordinates()

        if Tool_points[1][1] > Tool_points[0][1]:
            tool_plane = [Tool_points[2], Tool_points[0], Tool_points[1]]
        else:
            tool_plane = [Tool_points[2], Tool_points[1], Tool_points[0]]
        
        # remove the tool points
        detections_right = detections_right[3:]
        detections_left = detections_left[3:]

        # Femur tracker plane
        femure_detection_right = detections_right[:3]
        femure_detection_left = detections_left[:3]

        femure = TriangleTracker(
            femure_detection_right,
            femure_detection_left,
            "F",
        )

        Femur_points = femure.getLEDcordinates()

        if Femur_points[1][1] >= Femur_points[0][1]:
            femur_plane = [Femur_points[2], Femur_points[0], Femur_points[1]]
        else:
            femur_plane = [Femur_points[2], Femur_points[1], Femur_points[0]]

        # Tibia tracker plane
        tibia_detection_right = detections_right[3:6]
        tibia_detection_left = detections_left[3:6]

        tibia = TriangleTracker(
            tibia_detection_right,
            tibia_detection_left,
            "T",
        )                               

        Tibia_points = tibia.getLEDcordinates()
                    
        if Tibia_points[1][1] >= Tibia_points[0][1]:
            tibia_plane = [Tibia_points[2], Tibia_points[0], Tibia_points[1]]
        else:
            tibia_plane = [Tibia_points[2], Tibia_points[1], Tibia_points[0]]
        

        print(f'Robot_points   {Robot_points}')
        print(f'Femur_points   {Femur_points}')
        print(f'Tool_points   {Tool_points}')
        print(f'Tibia_points   {Tibia_points}')
        target_tool_point = Tool_points[2]
        print("Selected tool point:", target_tool_point)
        break       

else:
    print("Failed to move the robot to the desired position.")

