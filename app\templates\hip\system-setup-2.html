<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="/static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
    <style>
        .camera-portal {
            object-fit: cover;
            border-radius: 8px;
            background: #000;
            display: none;
            max-width: 891px;
            max-height: 488px;
        }
        .camera-portal.show-video {
            display: block;
        }
        .camp-img.camera-portal {
            width: 626px;
            height: 440px;
            aspect-ratio: 313/220;
            margin: auto;
        }
        .camp-img {
            width: 626px;
            height: 440px;
            aspect-ratio: 313/220;
            margin: auto;
            object-fit: cover;
            border-radius: 8px;
        }
        .camera-modal {
            display: none;
            position: fixed;
            z-index: 1050;
            left: 50%;
            top: 50%;
            width: 600px;
            height: 450px;
            transform: translate(-50%, -50%);
            background-color: rgba(15, 15, 15, 0.97);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.6);
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            border: 2px solid #2F2B41;
        }

        .camera-modal.show {
            display: block;
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }

        .camera-modal-content {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .close-camera {
            position: absolute;
            right: 12px;
            top: 12px;
            color: #fff;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease, transform 0.2s ease;
        }

        .close-camera:hover {
            color: #ff5555;
            transform: scale(1.2);
        }

        .camera-modal h2 {
            color: #FFFFFF;
            font-size: 16px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .camera-modal p {
            color: #2F2B41;
            font-size: 13px;
            margin-bottom: 10px;
            font-weight: 500;
        }

        #videoStream {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
            background: #000;
            display: none; /* Initially hidden */
        }

        /* Custom styles for the main camera feed */
        .main-camera-container {
            width: 626px;
            height: 440px;
            border-radius: 8px;
            overflow: hidden;
            background: #000;
            margin: auto;
        }

        #liveCameraFeed {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        @media (max-width: 768px) {
            .camera-modal {
                width: 90%;
                height: 400px;
            }
            .camera-modal h2 {
                font-size: 14px;
            }
            .camera-modal p {
                font-size: 12px;
            }
        }

        /* Raw CSS for video stream */
        .video-container {
            width: 626px;
            height: 440px;
            position: relative;
            margin: auto;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #fff;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
        }

        #liveCameraFeed {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block !important;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
        }

        .grid {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
        }

        .video-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            z-index: 2;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap system-setup-screen">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>System Setup</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>
                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';
                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="grid">
                                                <div class="video-container">
                                                    <img id="videoStream" style="width: 100%; height: 100%; object-fit: cover;" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a id="backBtn" href="operating-positio-screen.html"><span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back</a>
                                    </div>
                                    <div class="btn">
                                        <a id="nextBtn" href="femur-registration.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>Place Pins In Iliac Crest. Position The Camera To Ensure Crest Tracker Is Aligned With Camera Fov</p>
                        </div>
                    </div>
                    <div class="check_box">
                        <div class="new">
                             <form id="trackerForm">
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question1" class="section-toggle APT"/>
                                    <label for="coupon_question1">Activate Pelvis Tracker</label>
                                </div>
                                <!--<div class="form-group">
                                    <input type="checkbox" id="coupon_question2" class="section-toggle AFT"/>
                                    <label for="coupon_question2">Activate Femur Tracker</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question3" class="section-toggle ACT"/>
                                    <label for="coupon_question3">Activate Crest Tracker</label>
                                </div>-->
                            </form>
                        </div>
                    </div>
					<script>
						document.getElementById('trackerForm').addEventListener('submit', function(event) {
							event.preventDefault(); // Prevent the default form submission

							// Retrieve the values of the checkboxes
							const coupon1Checked = document.getElementById('coupon_question1').checked;
							const coupon2Checked = document.getElementById('coupon_question2').checked;
							const coupon3Checked = document.getElementById('coupon_question3').checked;

							// You can now do something with these values
							console.log('Activate parvis Tracker:', coupon1Checked);
							console.log('Activate Femur Tracker:', coupon2Checked);
							console.log('Activate Crest Tracker:', coupon3Checked);
							// Example of processing data or sending it to the server
							// fetch('/your-endpoint', {
							//     method: 'POST',
							//     body: JSON.stringify({
							//         coupon_question1: coupon1Checked,
							//         coupon_question2: coupon2Checked,
							//         coupon_question3: coupon3Checked
							//     }),
							//     headers: {
							//         'Content-Type': 'application/json'
							//     }
							// });
						});
					</script>

                    <div class="supine-img">
                        <img src="../static/images/hip-bone/latera.png" class="img-fluid" alt="">
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
							   <a id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
							</li>
						   <li class="footer_btn_three">
								<a id="powerbuttonLink">
									<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
								</a>
								<script>
									document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
										event.preventDefault();  // Prevent default anchor behavior
										window.location.href = '/';  // Redirect to root path
									});
								</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>
<script type="text/javascript">
    let socket;
    let delay = 100;
    let activeWebSocket = null;
    let autoConnectAttempts = 0;
    const MAX_AUTO_CONNECT_ATTEMPTS = 3;

    function connectWebSocket() {
        if (activeWebSocket) {
            activeWebSocket.close();
        }

        activeWebSocket = new WebSocket('ws://127.0.0.1:8000/ws');

        activeWebSocket.onopen = function() {
            console.log('WebSocket connected');
            activeWebSocket.send(JSON.stringify({ file: "video" }));
            // Show the video stream container when connection is established
            const videoStream = document.getElementById('videoStream');
            if (videoStream) {
                videoStream.style.display = 'block';
            }
        };

        activeWebSocket.onmessage = function(event) {
            const imageElement = document.getElementById('videoStream');
            if (imageElement) {
                imageElement.src = 'data:image/jpeg;base64,' + event.data;
            }
        };

        activeWebSocket.onerror = function(error) {
            console.error('WebSocket error:', error);
            if (autoConnectAttempts < MAX_AUTO_CONNECT_ATTEMPTS) {
                setTimeout(() => {
                    autoConnectAttempts++;
                    connectWebSocket();
                }, 1000);
            }
        };

        activeWebSocket.onclose = function() {
            console.log('WebSocket closed');
            activeWebSocket = null;
            if (autoConnectAttempts < MAX_AUTO_CONNECT_ATTEMPTS) {
                setTimeout(() => {
                    autoConnectAttempts++;
                    connectWebSocket();
                }, 1000);
            }
        };
    }

    // Attach event listeners for camera functionality
    document.addEventListener('DOMContentLoaded', function() {
        const videoStream = document.getElementById('videoStream');

        // Try to connect automatically when page loads
        connectWebSocket();

        // Camera button as fallback
        document.querySelector('.btn.first').addEventListener('click', function(e) {
            e.preventDefault();
            if (!activeWebSocket) {
                autoConnectAttempts = 0; // Reset attempts counter on manual click
                connectWebSocket();
            } else {
                // If WebSocket is already connected, disconnect it and hide video
                activeWebSocket.close();
                activeWebSocket = null;
                if (videoStream) {
                    videoStream.style.display = 'none';
                }
            }
        });

        // Clean up WebSocket on page unload
        window.onbeforeunload = function() {
            if (activeWebSocket) {
                activeWebSocket.close();
            }
        };
    });

    // Tracker WebSocket functionality
    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            socket = new WebSocket('ws://127.0.0.1:8000/ws');

            socket.onopen = function(event) {
                console.log('✅ Socket opened for systemsetup');
                const currentUrl = window.location.href;
                let pageName = currentUrl.split('/').pop().split('?')[0] || "index";
                const fileInfo = JSON.stringify({ file: pageName });
                socket.send(fileInfo);
                console.log("📤 Sent file name:", fileInfo);
            };

            socket.onmessage = function(event) {
                console.log('📩 Received message:', event.data);
                const values = event.data.split(',');
                const abbreviation = values[0];

                if (abbreviation === 'APT') {
                    console.log('APT data received');
                    const checkbox = document.getElementById('coupon_question1');
                    if (checkbox) {
                        // Force the checkbox to be checked
                        checkbox.checked = true;
                        checkbox.setAttribute('checked', 'checked');

                        // Trigger change event
                        const event = new Event('change', {
                            bubbles: true,
                            cancelable: true
                        });
                        checkbox.dispatchEvent(event);

                        console.log('Checkbox state:', checkbox.checked);

                        // Only redirect if checkbox is actually checked
                        setTimeout(() => {
                            if (checkbox.checked) {
                                console.log('Redirecting...');
                                var currentUrl = window.location.href;
                                var projectUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);
                                window.location.href = projectUrl + 'femur-registration.html';
                            } else {
                                console.log('Checkbox not checked, retrying...');
                                checkbox.checked = true;
                                checkbox.dispatchEvent(event);
                            }
                        }, 1000);
                        socket.close();

                    } else {
                        console.error('Checkbox not found');
                    }
                }
            };

            socket.onerror = function(error) {
                console.error('❌ Socket error:', error);
            };

            socket.onclose = function(event) {
                console.log('⚠️ Socket closed:', event);
                setTimeout(() => startServer(index), delay);

            };
        }
    }

    window.onload = function() {
        startServer();
    };
</script>
</html>