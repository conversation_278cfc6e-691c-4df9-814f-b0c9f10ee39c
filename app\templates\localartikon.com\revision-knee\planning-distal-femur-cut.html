<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Artikon</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="../css/bootstrap.min.css">
	<link rel="stylesheet" type="text/css" href="../css/style.css">
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                         <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut resect_proximal">
                        <div class="profile">
                            <ul>
                                <li>Planning Distal Femur Cut</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../images/profile.png" /></span>Johen Mark
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center justify-content-center">
                                        <div class="col-8">
                                            <div class="new-grid-box">
                                                <div class="row">
                                                    <div class="col-3">
                                                        <div class="grid-box">
                                                            <button class="input-btn d-flex align-items-center justify-content-center">
                                                                <span id="textbox1">90.0</span>
                                                                <sup class="fs-14">0</sup>
                                                            </button>
                                                            <div class="position-relative">
                                                                <div class="grid-box-img">
                                                                    <img src="../images/revision-tkr/planning-distal-femur-cut/femur_cut_plan1.jpg" class="img-fluid" alt="">
                                                                </div>
                                                                <div class="txt txt-top left">
                                                                    M
                                                                </div>
                                                                <div class="txt txt-top right">
                                                                    L
                                                                </div>
                                                            </div>
                                                            <div class="sub-grid ps-0 pe-0">
                                                                <div class="depth">
                                                                    <div class="depth_bg">Varus / Valgus</div>
                                                                    <div class="depth_btn">
                                                                        <ul>
                                                                            <li>0<span class="small-txt">0</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <h5>Align to</h5>
                                                        <div class="row justify-content-center g-2">
                                                            <div class="col-6">
                                                                <div class="grid-box-center-img">
                                                                    <form>
                                                                        <input class="checkbox" type="checkbox" id="" name="" value="">
                                                                        <label for="" class="dashed-border label pb-2">Mechenical Axis</label>
                                                                    </form>
                                                                    <div class="grid-box-img">
                                                                        <img src="../images/revision-tkr/planning-distal-femur-cut/femur_cut_planning_front.jpg" class="img-fluid" alt="">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-6">
                                                                <div class="grid-box-center-img">
                                                                    <form>
                                                                        <input class="checkbox" type="checkbox" id="" name="" value="">
                                                                        <label for="" class="solid-border label pb-2" >Im Canal</label>
                                                                        <span class="dashed-border"></span>
                                                                    </form>
                                                                    <div class="grid-box-img">
                                                                        <img src="../images/revision-tkr/planning-distal-femur-cut/femur_cut_planning_side.jpg" class="img-fluid" alt="">
                                                                    </div> 
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-3">
                                                        <div class="grid-box">
                                                            <button class="input-btn d-flex align-items-center justify-content-center">
                                                                <span id="textbox1">90.0</span>
                                                                <sup class="fs-14">0</sup>
                                                            </button>
                                                            <div class="grid-box-img">
                                                                <img src="../images/revision-tkr/planning-distal-femur-cut/femur_cut_planning_side-1.jpg" class="img-fluid" alt="">
                                                            </div>
                                                            <div class="sub-grid ps-0 pe-0">
                                                                <div class="depth">
                                                                    <div class="depth_bg">Varus / Valgus</div>
                                                                    <div class="depth_btn">
                                                                        <ul>
                                                                            <li>0<span class="small-txt">0</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="note">
                                                <p class="note_txt">Notes:</p>
                                                <p>Press Pedal to move Robotic guide to cut plane</p>
                                            </div>
                                        </div>
                                        <!-- <div class="col-6">
                                            <div class="depth btn_box">
                                                <div class="depth_bg">Depth</div>
                                                <div class="depth_btn">
                                                    <ul>
                                                        <li><img src="../images/icon/minus.png" /></li>
                                                        <li>9<span class="small-txt">mm</span></li>
                                                        <li><img src="../images/icon/plus.png" /></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div> -->
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="blank blank-none"></div>
                                    <div class="btn">
                                        <a href="femur-im-canal-ream.html">
                                            <span class="mr-20"><img src="../images/left-arrow.png" /></span>Back
                                        </a>
                                    </div>
                                    <div class="btn">
                                        <a href="guidance-distal-femur-cut.html"><span class="mr-20">Next</span><img src="../images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#">
                                    <span><img src="../images/home.png" /></span>Main Menu
                                </a>
                            </li>
                            <li class="footer_btn_three">
                                <a href="#">
                                    <span><img src="../images/union.png" /></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../js/common.js"></script>
<script>
    $(document).ready(function() {
        // Listen for change event on checkboxes
        $('.checkbox').change(function() {
            // Check if the checkbox is checked
            if ($(this).is(':checked')) {
                // Add the checked-color class to the parent element
                $(this).closest('.grid-box-center-img').addClass('checked-color');
            } else {
                // Remove the checked-color class from the parent element
                $(this).closest('.grid-box-center-img').removeClass('checked-color');
            }
        });
    });
</script>
</html>