import csv
import datetime
import json
import math
import os.path
import pickle
import queue
import threading
import time

import cv2
import pyvista as pv
import pyvistaqt as pvqt
import vtk
# from pythonProject.general.common.motor_control import start_motor, stop_motor, close_connection
from PyQt5 import QtWidgets  # or from PySide2 import QtCore
from PyQt5.QtCore import Qt
import numpy as np
import open3d as o3d
from sklearn.linear_model import LinearRegression
from stl import mesh

from pythonProject.general.camera.CameraParams_header import MV_FRAME_OUT_INFO_EX
from pythonProject.general.camera.CaptureCameraImage import DualCameraOperation
from pythonProject.general.camera.MvCameraControl_class import *
from pythonProject.general.common import calibration
from pythonProject.general.common import utils
from pythonProject.general.common.ReadSerialData import read_encoder_data
from pythonProject.general.common.tracker_object import TriangleTracker
from scipy.spatial import ConvexHull
from xarm.wrapper import XArmAPI

SUCCESS = 0
FAIL = -1
DEBUG = True
LOADIMAGESFROMLOCAL = False
if DEBUG:
    import inspect
from scipy.spatial import KDTree


def fit_plane(point_cloud):
    """
    input
        point_cloud : list of xyz values　numpy.array
    output
        plane_v : (normal vector of the best fit plane)
        com : center of mass
    """

    com = np.sum(point_cloud, axis=0) / len(point_cloud)
    # calculate the center of mass
    q = point_cloud - com
    # move the com to the origin and translate all the points (use numpy broadcasting)
    Q = np.dot(q.T, q)
    # calculate 3x3 matrix. The inner product returns total sum of 3x3 matrix
    la, vectors = np.linalg.eig(Q)
    # Calculate eigenvalues and eigenvectors
    plane_v = vectors.T[np.argmin(la)]
    # Extract the eigenvector of the minimum eigenvalue

    return plane_v, com


def load_point_cloud_from_csv(file_path):
    """
    Load point cloud data from a CSV file.

    Parameters:
        file_path (str): Path to the CSV file.

    Returns:
        numpy.ndarray: Array of points.
    """
    return np.genfromtxt(file_path, delimiter=',')  # Assuming CSV format with comma separator


def load_point_cloud_from_txt(file_path):
    """
    Load point cloud data from a TXT file.

    Parameters:
        file_path (str): Path to the TXT file.

    Returns:
        numpy.ndarray: Array of points.
    """
    points = []
    with open(file_path, 'r') as file:
        for line in file:
            line = line.strip()[1:-1]  # Remove square brackets
            point = np.array([float(i) for i in line.split()])  # Convert to float and create an array
            points.append(point)
    return np.array(points)


class HipMarking(DualCameraOperation):
    def __init__(self):
        self.framecount_text = None
        self.ring_points_actor = None
        self.target_color = None
        self.plotter = None
        self.femure_plane = None
        self.last_updated_point = None
        self.mesh = None
        self.transformed_points = None
        self.g_bExit = False
        super().__init__()
        self.dual_cam = DualCameraOperation()
        self.dual_cam.initialize_cameras()
        self.dual_cam.start_grabbing()
        self.Camera_left = self.dual_cam.cam
        self.Camera_right = self.dual_cam.cam2
        self.data_buf_left = (c_ubyte * self.dual_cam.nPayloadSize)()
        self.data_buf_left = byref(self.data_buf_left)
        self.data_buf_right = (c_ubyte * self.dual_cam.nPayloadSize2)()
        self.data_buf_right = byref(self.data_buf_right)
        self.nPayloadSize_left = self.dual_cam.nPayloadSize
        self.nPayloadSize_right = self.dual_cam.nPayloadSize2
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        self.point_queue = queue.Queue()
        self.running = False
        self.FemurePlane = []
        self.lock = threading.Lock()
        self.data_available = threading.Condition(self.lock)

    def __exit__(self):
        self.g_bExit = False
        self.dual_cam.stop_grabbing()

    def update_femure_plane(self, data):
        with self.data_available:
            self.FemurePlane = data  # Update the data
            self.data_available.notify_all()  # Notify any waiting threads that data is available

    def read_femure_plane(self):
        with self.data_available:
            while not self.FemurePlane:  # Wait until data is available
                self.data_available.wait()
            # Copy the data for thread-safe access
            data_copy = list(self.FemurePlane)
        return data_copy

    def save_images(self, img_left, img_right, counter=None):
        # Get the calling function name
        caller_function = inspect.stack()[1].function  # Name of the function that called this one

        # Define the save directory inside self.current_dir
        save_dir = os.path.join(self.current_dir, f"{caller_function}")

        # Create the directory if it doesn't exist
        os.makedirs(save_dir, exist_ok=True)

        # Modify file names based on the counter
        counter_suffix = f"_{counter}" if counter is not None else ""

        # Define the image file paths
        img_left_path = os.path.join(save_dir, f"img_left{counter_suffix}.jpg")
        img_right_path = os.path.join(save_dir, f"img_right{counter_suffix}.jpg")

        # Save images
        cv2.imwrite(img_left_path, img_left)
        cv2.imwrite(img_right_path, img_right)

        print(f"Images saved successfully in {save_dir}:\nLeft: {img_left_path}\nRight: {img_right_path}")

    def get_saved_images(self, counter=None):
        """
        Retrieve the saved images based on the calling function name and optional counter.

        :param counter: Optional counter to retrieve specific images
        :return: Tuple of (img_left, img_right) if images exist, else (None, None)
        """
        # Get the calling function name
        caller_function = inspect.stack()[1].function  # Name of the function that called this one

        # Define the save directory inside self.current_dir
        save_dir = os.path.join(self.current_dir, f"{caller_function}")

        # Modify file names based on the counter
        counter_suffix = f"_{counter}" if counter is not None else ""

        # Define the image file paths
        img_left_path = os.path.join(save_dir, f"img_left{counter_suffix}.jpg")
        img_right_path = os.path.join(save_dir, f"img_right{counter_suffix}.jpg")

        # Check if files exist before reading
        if not os.path.exists(img_left_path) or not os.path.exists(img_right_path):
            print(f"Images not found: {img_left_path}, {img_right_path}")
            return None, None

        # Load images
        img_left = cv2.imread(img_left_path,cv2.IMREAD_GRAYSCALE)
        img_right = cv2.imread(img_right_path,cv2.IMREAD_GRAYSCALE)

        print(f"Images retrieved successfully from {save_dir}:")
        return img_left, img_right

    # http://127.0.0.1:8000/hip/femur-registration.html

    def hip_system_setup(self):
        variable_dict = {}
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        point_reg = ['APT']
        framecount = 0
        # pointer_data = []
        while True:
            value = utils.checkLoopExitCondition()
            if value.lower() != 'system-setup':
                return
            if not LOADIMAGESFROMLOCAL:
                ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                                500)
                ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                                  stFrameInfo, 500)
                if ret == 0 and ret2 == 0:
                    img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                        (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                    img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                        (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
            else :
                img_left , img_right =  self.get_saved_images()
                print(f' {len(detections_right)} {len(detections_left)}  ')

            rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)

            try:
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 6 and 6 == len(detections_left):
                    detections_right = sorted(detections_right, key=lambda x: x[0])
                    detections_left = sorted(detections_left, key=lambda x: x[0])
                    # Extract first 3 coordinates
                    first_three_right = detections_right[:3]
                    first_three_left = detections_left[:3]
                    next_three_right = detections_right[-3:]
                    next_three_left = detections_left[-3:]

                    Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                     rec_img_left)
                    Pointer_traker.find_depth()
                    # pointer_tip_gcs = Pointer_traker.getpointertip()
                    pointer_tip_gcs = Pointer_traker.getStylusPoint()
                    # pointer_data.append(pointer_tip_gcs)
                    # if len(pointer_data) < 10:
                    #     continue
                    # pointer_tip_gcs = utils.filter_point_registration_data(pointer_data)
                    femure = TriangleTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right,
                                             rec_img_left)
                    femure.find_depth()
                    F_points = femure.getLEDcordinates()

                    Tracker_plane = [F_points[2], F_points[1], F_points[0]]
                    if framecount == 0:
                        utils.writeRegisteredPoint(point_reg[framecount])
                        print(f'Point registered {point_reg[framecount]}')
                        utils.play_notification_sound()

                        if DEBUG:
                            self.save_images(img_left,img_right)

                        pickle_path_TP = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                                      'act_variable.pickle')
                        os.makedirs(os.path.dirname(pickle_path_TP), exist_ok=True)
                        variable_memory = {'C': pointer_tip_gcs, 'Plane': Tracker_plane}
                        variable_dict[point_reg[framecount]] = variable_memory
                        with open(pickle_path_TP, 'wb') as handle:
                            pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
                        print("Pickle dump successful")
                    framecount += 1
                else:
                    print(f"no data[0x%x] {len(detections_right)} {len(detections_right)}" % ret)
                if framecount == 1:
                    break
            except Exception as e:
                print(f'Error {e}')
            self.dual_cam.stop_grabbing()
        return 0

    def hip_TP_variable_MarkingPipeline(self):
        variable_dict = {}
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        point_reg = ['TP']
        framecount = 0
        # pointer_data = []
        while True:
            value = utils.checkLoopExitCondition()
            if value.lower() != 'hip_tp_register':
                return
            if not LOADIMAGESFROMLOCAL:
                ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                                500)
                ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                                  stFrameInfo, 500)
                if ret == 0 and ret2 == 0:
                    img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                        (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                    img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                        (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
            else:
                img_left, img_right = self.get_saved_images()

            rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)


            try:
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 6 and 6 == len(detections_left):
                    detections_right = sorted(detections_right, key=lambda x: x[0])
                    detections_left = sorted(detections_left, key=lambda x: x[0])
                    # Extract first 3 coordinates
                    first_three_right = detections_right[:3]
                    first_three_left = detections_left[:3]
                    next_three_right = detections_right[-3:]
                    next_three_left = detections_left[-3:]

                    Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                     rec_img_left)
                    Pointer_traker.find_depth()
                    # pointer_tip_gcs = Pointer_traker.getpointertip()
                    pointer_tip_gcs = Pointer_traker.getStylusPoint()
                    # pointer_data.append(pointer_tip_gcs)
                    # if len(pointer_data) < 10:
                    #     continue
                    # pointer_tip_gcs = utils.filter_point_registration_data(pointer_data)
                    femure = TriangleTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right,
                                             rec_img_left)
                    femure.find_depth()
                    F_points = femure.getLEDcordinates()

                    Tracker_plane = [F_points[2], F_points[1], F_points[0]]
                    if framecount == 0:
                        utils.writeRegisteredPoint(point_reg[framecount])
                        print(f'Point registered {point_reg[framecount]}')
                        utils.play_notification_sound()
                        pickle_path_TP = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                                      'tp_variable.pickle')
                        os.makedirs(os.path.dirname(pickle_path_TP), exist_ok=True)
                        variable_memory = {'C': pointer_tip_gcs, 'Plane': Tracker_plane}
                        variable_dict[point_reg[framecount]] = variable_memory
                        with open(pickle_path_TP, 'wb') as handle:
                            pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
                        print("Pickle dump successful")

                        if DEBUG:
                            self.save_images(img_left,img_right)
                    framecount += 1
                else:
                    print("no data[0x%x]" % ret)
                if framecount == 1:
                    break
            except Exception as e:
                print(f'Error {e}')
        self.dual_cam.stop_grabbing()
        return 0

    # http://127.0.0.1:8000/hip/register-acetabulum.html?selectedPlane=coupon_question1
    # http://127.0.0.1:8000/hip/acetabulum-component-placement.html
    def hip_FivePoint_MarkingPipeline(self):
        try:
            pickle_path_TP = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                          'tp_variable.pickle')
            with open(pickle_path_TP, 'rb') as handle:
                TP = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")
        utils.writeRegisteredPoint('STEP0')

        variable_dict = {}
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        point_reg = ['FO', 'SU', 'CA', 'AT', 'PT']
        framecount = 0
        # pointer_data = []
        while True:
            value = utils.checkLoopExitCondition()
            if value.lower() != 'hip_FivePoint_register'.lower():
                return
            ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                            500)
            if not LOADIMAGESFROMLOCAL:
                ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                                500)
                ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                                  stFrameInfo, 500)
                if ret == 0 and ret2 == 0:
                    img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                        (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                    img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                        (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
            else:
                img_left, img_right = self.get_saved_images(counter=point_reg[framecount])

            rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)
            try:
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 6 and 6 == len(detections_left):
                    detections_right = sorted(detections_right, key=lambda x: x[0])
                    detections_left = sorted(detections_left, key=lambda x: x[0])
                    # Extract first 3 coordinates
                    first_three_right = detections_right[:3]
                    first_three_left = detections_left[:3]
                    next_three_right = detections_right[-3:]
                    next_three_left = detections_left[-3:]

                    Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                     rec_img_left)
                    Pointer_traker.find_depth()
                    pointer_tip_gcs = Pointer_traker.getStylusPoint()
                    # pointer_data.append(pointer_tip_gcs)
                    # if len(pointer_data) < 10:
                    #     continue
                    # pointer_tip_gcs = utils.filter_point_registration_data(pointer_data)

                    femure = TriangleTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right,
                                             rec_img_left)
                    femure.find_depth()
                    F_points = femure.getLEDcordinates()

                    Tracker_plane = [F_points[2], F_points[1], F_points[0]]

                    pointRegistered = utils.find_new_point_location(old_plane_points=Tracker_plane,
                                                                    new_plane_points=TP['TP']['Plane'],
                                                                    old_marked_point=pointer_tip_gcs)
                    variable_memory = {'C': pointRegistered, 'Plane': TP['TP']['Plane']}
                    print(f'pointRegistered {pointRegistered}   {pointer_tip_gcs}')
                    variable_dict[point_reg[framecount]] = variable_memory

                    utils.writeRegisteredPoint(point_reg[framecount])
                    print(f'Point registered {point_reg[framecount]}')
                    utils.play_notification_sound()
                    if DEBUG:
                        self.save_images(img_left, img_right,counter=point_reg[framecount])

                    # pointer_data.clear()
                    framecount = framecount + 1
                else:
                    print("no data[0x%x]" % ret)
                if framecount == 5:
                    print("All points registered.")
                    Hip_variable_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                                     'hip_variables.pickle')
                    os.makedirs(os.path.dirname(Hip_variable_path), exist_ok=True)
                    with open(Hip_variable_path, 'wb') as handle:
                        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
                    print("Pickle dump successful")
                    break
            except Exception as e:
                print(f'Error {e}')
        return 0

    def hipCollectRingPointCloudMarkingPipeline(self):
        try:
            pickle_path_TP = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                          'tp_variable.pickle')
            with open(pickle_path_TP, 'rb') as handle:
                TP = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")
        # utils.writeRegisteredPoint('STEP0')

        variable_dict = {}
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        point_reg = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']  #'K', 'L', 'M', 'N', 'O']
        framecount = 0
        # pointer_data = []

        obj_file_path = r'D:\SpineSurgery\pythonProject\app\static\images\hip-bone\3D\3Dhip\pelvis.obj'
        self.mesh = pv.read(obj_file_path)
        self.plotter = pvqt.BackgroundPlotter()

        Pos = (166.21689181742644, 460.3668000931386, -161.90325441730778)
        fpoint = (-66.02783205, -3.4104290000000006, -165.4661865)
        self.plotter.camera_position = [Pos, fpoint, (0, 1, 0)]  # Position the camera appropriately

        self.plotter.window().window().setMenuBar(None)
        self.plotter.window().window().findChild(QtWidgets.QToolBar)
        self.plotter.window().setWindowFlags(Qt.FramelessWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.plotter.set_background("black")
        self.plotter.show()
        self.plotter.window_size = (1913, 959)
        self.plotter.app_window.showMaximized()

        ring_points_hip = np.array([
            [-63.6134491, 29.2462463, -157.989151],  # SU
            [-30.7326965, 8.4291382, -133.5983887],  # AT
            [-15.77771, 17.0255318, -159.7479401],  # PT
            [-34.1718445, 9.8754272, - 128.4161987],
            [-41.6091003, 14.5238991, - 129.6721802],
            [-50.9671631, 17.8325405, - 132.4189758],
            [-59.7008972, 27.0550041, - 141.3748016],
            [-55.3602142, 25.9570751, - 170.2815552],
            [-44.6703949, 26.4036331, - 176.6385498],
            [-33.8677979, 25.1242809, - 177.1656494],
            [-25.1761932, 23.3919964, - 174.219986]

        ])
        initial_color = [240 / 255, 220 / 255, 170 / 255]  # RGB normalized for bone color
        colors = np.tile(initial_color, (self.mesh.n_points, 1))
        self.mesh["colors"] = colors

        # Add the femur model (mesh) with per-point colors
        self.plotter.add_mesh(self.mesh, scalars="colors", rgb=True, label="Femur Model")

        stl_color = np.array([64 / 255, 224 / 255, 208 / 255])
        stl_points = pv.PolyData(ring_points_hip)

        # Create a point cloud mesh for STL points and assign color
        stl_points["colors"] = np.tile(stl_color, (ring_points_hip.shape[0], 1))

        # Increase the size of the points by using 'glyph' to represent the points as spheres
        sphere_radius = 1  # Set sphere size to 20
        glyph = stl_points.glyph(geom=pv.Sphere(radius=sphere_radius), scale=True, orient=False)

        # Add the STL points to the plotter with the new size
        self.ring_points_actor = self.plotter.add_mesh(glyph, scalars="colors", rgb=True, label="Ring Points")
        self.framecount_text = self.plotter.add_text('Collect Rim Points: 0/10', position='upper_left', color='white',
                                                     font_size=10)

        try:
            Hip_variable_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                             'hip_variables.pickle')
            with open(Hip_variable_path, 'rb') as handle:
                hip_dict = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")

        camera_reference = np.array([
            hip_dict['FO']['C'],  #FO
            hip_dict['SU']['C'],  #SU
            hip_dict['CA']['C'],  #CA
            hip_dict['AT']['C'],  #AT
            hip_dict['PT']['C'],  #PT
        ])
        camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
        hip_STL_obj_points = np.array([
            [-45.5239716, -2.5871658, -154.1749573],  # F0
            [-63.6134491, 29.2462463, -157.989151],  # SU
            [-22.7026062, 2.6151695, -149.2989197],  # CA
            [-30.7326965, 8.4291382, -133.5983887],  # AT
            [-15.77771, 17.0255318, -159.7479401],  # PT
        ])
        # Calculate transformation matrix based on the camera reference
        self.transformation_matrix = self.calculate_transformation_matrix_with_scaling(camera_reference,
                                                                                       hip_STL_obj_points)
        while True:
            value = utils.checkLoopExitCondition()
            if value.lower() != 'hip_FivePoint_register'.lower():
                return
            if not LOADIMAGESFROMLOCAL:
                ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                                500)
                ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                                  stFrameInfo, 500)
                if ret == 0 and ret2 == 0:
                    img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                        (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                    img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                        (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
            else:
                img_left, img_right = self.get_saved_images(counter=point_reg[framecount])

            rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)
            try:
                detections_right = utils.find_contours_Kmeans(rec_img_right)
                detections_left = utils.find_contours_Kmeans(rec_img_left)
                if len(detections_right) == 6 and 6 == len(detections_left):
                    detections_right = sorted(detections_right, key=lambda x: x[0])
                    detections_left = sorted(detections_left, key=lambda x: x[0])
                    # Extract first 3 coordinates
                    first_three_right = detections_right[:3]
                    first_three_left = detections_left[:3]
                    next_three_right = detections_right[-3:]
                    next_three_left = detections_left[-3:]

                    Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                     rec_img_left)
                    Pointer_traker.find_depth()
                    pointer_tip_gcs = Pointer_traker.getStylusPoint()
                    # pointer_data.append(pointer_tip_gcs)
                    # if len(pointer_data) < 10:
                    #     continue
                    # pointer_tip_gcs = utils.filter_point_registration_data(pointer_data)

                    femure = TriangleTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right,
                                             rec_img_left)
                    femure.find_depth()
                    F_points = femure.getLEDcordinates()

                    Tracker_plane = [F_points[2], F_points[1], F_points[0]]
                    # self.femure_plane = Tracker_plane
                    pointRegistered = utils.find_new_point_location(old_plane_points=Tracker_plane,
                                                                    new_plane_points=TP['TP']['Plane'],
                                                                    old_marked_point=pointer_tip_gcs)

                    variable_memory = {'C': pointRegistered, 'Plane': TP['TP']['Plane']}
                    print(f'pointRegistered {pointRegistered}   {pointer_tip_gcs}')
                    print(f'Point registered {point_reg[framecount]}')
                    utils.play_notification_sound()

                    variable_dict[point_reg[framecount]] = variable_memory
                    data = f"{pointRegistered[0]},{pointRegistered[1]},{pointRegistered[2]}\n"
                    # utils.writeRegisteredPoint(point_reg[framecount])
                    framecount = framecount + 1
                    point = np.array(eval(data), dtype=np.float32)
                    point[1] = -point[1]

                    transformed_point = np.dot(self.transformation_matrix[:3, :3],
                                               point) + self.transformation_matrix[:3, 3]

                    single_point = np.array([transformed_point])
                    print(f'single_point {single_point}')
                    # single_point = np.array([point])

                    # 4. Create a PolyData object for the point
                    point_cloud = pv.PolyData(single_point)

                    # 5. Assign a color to the point (optional, you can choose your own color)
                    point_color = np.array([127 / 255, 1, 0])  # Example: Teal color
                    point_cloud["colors"] = np.tile(point_color, (single_point.shape[0], 1))

                    # 6. Optionally, increase the size of the point by using a glyph (small sphere)
                    glyph_radius = 1  # Adjust the radius as needed
                    glyph = point_cloud.glyph(geom=pv.Sphere(radius=glyph_radius), scale=False, orient=False)

                    # 7. Add the new point (or sphere) to the plotter
                    self.plotter.add_mesh(glyph, scalars="colors", rgb=True, label="Incoming Point")

                    # Update the framecount text at the top
                    if self.framecount_text:
                        # Clear the old text actor by removing it from the plotter
                        self.plotter.remove_actor(self.framecount_text)

                    # Add the updated framecount text
                    self.framecount_text = self.plotter.add_text(f'Rim Points: {framecount}/10',
                                                                 position='upper_left', color='white',
                                                                 font_size=10)

                    # Optionally, update the plotter
                    self.plotter.update()
                    self.plotter.app.processEvents()

                    # Update the mesh with the new colors

                    # pointer_data.clear()

                else:
                    print("no data[0x%x]" % ret)

                self.plotter.render()

                # Process events to keep the UI responsive
                self.plotter.app.processEvents()
                if framecount == 10:
                    print("All points registered.")
                    Hip_point_cloud_file = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                                        'RingPointCloud.pickle')
                    os.makedirs(os.path.dirname(Hip_point_cloud_file), exist_ok=True)
                    with open(Hip_point_cloud_file, 'wb') as handle:
                        pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
                    print("Pickle dump successful")
                    self.plotter.close()
                    break
            except Exception as e:
                print(f'Error {e}')
        return 0

    def hipCollectRingPointCloudplotter(self):
        obj_file_path = r'D:\SpineSurgery\pythonProject\app\static\images\hip-bone\3D\3Dhip\pelvis.obj'
        self.mesh = pv.read(obj_file_path)
        self.plotter = pvqt.BackgroundPlotter()

        Pos = (166.21689181742644, 460.3668000931386, -161.90325441730778)
        fpoint = (-66.02783205, -3.4104290000000006, -165.4661865)
        self.plotter.camera_position = [Pos, fpoint, (0, 1, 0)]  # Position the camera appropriately

        self.plotter.window().window().setMenuBar(None)
        self.plotter.window().window().findChild(QtWidgets.QToolBar)
        self.plotter.window().setWindowFlags(Qt.FramelessWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.plotter.set_background("black")
        self.plotter.show()
        self.plotter.window_size = (1913, 959)
        self.plotter.app_window.showMaximized()

        ring_points_hip = np.array([
            [-63.6134491, 29.2462463, -157.989151],  # SU
            [-30.7326965, 8.4291382, -133.5983887],  # AT
            [-15.77771, 17.0255318, -159.7479401],  # PT
            [-34.1718445, 9.8754272, - 128.4161987],
            [-41.6091003, 14.5238991, - 129.6721802],
            [-50.9671631, 17.8325405, - 132.4189758],
            [-59.7008972, 27.0550041, - 141.3748016],
            [-55.3602142, 25.9570751, - 170.2815552],
            [-44.6703949, 26.4036331, - 176.6385498],
            [-33.8677979, 25.1242809, - 177.1656494],
            [-25.1761932, 23.3919964, - 174.219986]

        ])
        initial_color = [240 / 255, 220 / 255, 170 / 255]  # RGB normalized for bone color
        colors = np.tile(initial_color, (self.mesh.n_points, 1))
        self.mesh["colors"] = colors

        # Add the femur model (mesh) with per-point colors
        self.plotter.add_mesh(self.mesh, scalars="colors", rgb=True, label="Femur Model")

        stl_color = np.array([64 / 255, 224 / 255, 208 / 255])
        stl_points = pv.PolyData(ring_points_hip)

        # Create a point cloud mesh for STL points and assign color
        stl_points["colors"] = np.tile(stl_color, (ring_points_hip.shape[0], 1))

        # Increase the size of the points by using 'glyph' to represent the points as spheres
        sphere_radius = 1  # Set sphere size to 20
        glyph = stl_points.glyph(geom=pv.Sphere(radius=sphere_radius), scale=True, orient=False)

        # Add the STL points to the plotter with the new size
        self.ring_points_actor = self.plotter.add_mesh(glyph, scalars="colors", rgb=True, label="Ring Points")
        self.framecount_text = self.plotter.add_text('Collect Rim Points: 0/10', position='upper_left', color='white',
                                                     font_size=10)
        # File path for the 3D femur model
        while True:
            # Check if femure_plane has been updated
            if self.femure_plane is not None:
                # print("femure_plane has been updated!")
                break  # Exit the loop when the condition is met

            # Log and wait for a short duration before checking again
            # print("Waiting for femure_plane to be updated...")
            time.sleep(1)  # Avoid tight looping and reduce CPU usage
            self.plotter.update()
            self.plotter.app.processEvents()
        try:
            Hip_variable_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                             'hip_variables.pickle')
            with open(Hip_variable_path, 'rb') as handle:
                hip_dict = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")

        FO = utils.find_new_point_location(old_plane_points=hip_dict['FO']['Plane'],
                                           new_plane_points=self.femure_plane,
                                           old_marked_point=hip_dict['FO']['C'])
        SU = utils.find_new_point_location(old_plane_points=hip_dict['SU']['Plane'],
                                           new_plane_points=self.femure_plane,
                                           old_marked_point=hip_dict['SU']['C'])
        CA = utils.find_new_point_location(old_plane_points=hip_dict['CA']['Plane'],
                                           new_plane_points=self.femure_plane,
                                           old_marked_point=hip_dict['CA']['C'])
        AT = utils.find_new_point_location(old_plane_points=hip_dict['AT']['Plane'],
                                           new_plane_points=self.femure_plane,
                                           old_marked_point=hip_dict['AT']['C'])
        PT = utils.find_new_point_location(old_plane_points=hip_dict['PT']['Plane'],
                                           new_plane_points=self.femure_plane,
                                           old_marked_point=hip_dict['PT']['C'])

        camera_reference = np.array([
            FO,
            SU,
            CA,
            AT,
            PT
        ])
        camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
        hip_STL_obj_points = np.array([
            [-45.5239716, -2.5871658, -154.1749573],  # F0
            [-63.6134491, 29.2462463, -157.989151],  # SU
            [-22.7026062, 2.6151695, -149.2989197],  # CA
            [-30.7326965, 8.4291382, -133.5983887],  # AT
            [-15.77771, 17.0255318, -159.7479401],  # PT
        ])
        # Calculate transformation matrix based on the camera reference
        self.transformation_matrix = self.calculate_transformation_matrix_with_scaling(camera_reference,
                                                                                       hip_STL_obj_points)

        while True:
            try:
                if not self.point_queue.empty():
                    point = self.point_queue.get()
                    if point == 'exit':
                        self.plotter.close()
                        break
                    if point == 'no data':
                        print('Pointer is not visible')

                    else:
                        if self.ring_points_actor:
                            self.plotter.remove_actor(self.ring_points_actor)  # Remove only the ring points actor
                        # Convert the point to a numpy array and apply the transformation
                        point = np.array(eval(point), dtype=np.float32)
                        framecount = int(point[3]) + 1  # Ensure `colour` is an integer
                        point = [point[0], -point[1], point[2]]  # Adjust Z axis orientation if needed

                        print(f'framecount {framecount}')
                        transformed_point = np.dot(self.transformation_matrix[:3, :3],
                                                   point) + self.transformation_matrix[:3, 3]

                        single_point = np.array([transformed_point])
                        print(f'single_point {single_point}')
                        # single_point = np.array([point])

                        # 4. Create a PolyData object for the point
                        point_cloud = pv.PolyData(single_point)

                        # 5. Assign a color to the point (optional, you can choose your own color)
                        point_color = np.array([127 / 255, 1, 0])  # Example: Teal color
                        point_cloud["colors"] = np.tile(point_color, (single_point.shape[0], 1))

                        # 6. Optionally, increase the size of the point by using a glyph (small sphere)
                        glyph_radius = 1  # Adjust the radius as needed
                        glyph = point_cloud.glyph(geom=pv.Sphere(radius=glyph_radius), scale=False, orient=False)

                        # 7. Add the new point (or sphere) to the plotter
                        self.plotter.add_mesh(glyph, scalars="colors", rgb=True, label="Incoming Point")

                        # Update the framecount text at the top
                        # Update the framecount text at the top
                        if self.framecount_text:
                            # Clear the old text actor by removing it from the plotter
                            self.plotter.remove_actor(self.framecount_text)

                        # Add the updated framecount text
                        self.framecount_text = self.plotter.add_text(f'Rim Points: {framecount}/10',
                                                                     position='upper_left', color='white',
                                                                     font_size=10)

                        # Optionally, update the plotter
                        self.plotter.update()
                        self.plotter.app.processEvents()

                # Update the mesh with the new colors
                self.plotter.render()

                # Process events to keep the UI responsive
                self.plotter.app.processEvents()

            except Exception as e:
                print(f"Error: {e}")

    def hip_FivePoint_register_2(self):
        try:
            pickle_path_TP = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                          'tp_variable.pickle')
            with open(pickle_path_TP, 'rb') as handle:
                TP = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")
        utils.writeRegisteredPoint('STEP0')

        variable_dict = {}
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        point_reg = ['FO', 'SU', 'CA', 'AT', 'PT']
        framecount = 0
        # pointer_data = []
        while True:
            value = utils.checkLoopExitCondition()
            if value.lower() != 'hip_FivePoint_register_2'.lower():
                return
            ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                            500)
            ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                              stFrameInfo, 500)
            if ret == 0 and ret2 == 0:
                img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

                rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)
                try:
                    detections_right = utils.find_contours_Kmeans(rec_img_right)
                    detections_left = utils.find_contours_Kmeans(rec_img_left)
                    if len(detections_right) == 6 and 6 == len(detections_left):
                        detections_right = sorted(detections_right, key=lambda x: x[0])
                        detections_left = sorted(detections_left, key=lambda x: x[0])
                        # Extract first 3 coordinates
                        first_three_right = detections_right[:3]
                        first_three_left = detections_left[:3]
                        next_three_right = detections_right[-3:]
                        next_three_left = detections_left[-3:]

                        Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                         rec_img_left)
                        Pointer_traker.find_depth()
                        pointer_tip_gcs = Pointer_traker.getStylusPoint()
                        # pointer_data.append(pointer_tip_gcs)
                        # if len(pointer_data) < 10:
                        #     continue
                        # pointer_tip_gcs = utils.filter_point_registration_data(pointer_data)

                        femure = TriangleTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right,
                                                 rec_img_left)
                        femure.find_depth()
                        F_points = femure.getLEDcordinates()

                        Tracker_plane = [F_points[2], F_points[1], F_points[0]]

                        pointRegistered = utils.find_new_point_location(old_plane_points=Tracker_plane,
                                                                        new_plane_points=TP['TP']['Plane'],
                                                                        old_marked_point=pointer_tip_gcs)
                        variable_memory = {'C': pointRegistered, 'Plane': TP['TP']['Plane']}
                        print(f'pointRegistered {pointRegistered}   {pointer_tip_gcs}')
                        variable_dict[point_reg[framecount]] = variable_memory

                        utils.writeRegisteredPoint(point_reg[framecount])
                        print(f'Point registered {point_reg[framecount]}')
                        utils.play_notification_sound()

                        # pointer_data.clear()
                        framecount = framecount + 1
                        if framecount == 5:
                            print("All points registered.")
                            RE_Hip_variable_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                                                'register_hip_variables.pickle')
                            os.makedirs(os.path.dirname(RE_Hip_variable_path), exist_ok=True)
                            with open(RE_Hip_variable_path, 'wb') as handle:
                                pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
                            print("Pickle dump successful")
                            break
                    else:
                        print("no data[0x%x]" % ret)
                except Exception as e:
                    print(f'Error {e}')
        self.dual_cam.stop_grabbing()
        return 0

    # http://127.0.0.1:8000/hip/pelvis-registration-2.html?selected=coupon_question1
    def hip_ASIS_three_points_MarkingPipeline(self):
        try:
            pickle_path_TP = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                          'tp_variable.pickle')
            with open(pickle_path_TP, 'rb') as handle:
                TP = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")

        variable_dict = {}
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        point_reg = ['ASL', 'ASR', 'PSC']
        framecount = 0
        # pointer_data = []
        while True:
            value = utils.checkLoopExitCondition()
            if value.lower() != 'hip_threepoint_register'.lower():
                return
            ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                            500)
            ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                              stFrameInfo, 500)
            if ret == 0 and ret2 == 0:
                img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

                rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)
                try:
                    detections_right = utils.find_contours_Kmeans(rec_img_right)
                    detections_left = utils.find_contours_Kmeans(rec_img_left)
                    if len(detections_right) == 6 and 6 == len(detections_left):
                        detections_right = sorted(detections_right, key=lambda x: x[0])
                        detections_left = sorted(detections_left, key=lambda x: x[0])
                        # Extract first 3 coordinates
                        first_three_right = detections_right[:3]
                        first_three_left = detections_left[:3]
                        next_three_right = detections_right[-3:]
                        next_three_left = detections_left[-3:]

                        Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                         rec_img_left)
                        Pointer_traker.find_depth()

                        pointer_tip_gcs = Pointer_traker.getStylusPoint()

                        femure = TriangleTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right,
                                                 rec_img_left)
                        femure.find_depth()
                        F_points = femure.getLEDcordinates()

                        Tracker_plane = [F_points[2], F_points[1], F_points[0]]

                        pointRegistered = utils.find_new_point_location(old_plane_points=Tracker_plane,
                                                                        new_plane_points=TP['TP']['Plane'],
                                                                        old_marked_point=pointer_tip_gcs)
                        variable_memory = {'C': pointRegistered, 'Plane': TP['TP']['Plane']}
                        print(f'pointRegistered {pointRegistered}   {pointer_tip_gcs}')
                        variable_dict[point_reg[framecount]] = variable_memory
                        utils.play_notification_sound()
                        utils.writeRegisteredPoint(point_reg[framecount])
                        print(f'Point registered {point_reg[framecount]}')
                        framecount = framecount + 1
                    else:
                        print("no data[0x%x]" % ret)
                    if framecount == 3:
                        print("All points registered.")
                        Hip_threePoint_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                                           'hip_three_variables.pickle')
                        os.makedirs(os.path.dirname(Hip_threePoint_path), exist_ok=True)
                        with open(Hip_threePoint_path, 'wb') as handle:
                            pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
                        print("Pickle dump successful")

                        break
                except Exception as e:
                    print(f'Error {e}')
        self.dual_cam.stop_grabbing()
        return 0

    def mid_axial_body_plane(self):
        try:
            pickle_path_TP = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                          'tp_variable.pickle')
            with open(pickle_path_TP, 'rb') as handle:
                TP = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")

        variable_dict = {}
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        point_reg = ['NOTE0', 'NOTE1', 'NOTE2']
        framecount = 0
        # pointer_data = []
        while True:
            value = utils.checkLoopExitCondition()
            if value.lower() != 'mid-axial-body-plane':
                return
            ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                            500)
            ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                              stFrameInfo, 500)
            if ret == 0 and ret2 == 0:
                img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

                rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)
                try:
                    detections_right = utils.find_contours_Kmeans(rec_img_right)
                    detections_left = utils.find_contours_Kmeans(rec_img_left)
                    if len(detections_right) == 6 and 6 == len(detections_left):
                        detections_right = sorted(detections_right, key=lambda x: x[0])
                        detections_left = sorted(detections_left, key=lambda x: x[0])
                        # Extract first 3 coordinates
                        first_three_right = detections_right[:3]
                        first_three_left = detections_left[:3]
                        next_three_right = detections_right[-3:]
                        next_three_left = detections_left[-3:]

                        Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                         rec_img_left)
                        Pointer_traker.find_depth()

                        pointer_tip_gcs = Pointer_traker.getStylusPoint()

                        femure = TriangleTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right,
                                                 rec_img_left)
                        femure.find_depth()
                        F_points = femure.getLEDcordinates()

                        Tracker_plane = [F_points[2], F_points[1], F_points[0]]

                        pointRegistered = utils.find_new_point_location(old_plane_points=Tracker_plane,
                                                                        new_plane_points=TP['TP']['Plane'],
                                                                        old_marked_point=pointer_tip_gcs)
                        variable_memory = {'C': pointRegistered, 'Plane': TP['TP']['Plane']}
                        print(f'pointRegistered {pointRegistered}   {pointer_tip_gcs}')
                        variable_dict[point_reg[framecount]] = variable_memory
                        utils.writeRegisteredPoint(point_reg[framecount])
                        utils.play_notification_sound()
                        print(f'Point registered {point_reg[framecount]}')
                        framecount = framecount + 1
                        # time.sleep(1)
                    else:
                        print("no data[0x%x]" % ret)
                    if framecount == 3:
                        print("All points registered.")
                        Hip_threePoint_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                                           'hip_three_variables.pickle')
                        os.makedirs(os.path.dirname(Hip_threePoint_path), exist_ok=True)
                        with open(Hip_threePoint_path, 'wb') as handle:
                            pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
                        print("Pickle dump successful")

                        break
                except Exception as e:
                    print(f'Error {e}')
        self.dual_cam.stop_grabbing()
        return 0

    def collect_pointCloud(self):
        points_dict = {}
        try:
            pickle_path_TP = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                          'tp_variable.pickle')
            with open(pickle_path_TP, 'rb') as handle:
                TP = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        framecount = 0
        pointer_data = []

        self.color_state = {}  # Dictionary to store the color state of each point
        distance_threshold = 0.01  # Define your distance threshold here
        obj_file_path = r'D:\SpineSurgery\pythonProject\app\static\images\hip-bone\3D\3Dhip\pelvis.obj'
        self.mesh = pv.read(obj_file_path)
        self.plotter = pvqt.BackgroundPlotter()
        Pos = (166.21689181742644, 460.3668000931386, -161.90325441730778)
        fpoint = (-66.02783205, -3.4104290000000006, -165.4661865)
        self.plotter.camera_position = [Pos, fpoint, (0, 1, 0)]  # Position the camera appropriately
        self.plotter.window().window().setMenuBar(None)
        self.plotter.window().window().findChild(QtWidgets.QToolBar)
        self.plotter.window().setWindowFlags(Qt.FramelessWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.plotter.set_background("black")
        self.plotter.show()
        self.plotter.window_size = (1913, 959)
        self.plotter.app_window.showMaximized()
        try:
            Hip_variable_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                             'hip_variables.pickle')
            with open(Hip_variable_path, 'rb') as handle:
                hip_dict = pickle.load(handle)
                print("TP variables loaded successfully")

        except Exception as e:
            print(f"Error loading TP variables {e}")
        camera_reference = np.array([
            hip_dict['FO']['C'],  # FO
            hip_dict['SU']['C'],  # SU
            hip_dict['CA']['C'],  # CA
            hip_dict['AT']['C'],  # AT
            hip_dict['PT']['C'],  # PT
        ])
        camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
        hip_STL_obj_points = np.array([
            [-45.5239716, -2.5871658, -154.1749573],  # F0
            [-63.6134491, 29.2462463, -157.989151],  # SU
            [-22.7026062, 2.6151695, -149.2989197],  # CA
            [-30.7326965, 8.4291382, -133.5983887],  # AT
            [-15.77771, 17.0255318, -159.7479401],  # PT
        ])
        # Calculate transformation matrix based on the camera reference
        self.transformation_matrix = self.calculate_transformation_matrix_with_scaling(camera_reference,
                                                                                       hip_STL_obj_points)
        initial_color = [240 / 255, 220 / 255, 170 / 255]  # RGB normalized for bone color
        colors = np.tile(initial_color, (self.mesh.n_points, 1))
        self.mesh["colors"] = colors

        # Add the femur model (mesh) with per-point colors
        self.plotter.add_mesh(self.mesh, scalars="colors", rgb=True, label="Femur Model")
        self.last_updated_point = None
        sphere_actor = None
        color_map = {
            0: [0.0, 1.0, 0.0],  # Green for condition 0
            1: [1.0, 1.0, 0.0],  # Yellow for condition 1
            2: [1.0, 0.0, 0.0],  # Red for condition 2
            4: [64 / 255, 224 / 255, 208 / 255],  # Turquoise Blue for condition 4
        }

        stl_color = np.array([127 / 255, 1, 0])  # Chartreuse Green for STL points
        stl_points = pv.PolyData(hip_STL_obj_points)

        # Create a point cloud mesh for STL points and assign color
        stl_points["colors"] = np.tile(stl_color, (hip_STL_obj_points.shape[0], 1))

        # Increase the size of the points by using 'glyph' to represent the points as spheres
        sphere_radius = 2  # Set sphere size to 20
        glyph = stl_points.glyph(geom=pv.Sphere(radius=sphere_radius), scale=True, orient=False)

        # Add the STL points to the plotter with the new size
        self.plotter.add_mesh(glyph, scalars="colors", rgb=True, label="STL Points")

        while True:
            value = utils.checkLoopExitCondition()
            if value.lower() != 'hip_FivePoint_register'.lower():
                # femure_point_cloud_file_Path = os.path.join(self.current_dir, '..', '..',
                #                                             'registration_data', 'points_dict.pickle')
                #
                # # Ensure the directory exists
                # os.makedirs(os.path.dirname(femure_point_cloud_file_Path), exist_ok=True)
                #
                # # Save the dictionary with pickle
                # with open(femure_point_cloud_file_Path, 'wb') as handle:
                #     pickle.dump(points_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
                self.plotter.close()
                return
            ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                            500)
            ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                              stFrameInfo, 500)
            if ret == 0 and ret2 == 0:
                img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

                rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)
                try:
                    detections_right = utils.find_contours_Kmeans(rec_img_right)
                    detections_left = utils.find_contours_Kmeans(rec_img_left)
                    if len(detections_right) == 6 and 6 == len(detections_left):
                        detections_right = sorted(detections_right, key=lambda x: x[0])
                        detections_left = sorted(detections_left, key=lambda x: x[0])
                        # Extract first 3 coordinates
                        first_three_right = detections_right[:3]
                        first_three_left = detections_left[:3]
                        next_three_right = detections_right[-3:]
                        next_three_left = detections_left[-3:]

                        Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                         rec_img_left)
                        Pointer_traker.find_depth()
                        pointer_tip_gcs = Pointer_traker.getStylusPoint()

                        # pointer_data.append(pointer_tip_gcs)
                        # if len(pointer_data) < 20:
                        #     continue
                        # pointer_tip_gcs = utils.filter_point_registration_data(pointer_data)

                        femure = TriangleTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right,
                                                 rec_img_left)
                        femure.find_depth()
                        F_points = femure.getLEDcordinates()

                        Tracker_plane = [F_points[2], F_points[1], F_points[0]]
                        variable_memory = {'C': pointer_tip_gcs, 'Plane': Tracker_plane}
                        points_dict[str(framecount)] = variable_memory
                        self.femure_plane = Tracker_plane
                        pointRegistered = utils.find_new_point_location(old_plane_points=Tracker_plane,
                                                                        new_plane_points=TP['TP']['Plane'],
                                                                        old_marked_point=pointer_tip_gcs)
                        color_code = 4  # represents blue
                        data = f"{pointRegistered[0]},{pointRegistered[1]},{pointRegistered[2]}\n"
                        print(f' data {data}')
                        point = np.array(eval(data), dtype=np.float32)
                        point[1] = -point[1]

                        transformed_point = np.dot(self.transformation_matrix[:3, :3],
                                                   point) + self.transformation_matrix[:3, 3]

                        self.last_updated_point = transformed_point

                        closest_point_idx = self.mesh.find_closest_point(tuple(transformed_point))
                        self.mesh["colors"][closest_point_idx] = color_map.get(color_code, initial_color)
                        self.color_state[closest_point_idx] = color_code  # Update the color state

                        # Update the mesh with the new colors
                        self.plotter.update()

                        # points_dict[str(framecount)] = np.array(pointRegistered)
                        framecount += 1
                        # time.sleep(0.01)
                        if framecount == 250:
                            femure_point_cloud_file_Path = os.path.join(self.current_dir, '..', '..',
                                                                        'registration_data', 'points_dict.pickle')

                            # Ensure the directory exists
                            os.makedirs(os.path.dirname(femure_point_cloud_file_Path), exist_ok=True)

                            # Save the dictionary with pickle
                            with open(femure_point_cloud_file_Path, 'wb') as handle:
                                pickle.dump(points_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

                            print(f"Points saved successfully for framecount {framecount - 1}")
                            # self.point_queue.put('exit')
                            break
                        self.plotter.render()

                        # Process events to keep the UI responsive
                        self.plotter.app.processEvents()
                    else:
                        print("no data[0x%x]" % ret)
                        if sphere_actor is not None:
                            self.plotter.remove_actor(sphere_actor)
                            sphere_actor = None  # Ensure the reference is cleared
                        # self.point_queue.put('no data')
                        time.sleep(0.5)
                except Exception as e:
                    print(f' Error {e}')
                    self.point_queue.put('nodata')

    def collect_pointCloud_revision(self):
        points_dict = {}
        try:
            pickle_path_TP = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                          'tp_variable.pickle')
            with open(pickle_path_TP, 'rb') as handle:
                TP = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        framecount = 0
        # pointer_data = []

        # self.captured_points = {}  # Initialize an empty set to store unique points

        while True:
            value = utils.checkLoopExitCondition()
            if value.lower() != 'hip_revision_sixpoint_register':
                return
            ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                            500)
            ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                              stFrameInfo, 500)
            if ret == 0 and ret2 == 0:
                img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

                rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)
                try:
                    detections_right = utils.find_contours_Kmeans(rec_img_right)
                    detections_left = utils.find_contours_Kmeans(rec_img_left)
                    if len(detections_right) == 6 and 6 == len(detections_left):
                        detections_right = sorted(detections_right, key=lambda x: x[0])
                        detections_left = sorted(detections_left, key=lambda x: x[0])
                        # Extract first 3 coordinates
                        first_three_right = detections_right[:3]
                        first_three_left = detections_left[:3]
                        next_three_right = detections_right[-3:]
                        next_three_left = detections_left[-3:]

                        Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                         rec_img_left)
                        Pointer_traker.find_depth()
                        pointer_tip_gcs = Pointer_traker.getStylusPoint()

                        # pointer_data.append(pointer_tip_gcs)
                        # if len(pointer_data) < 20:
                        #     continue
                        # pointer_tip_gcs = utils.filter_point_registration_data(pointer_data)

                        femure = TriangleTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right,
                                                 rec_img_left)
                        femure.find_depth()
                        F_points = femure.getLEDcordinates()

                        Tracker_plane = [F_points[2], F_points[1], F_points[0]]
                        variable_memory = {'C': pointer_tip_gcs, 'Plane': Tracker_plane}
                        points_dict[str(framecount)] = variable_memory
                        self.femure_plane = Tracker_plane
                        pointRegistered = utils.find_new_point_location(old_plane_points=Tracker_plane,
                                                                        new_plane_points=TP['TP']['Plane'],
                                                                        old_marked_point=pointer_tip_gcs)
                        color_code = 4  # represents blue
                        data = f"{pointRegistered[0]},{pointRegistered[1]},{pointRegistered[2]},{color_code}\n"
                        print(f' data {data}')
                        self.point_queue.put(data)

                        # points_dict[str(framecount)] = np.array(pointRegistered)
                        framecount += 1
                        # time.sleep(0.01)
                        if framecount == 500:
                            femure_point_cloud_file_Path = os.path.join(self.current_dir, '..', '..',
                                                                        'registration_data', 'points_dict.pickle')

                            # Ensure the directory exists
                            os.makedirs(os.path.dirname(femure_point_cloud_file_Path), exist_ok=True)

                            # Save the dictionary with pickle
                            with open(femure_point_cloud_file_Path, 'wb') as handle:
                                pickle.dump(points_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)

                            print(f"Points saved successfully for framecount {framecount - 1}")
                            self.point_queue.put('exit')
                            break
                    else:
                        print("no data[0x%x]" % ret)
                        self.point_queue.put('no data')
                        time.sleep(0.5)
                except Exception as e:
                    print(f' Error {e}')
                    self.point_queue.put('nodata')

    def euclidean_distance(self, p1, p2):
        return np.linalg.norm(np.array(p1) - np.array(p2))

    def get_color_based_on_distance(self, distance, radius1, radius2):
        if distance <= radius1:
            return 0
        elif distance <= radius2:
            return 1
        else:
            return 2

    def angle_between_vectors(self, v1, v2):
        v1_norm = np.linalg.norm(v1)
        v2_norm = np.linalg.norm(v2)
        dot_product = np.dot(v1, v2)
        cos_theta = np.clip(dot_product / (v1_norm * v2_norm), -1.0, 1.0)
        return np.arccos(cos_theta)

    def update_point_colors(self, point_cloud_dict, pointer_tip_gcs, FO_point, vector1, vector2):
        # Calculate the angle between the vectors
        angle = self.angle_between_vectors(vector1, vector2)

        # Calculate scaling factor based on the angle
        scaling_factor = 1 - (angle / np.pi)

        # Calculate the distance to FO point
        distance_to_fo = self.euclidean_distance(pointer_tip_gcs, FO_point)
        radius1 = 0.5 * distance_to_fo * scaling_factor  # 50% of distance to FO_point adjusted by angle
        radius2 = 0.95 * distance_to_fo * scaling_factor  # 95% of distance to FO_point adjusted by angle

        # Dictionary to store updated point colors
        point_colors = {}

        # Iterate through all the 3D points in point_cloud_dict
        for i, point_data in point_cloud_dict.items():
            # Use the find_new_point_location method to get the transformed point
            point = utils.find_new_point_location(
                old_plane_points=point_data['Plane'],
                new_plane_points=self.femure_plane,  # Assuming femure_plane is the target plane after transformation
                old_marked_point=point_data['C']
            )

            # Calculate the distance between the transformed point and pointer_tip_gcs
            distance = self.euclidean_distance(pointer_tip_gcs, point)

            # Get color based on the distance and radii
            color = self.get_color_based_on_distance(distance, radius1, radius2)

            # Store the color for the point
            point_colors[i] = color

        return point_colors

    def load_data(self, file_path='LED_data_6ft_final.txt'):
        led_points = []
        robot_points = []

        with open(file_path, "r") as file:
            for line in file:
                if "LED" in line and "robot" in line:
                    led_part = line.split("LED [")[1].split("]")[0].split()
                    robot_part = line.split("robot (")[1].split(")")[0].split(", ")

                    # Convert to float
                    led_x, led_y, led_z = map(float, led_part)
                    robot_x, robot_y, robot_z = map(float, robot_part)

                    led_points.append([led_x, led_y, led_z])
                    robot_points.append([robot_x, robot_y, robot_z])

        return np.array(led_points), np.array(robot_points)

    def robot_destination_point(self, Newrobot_tracker_plane, opticalpoint):
        robot_tracker_plane = np.array([
            [533.2334957, 491.84367074, 372.20815346],
            [541.01826208, 491.76316066, 372.69216926],
            [537.28529877, 480.50172382, 375.13125414]
        ])
        modelR = LinearRegression()
        led_points, robot_points = self.load_data()
        led_points[:, [1, 2]] = led_points[:, [2, 1]]
        modelR.fit(robot_points, led_points)
        C = [0, 0, 0]
        C = np.array(C).reshape(1, -1)
        old_mark_point = modelR.predict(C)
        old_mark_point[0][1], old_mark_point[0][2] = old_mark_point[0][2], old_mark_point[0][1]
        print("*" * 100)
        print(old_mark_point)
        print("*" * 100)

        # Given base location
        # base_location = np.array([552.72079317, 469.84631172, 393.37443628])

        # Step 1: Compute centroid
        centroid = np.mean(robot_tracker_plane, axis=0)

        # Step 2: Compute translation vector
        translation_vector = old_mark_point[0] - centroid

        # Step 3: Translate the plane
        translated_plane = robot_tracker_plane + translation_vector

        new_origin = utils.find_new_point_location(old_plane_points=robot_tracker_plane,
                                                   new_plane_points=Newrobot_tracker_plane,
                                                   old_marked_point=old_mark_point[0])

        centroid = np.mean(robot_tracker_plane, axis=0)

        # Step 2: Compute translation vector
        translation_vector = new_origin - centroid

        # Step 3: Translate the plane
        Newtranslated_plane = robot_tracker_plane + translation_vector

        # new_origin[1], new_origin[2] = new_origin[2], new_origin[1]
        # new_origin = np.array(new_origin).reshape(1, -1)
        # led_points = led_points + new_origin
        led_points[:, [1, 2]] = led_points[:, [2, 1]]
        transformed_led_points = np.array([
            utils.find_new_point_location(old_plane_points=translated_plane,
                                          new_plane_points=tuple(Newtranslated_plane),
                                          old_marked_point=led) for led in led_points
        ])

        # Swap Y and Z back to match coordinate system
        transformed_led_points[:, [1, 2]] = transformed_led_points[:, [2, 1]]
        # transformed_led_points = np.vstack([transformed_led_points, new_origin])
        # robot_points = np.vstack([robot_points, np.array([0,0,0])])

        updated_model = LinearRegression()
        updated_model.fit(transformed_led_points, robot_points)

        opticalpoint[2], opticalpoint[1] = opticalpoint[1], opticalpoint[2]
        point1 = np.array(opticalpoint).reshape(1, -1)
        predicted_dest = updated_model.predict(point1)

        return predicted_dest[0][0], predicted_dest[0][0], predicted_dest[0][0]

    def hip_reming_process(self):
        try:
            femure_point_cloud_file_Path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                                        'points_dict.pickle')
            with open(femure_point_cloud_file_Path, 'rb') as handle:
                point_cloud_dict = pickle.load(handle)
                print("Femur point cloud loaded successfully")

            Hip_variable_path = os.path.join(self.current_dir, '..', '..', 'registration_data', 'hip_variables.pickle')
            with open(Hip_variable_path, 'rb') as handle:
                hip_dict = pickle.load(handle)
                print("Hip variables loaded successfully")

        except Exception as e:
            print(f"Error loading files: {e}")
            return
        arm = XArmAPI('*************')
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        framecount = 0
        # Establish connection to the arm
        arm.connect()
        if arm.connected:
            arm.motion_enable(enable=True)  # Enable motion
            arm.set_mode(0)  # Set to position control mode
            arm.set_state(0)  # Set to ready state

            while True:
                self.g_bExit = utils.checkLoopExitCondition()
                if self.g_bExit:
                    utils.SetLoopExitCondition(False)
                    self.dual_cam.stop_grabbing()
                    return -1

                ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                                500)
                ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                                  stFrameInfo,
                                                                  500)

                if ret == 0 and ret2 == 0:
                    img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                        (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                    img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                        (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

                    rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)

                    try:
                        detections_right = utils.find_contours_Kmeans(rec_img_right)
                        detections_left = utils.find_contours_Kmeans(rec_img_left)

                        if len(detections_right) == 6 and len(detections_left) == 6:
                            detections_right = sorted(detections_right, key=lambda x: x[0])
                            detections_left = sorted(detections_left, key=lambda x: x[0])

                            # Extract first 3 coordinates
                            first_three_right = detections_right[:3]
                            first_three_left = detections_left[:3]
                            next_three_right = detections_right[-3:]
                            next_three_left = detections_left[-3:]

                            # Femur tracking and finding depth
                            femure = TriangleTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right,
                                                     rec_img_left)
                            femure.find_depth()
                            F_points = femure.getLEDcordinates()

                            Tracker_plane = [F_points[2], F_points[1], F_points[0]]
                            self.femure_plane = Tracker_plane

                            # Pointer tracking
                            Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left),
                                                             rec_img_right,
                                                             rec_img_left)
                            Pointer_traker.find_depth()
                            remure_leds = Pointer_traker.getLEDcordinates()
                            pointer_tip_gcs = Pointer_traker.getStylusPoint()
                            mid_remure_led = (np.array(remure_leds[0]) + np.array(remure_leds[1])) / 2
                            remure_vector = np.array(remure_leds[2]) - np.array(mid_remure_led)

                            FO = utils.find_new_point_location(old_plane_points=hip_dict['FO']['Plane'],
                                                               new_plane_points=Tracker_plane,
                                                               old_marked_point=hip_dict['FO']['C'])
                            SU = utils.find_new_point_location(old_plane_points=hip_dict['SU']['Plane'],
                                                               new_plane_points=Tracker_plane,
                                                               old_marked_point=hip_dict['SU']['C'])
                            CA = utils.find_new_point_location(old_plane_points=hip_dict['CA']['Plane'],
                                                               new_plane_points=Tracker_plane,
                                                               old_marked_point=hip_dict['CA']['C'])
                            AT = utils.find_new_point_location(old_plane_points=hip_dict['AT']['Plane'],
                                                               new_plane_points=Tracker_plane,
                                                               old_marked_point=hip_dict['AT']['C'])
                            PT = utils.find_new_point_location(old_plane_points=hip_dict['PT']['Plane'],
                                                               new_plane_points=Tracker_plane,
                                                               old_marked_point=hip_dict['PT']['C'])
                            position = arm.get_position()
                            if position[0] == 0:  # Check if fetching position was successful
                                x, y, z, roll, pitch, yaw = position[1]
                                print(f"Current Position:\nX: {x} mm\nY: {y} mm\nZ: {z} mm")
                                print(f"Roll: {roll} degrees\nPitch: {pitch} degrees\nYaw: {yaw} degrees")
                            x_position, y_position, z_position = self.robot_destination_point(opticalpoint=FO)
                            #  DEFINE END EFFECTOR and Translate in the robotic position
                            #
                            #
                            # TAKE DIFFERENCE between originl get_position and last posion includeing translation and if it is
                            # ==0.01mm stop riming process
                            #
                            #
                            #
                            # Calculate vectors SU -> CA and AT -> PT
                            SU_CA = np.array(SU) - np.array(CA)
                            AT_PT = np.array(AT) - np.array(PT)

                            # Cross product of SU_CA and AT_PT
                            cross_product_ = np.cross(SU_CA, AT_PT)
                            norm_cross_product = np.linalg.norm(cross_product_)
                            hip_reference_vector = cross_product_ / norm_cross_product if norm_cross_product != 0 else cross_product_

                            # Dot product between remure_vector and hip_reference_vector
                            dot_product = np.dot(remure_vector, hip_reference_vector)
                            norm_remure = np.linalg.norm(remure_vector)
                            norm_hip_reference = np.linalg.norm(hip_reference_vector)
                            cos_theta = dot_product / (norm_remure * norm_hip_reference)

                            # Calculate angle in radians and convert to degrees
                            angle_radians = np.arccos(np.clip(cos_theta, -1.0, 1.0))
                            angle_degrees = np.degrees(angle_radians)

                            print(f"The angle between the two vectors is: {angle_degrees} degrees")

                            # Update point colors based on distances and angle-adjusted radii
                            point_colors = self.update_point_colors(point_cloud_dict, pointer_tip_gcs, FO,
                                                                    remure_vector,
                                                                    hip_reference_vector)

                            for i, color in point_colors.items():
                                # print(f"Point {i}: Color {color}")
                                temp = point_cloud_dict[str(i)]['C']
                                data = f'{temp[0]},{temp[1]},{temp[2]},{color}'
                                # print(f"Point {data}: Color {color}")
                                self.point_queue.put(data)
                            framecount += 1
                            # time.sleep(0.2)

                            if framecount == 100:
                                self.point_queue.put('exit')
                                break
                        else:
                            print("No data [0x%x]" % ret)
                            self.point_queue.put('no data')
                            time.sleep(0.5)

                    except Exception as e:
                        print(f"Error processing images: {e}")
                        self.point_queue.put('no data')

        return 0

    def hip_Revision_SixPoint_register(self):
        try:
            pickle_path_TP = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                          'tp_variable.pickle')
            with open(pickle_path_TP, 'rb') as handle:
                TP = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")
        # utils.writeRegisteredPoint('NoName')

        variable_dict = {}
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        point_reg = ['ASIS', 'TF', 'PSIS', 'IT', 'TAL', 'PS']
        framecount = 0
        pointer_data = []
        while True:
            value = utils.checkLoopExitCondition()
            if value.lower() != 'hip_revision_sixpoint_register':
                return
            ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                            500)
            ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                              stFrameInfo, 500)
            if ret == 0 and ret2 == 0:
                img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

                rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)
                try:
                    detections_right = utils.find_contours_Kmeans(rec_img_right)
                    detections_left = utils.find_contours_Kmeans(rec_img_left)
                    if len(detections_right) == 6 and 6 == len(detections_left):
                        detections_right = sorted(detections_right, key=lambda x: x[0])
                        detections_left = sorted(detections_left, key=lambda x: x[0])
                        # Extract first 3 coordinates
                        first_three_right = detections_right[:3]
                        first_three_left = detections_left[:3]
                        next_three_right = detections_right[-3:]
                        next_three_left = detections_left[-3:]

                        Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                         rec_img_left)
                        Pointer_traker.find_depth()
                        pointer_tip_gcs = Pointer_traker.getStylusPoint()
                        pointer_data.append(pointer_tip_gcs)
                        if len(pointer_data) < 20:
                            continue
                        pointer_tip_gcs = utils.filter_point_registration_data(pointer_data)

                        femure = TriangleTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right,
                                                 rec_img_left)
                        femure.find_depth()
                        F_points = femure.getLEDcordinates()

                        Tracker_plane = [F_points[2], F_points[1], F_points[0]]

                        pointRegistered = utils.find_new_point_location(old_plane_points=Tracker_plane,
                                                                        new_plane_points=TP['TP']['Plane'],
                                                                        old_marked_point=pointer_tip_gcs)
                        variable_memory = {'C': pointRegistered, 'Plane': TP['TP']['Plane']}
                        print(f'pointRegistered {pointRegistered}   {pointer_tip_gcs}')
                        variable_dict[point_reg[framecount]] = variable_memory

                        utils.writeRegisteredPoint(point_reg[framecount])
                        print(f'Point registered {point_reg[framecount]}')
                        utils.play_notification_sound()

                        pointer_data.clear()
                        framecount = framecount + 1
                    else:
                        print("no data[0x%x]" % ret)
                    if framecount == 6:
                        print("All points registered.")
                        Revision_Hip_variable_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                                                  'revision_hip_variables.pickle')
                        os.makedirs(os.path.dirname(Revision_Hip_variable_path), exist_ok=True)
                        with open(Revision_Hip_variable_path, 'wb') as handle:
                            pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
                        print("Pickle dump successful")

                        break
                except Exception as e:
                    print(f'Error {e}')
        self.dual_cam.stop_grabbing()
        return 0

    # def plot_pointCloud_with_background(self):
    #     def calculate_transformation_matrix(source_points, target_points):
    #         if source_points.size == 0 or target_points.size == 0:
    #             raise ValueError(
    #                 "Error: One or both of the input arrays are empty. Transformation matrix cannot be computed.")
    #         centroid_source = np.mean(source_points, axis=0)
    #         centroid_target = np.mean(target_points, axis=0)
    #         centered_source = source_points - centroid_source
    #         centered_target = target_points - centroid_target
    #
    #         H = np.dot(centered_source.T, centered_target)
    #         U, S, Vt = np.linalg.svd(H)
    #         R_matrix = np.dot(Vt.T, U.T)
    #
    #         if np.linalg.det(R_matrix) < 0:
    #             Vt[-1, :] *= -1
    #             R_matrix = np.dot(Vt.T, U.T)
    #
    #         t = centroid_target - np.dot(R_matrix, centroid_source)
    #         transformation_matrix = np.eye(4)
    #         transformation_matrix[:3, :3] = R_matrix
    #         transformation_matrix[:3, 3] = t
    #         return transformation_matrix
    #
    #     def add_transformed_live_point(point_cloud, stl_points, point, transformation_matrix):
    #         transformed_point = transformation_matrix @ np.append(point, 1)
    #         transformed_point = transformed_point[:3]
    #
    #         # Append the new point to the point cloud
    #         points = np.asarray(point_cloud.points)
    #         new_points = np.vstack([points, transformed_point])
    #         point_cloud.points = o3d.utility.Vector3dVector(new_points)
    #
    #         # Set all points to black
    #         colors = np.ones((len(new_points), 3)) * 255  # Set all points to black (0, 0, 0)
    #         point_cloud.colors = o3d.utility.Vector3dVector(colors)
    #
    #         # Calculate the nearest STL point for
    #         distances = np.linalg.norm(stl_points - transformed_point, axis=1)
    #         nearest_stl_point = stl_points[np.argmin(distances)]
    #         difference_vector = transformed_point - nearest_stl_point
    #         print(
    #             f"Transformed Point: {transformed_point}, Nearest STL Point: {nearest_stl_point}, Difference: {difference_vector}")
    #
    #     # Load STL and initial setup
    #     stl_mesh = mesh.Mesh.from_file(
    #         os.path.join(r'D:\SpineSurgery\pythonProject\app\static\images\hip-bone\HIP.obj'))
    #     vertices = stl_mesh.vectors.reshape(-1, 3)
    #     pcd = o3d.geometry.PointCloud()
    #     pcd.points = o3d.utility.Vector3dVector(vertices)
    #
    #     vis = o3d.visualization.VisualizerWithKeyCallback()
    #     vis.create_window(visible=True, width=1920, height=1080)  # Set your desired resolution
    #     vis.add_geometry(pcd)
    #     # vis.get_view_control().set_full_screen(True)
    #
    #     # Configure visualization
    #     # def set_full_screen(vis):
    #     #     vis.get_view_control().set_zoom(0.35)
    #     #     vis.get_render_option().background_color = [0, 0, 0]
    #     #     vis.get_render_option().mesh_show_back_face = True
    #     #
    #     # vis.register_key_callback(32, set_full_screen)  # Space key for full screen
    #     vis.poll_events()
    #     vis.update_renderer()
    #
    #     # Load STL points and Hip variables
    #     stl_points_path = os.path.join(self.current_dir, '..', '..', 'registration_data', 'stl_points.txt')
    #     stl_points = np.loadtxt(stl_points_path, delimiter=',')
    #
    #     Hip_variable_path = os.path.join(self.current_dir, '..', '..', 'registration_data', 'hip_variables.pickle')
    #     with open(Hip_variable_path, 'rb') as handle:
    #         Hip_dict = pickle.load(handle)
    #
    #     FemurePlane = self.read_femure_plane()
    #     SU = utils.find_new_point_location(Hip_dict['SU']['Plane'], FemurePlane, Hip_dict['SU']['C'])
    #     CA = utils.find_new_point_location(Hip_dict['CA']['Plane'], FemurePlane, Hip_dict['CA']['C'])
    #     AT = utils.find_new_point_location(Hip_dict['AT']['Plane'], FemurePlane, Hip_dict['AT']['C'])
    #     PT = utils.find_new_point_location(Hip_dict['PT']['Plane'], FemurePlane, Hip_dict['PT']['C'])
    #     reference_points = np.array(tuple([SU, CA, AT , PT]))
    #
    #     # reference_points = np.array([
    #     #     [515.7659519012589, 484.4478841296016, 188.23527473232923],
    #     #     [517.090524066549, 482.2972294067308, 188.51022778938153],
    #     #     [516.5354593000155, 483.33695592928365, 189.17117185197776],
    #     #     [516.5857882033099, 482.6842834229069, 187.56417066863264]
    #     # ])
    #
    #     transformation_matrix = calculate_transformation_matrix(reference_points, stl_points)
    #
    #     stl_point_cloud = o3d.geometry.PointCloud()
    #     vis.add_geometry(stl_point_cloud)
    #     vis.get_render_option().background_color = [0, 0, 0]  # Black background
    #     while True:
    #         incoming_point = self.point_queue.get()
    #
    #         # neighboring_points = generate_nearby_points(incoming_point, radius=0.01, num_points=6)
    #         if incoming_point == 'exit':
    #             break
    #         incoming_point = np.array(eval(incoming_point))
    #         add_transformed_live_point(stl_point_cloud, stl_points, incoming_point, transformation_matrix)
    #         # for point in neighboring_points:
    #         #     add_transformed_live_point(stl_point_cloud, stl_points, point, transformation_matrix)
    #         vis.update_geometry(stl_point_cloud)
    #         vis.poll_events()
    #         vis.update_renderer()
    #     vis.destroy_window()

    def calculate_transformation_matrix(self, source_points, target_points):
        """Calculate transformation matrix from source points to target points."""
        centroid_source = np.mean(source_points, axis=0)
        centroid_target = np.mean(target_points, axis=0)
        centered_source = source_points - centroid_source
        centered_target = target_points - centroid_target
        H = np.dot(centered_source.T, centered_target)
        U, S, Vt = np.linalg.svd(H)
        R_matrix = np.dot(Vt.T, U.T)
        if np.linalg.det(R_matrix) < 0:
            Vt[-1, :] *= -1
            R_matrix = np.dot(Vt.T, U.T)
        t = centroid_target - np.dot(R_matrix, centroid_source)
        transformation_matrix = np.eye(4)
        transformation_matrix[:3, :3] = R_matrix
        transformation_matrix[:3, 3] = t
        return transformation_matrix

    def calculate_transformation_matrix_with_scaling(self, source, target):
        """Calculate transformation matrix with scaling from source points to target points."""
        # Calculate centroids
        centroid_source = np.mean(source, axis=0)
        centroid_target = np.mean(target, axis=0)

        # Center the points
        centered_source = source - centroid_source
        centered_target = target - centroid_target

        # Estimate scaling factor
        scale = np.sum(np.linalg.norm(centered_target, axis=1)) / np.sum(np.linalg.norm(centered_source, axis=1))

        # Apply scaling to source points
        scaled_source = centered_source * scale

        # Compute H matrix for SVD
        H = np.dot(scaled_source.T, centered_target)

        # Compute rotation matrix using SVD
        U, S, Vt = np.linalg.svd(H)
        R_matrix = np.dot(Vt.T, U.T)

        # Correct for improper rotation (reflection) if determinant is negative
        if np.linalg.det(R_matrix) < 0:
            Vt[-1, :] *= -1
            R_matrix = np.dot(Vt.T, U.T)

        # Compute translation vector
        t = centroid_target - np.dot(R_matrix, centroid_source * scale)

        # Construct the final transformation matrix
        transformation_matrix = np.eye(4)
        transformation_matrix[:3, :3] = R_matrix * scale
        transformation_matrix[:3, 3] = t

        return transformation_matrix

    def save_updated_mesh(self, mesh_file_path, color_state_file_path):
        try:
            # Save the updated mesh to a file
            self.mesh.save(mesh_file_path)

            # Save the color state dictionary to a pickle file
            with open(color_state_file_path, 'wb') as f:
                pickle.dump(self.color_state, f)

            print("Updated mesh and color state saved successfully.")
        except Exception as e:
            print(f"Error saving mesh and color state: {e}")

    def plot_pointCloud_with_background(self):
        # File path for the 3D femur model
        while True:
            # Check if femure_plane has been updated
            if self.femure_plane is not None:
                # print("femure_plane has been updated!")
                break  # Exit the loop when the condition is met

            # Log and wait for a short duration before checking again
            # print("Waiting for femure_plane to be updated...")
            time.sleep(0.5)  # Avoid tight looping and reduce CPU usage
        self.color_state = {}  # Dictionary to store the color state of each point
        distance_threshold = 0.01  # Define your distance threshold here
        obj_file_path = r'D:\SpineSurgery\pythonProject\app\static\images\hip-bone\3D\3Dhip\pelvis.obj'
        self.mesh = pv.read(obj_file_path)
        self.plotter = pvqt.BackgroundPlotter()
        Pos = (166.21689181742644, 460.3668000931386, -161.90325441730778)
        fpoint = (-66.02783205, -3.4104290000000006, -165.4661865)
        self.plotter.camera_position = [Pos, fpoint, (0, 1, 0)]  # Position the camera appropriately
        self.plotter.window().window().setMenuBar(None)
        self.plotter.window().window().findChild(QtWidgets.QToolBar)
        self.plotter.window().setWindowFlags(Qt.FramelessWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.plotter.set_background("black")
        self.plotter.show()
        self.plotter.window_size = (1913, 959)
        self.plotter.app_window.showMaximized()
        try:
            Hip_variable_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                             'hip_variables.pickle')
            with open(Hip_variable_path, 'rb') as handle:
                hip_dict = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")

        # FO = utils.find_new_point_location(old_plane_points=hip_dict['FO']['Plane'],
        #                                    new_plane_points=self.femure_plane,
        #                                    old_marked_point=hip_dict['FO']['C'])
        # SU = utils.find_new_point_location(old_plane_points=hip_dict['SU']['Plane'],
        #                                    new_plane_points=self.femure_plane,
        #                                    old_marked_point=hip_dict['SU']['C'])
        # CA = utils.find_new_point_location(old_plane_points=hip_dict['CA']['Plane'],
        #                                    new_plane_points=self.femure_plane,
        #                                    old_marked_point=hip_dict['CA']['C'])
        # AT = utils.find_new_point_location(old_plane_points=hip_dict['AT']['Plane'],
        #                                    new_plane_points=self.femure_plane,
        #                                    old_marked_point=hip_dict['AT']['C'])
        # PT = utils.find_new_point_location(old_plane_points=hip_dict['PT']['Plane'],
        #                                    new_plane_points=self.femure_plane,
        #                                    old_marked_point=hip_dict['PT']['C'])
        camera_reference = np.array([
            hip_dict['FO']['C'],
            hip_dict['SU']['C'],
            hip_dict['CA']['C'],
            hip_dict['AT']['C'],
            hip_dict['PT']['C']
        ])
        camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
        hip_STL_obj_points = np.array([
            [-45.5239716, -2.5871658, -154.1749573],  # F0
            [-63.6134491, 29.2462463, -157.989151],  # SU
            [-22.7026062, 2.6151695, -149.2989197],  # CA
            [-30.7326965, 8.4291382, -133.5983887],  # AT
            [-15.77771, 17.0255318, -159.7479401],  # PT
        ])
        # Calculate transformation matrix based on the camera reference
        self.transformation_matrix = self.calculate_transformation_matrix_with_scaling(camera_reference,
                                                                                       hip_STL_obj_points)

        initial_color = [240 / 255, 220 / 255, 170 / 255]  # RGB normalized for bone color
        colors = np.tile(initial_color, (self.mesh.n_points, 1))
        self.mesh["colors"] = colors

        # Add the femur model (mesh) with per-point colors
        self.plotter.add_mesh(self.mesh, scalars="colors", rgb=True, label="Femur Model")
        self.last_updated_point = None
        sphere_actor = None
        color_map = {
            0: [0.0, 1.0, 0.0],  # Green for condition 0
            1: [1.0, 1.0, 0.0],  # Yellow for condition 1
            2: [1.0, 0.0, 0.0],  # Red for condition 2
            4: [64 / 255, 224 / 255, 208 / 255],  # Turquoise Blue for condition 4
        }

        stl_color = np.array([127 / 255, 1, 0])  # Chartreuse Green for STL points
        stl_points = pv.PolyData(hip_STL_obj_points)

        # Create a point cloud mesh for STL points and assign color
        stl_points["colors"] = np.tile(stl_color, (hip_STL_obj_points.shape[0], 1))

        # Increase the size of the points by using 'glyph' to represent the points as spheres
        sphere_radius = 2  # Set sphere size to 20
        glyph = stl_points.glyph(geom=pv.Sphere(radius=sphere_radius), scale=True, orient=False)

        # Add the STL points to the plotter with the new size
        self.plotter.add_mesh(glyph, scalars="colors", rgb=True, label="STL Points")

        while True:
            try:
                if not self.point_queue.empty():
                    point = self.point_queue.get()
                    if point == 'exit':
                        self.plotter.close()
                        break
                    if point == 'no data':
                        print('Pointer is not visible')
                        self.last_updated_point = None  # Clear the last updated point

                        # Remove the existing marker if it exists
                        if sphere_actor is not None:
                            self.plotter.remove_actor(sphere_actor)
                            sphere_actor = None  # Ensure the reference is cleared
                    else:
                        # Convert the point to a numpy array and apply the transformation
                        point = np.array(eval(point), dtype=np.float32)
                        color_code = int(point[3])  # Ensure `colour` is an integer

                        point = [point[0], -point[1], point[2]]  # Adjust Z axis orientation if needed

                        transformed_point = np.dot(self.transformation_matrix[:3, :3],
                                                   point) + self.transformation_matrix[:3, 3]

                        self.last_updated_point = transformed_point

                        closest_point_idx = self.mesh.find_closest_point(tuple(transformed_point))
                        self.mesh["colors"][closest_point_idx] = color_map.get(color_code, initial_color)
                        self.color_state[closest_point_idx] = color_code  # Update the color state

                        # Update the mesh with the new colors
                        self.plotter.update()


                else:
                    self.last_updated_point = None

                # Handle marker visibility based on the last updated point
                if self.last_updated_point is None:
                    # Remove the marker if it exists when no valid point is available
                    if sphere_actor is not None:
                        self.plotter.remove_actor(sphere_actor)
                        sphere_actor = None  # Clear the reference
                else:
                    # Create and display the marker for the current valid point
                    # Remove the old marker if it exists
                    if sphere_actor is not None:
                        self.plotter.remove_actor(sphere_actor)

                    height = 30
                    shaft_radius = 0.5
                    head_radius = 1

                    head = pv.Sphere(center=self.last_updated_point, radius=2)
                    half_height = height / 2

                    # Define the new center for the shaft, shifting by half the height along the x-axis
                    shaft_center = (
                        self.last_updated_point[0] + half_height, self.last_updated_point[1],
                        self.last_updated_point[2])

                    shaft = pv.Cylinder(center=shaft_center, direction=(-1, 0, 0), radius=0.25,
                                        height=height - head_radius)

                    # Combine the shaft and the head (sphere) to form the complete marker
                    marker = shaft + head
                    sphere_actor = self.plotter.add_mesh(marker, color=(0.75, 0.75, 0.75), label="Rotary Burr")

                # Update the mesh with the new colors
                self.plotter.render()

                # Process events to keep the UI responsive
                self.plotter.app.processEvents()

            except Exception as e:
                print(f"Error: {e}")

    def save_to_file(self, points, filename):
        # Save the adjusted points to a CSV file without headers
        with open(filename, 'w', newline='') as file:
            writer = csv.writer(file)
            for point in points:
                writer.writerow(point)  # Write each adjusted point without headers

    def Revisionplot_pointCloud_with_background(self):
        # File path for the 3D femur model
        while True:
            # Check if femure_plane has been updated
            if self.femure_plane is not None:
                # print("femure_plane has been updated!")
                break  # Exit the loop when the condition is met

            # Log and wait for a short duration before checking again
            # print("Waiting for femure_plane to be updated...")
            time.sleep(0.5)  # Avoid tight looping and reduce CPU usage
        self.color_state = {}  # Dictionary to store the color state of each point
        distance_threshold = 0.01  # Define your distance threshold here
        obj_file_path = r'D:\SpineSurgery\pythonProject\app\static\images\hip-bone\3D\3Dhip\GRADE_2B.obj'
        self.mesh = pv.read(obj_file_path)
        self.plotter = pvqt.BackgroundPlotter()
        self.plotter.window().window().setMenuBar(None)
        self.plotter.window().window().findChild(QtWidgets.QToolBar)
        self.plotter.window().setWindowFlags(Qt.FramelessWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.plotter.set_background("black")
        # self.plotter.show()
        # self.plotter.window_size = (1920, 1080)
        self.plotter.app_window.showMaximized()
        try:
            Hip_variable_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                             'revision_hip_variables.pickle')
            with open(Hip_variable_path, 'rb') as handle:
                hip_dict = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")

        ASIS = utils.find_new_point_location(old_plane_points=hip_dict['ASIS']['Plane'],
                                             new_plane_points=self.femure_plane,
                                             old_marked_point=hip_dict['ASIS']['C'])
        TF = utils.find_new_point_location(old_plane_points=hip_dict['TF']['Plane'],
                                           new_plane_points=self.femure_plane,
                                           old_marked_point=hip_dict['TF']['C'])
        PSIS = utils.find_new_point_location(old_plane_points=hip_dict['PSIS']['Plane'],
                                             new_plane_points=self.femure_plane,
                                             old_marked_point=hip_dict['PSIS']['C'])
        TAL = utils.find_new_point_location(old_plane_points=hip_dict['TAL']['Plane'],
                                            new_plane_points=self.femure_plane,
                                            old_marked_point=hip_dict['TAL']['C'])
        IT = utils.find_new_point_location(old_plane_points=hip_dict['IT']['Plane'],
                                           new_plane_points=self.femure_plane,
                                           old_marked_point=hip_dict['IT']['C'])
        # PS = utils.find_new_point_location(old_plane_points=hip_dict['PS']['Plane'],
        #                                    new_plane_points=self.femure_plane,
        #                                    old_marked_point=hip_dict['PS']['C'])

        camera_reference = np.array([
            ASIS,
            TF,
            PSIS,
            IT,
            TAL,
            # PS
        ])
        camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
        hip_STL_obj_points = np.array([
            [-41.2922211, 433.4049377, 194.4227905],  # ASIS
            [-108.0319901, 358.8612061, 117.9057465],  # TF
            [-47.8687057, 331.510376, 82.7817383],  # PSIS
            [54.1078644, 397.3883972, 98.4199829],  # IT
            [43.462677, 386.5711975, 151.892395],  # TAL
            # [66.2467041, 339.0275879, 203.1392975],  # PS
        ])
        # Calculate transformation matrix based on the camera reference
        self.transformation_matrix = self.calculate_transformation_matrix_with_scaling(camera_reference,
                                                                                       hip_STL_obj_points)

        initial_color = [240 / 255, 220 / 255, 170 / 255]  # RGB normalized for bone color
        colors = np.tile(initial_color, (self.mesh.n_points, 1))
        self.mesh["colors"] = colors

        # Add the femur model (mesh) with per-point colors
        self.plotter.add_mesh(self.mesh, scalars="colors", rgb=True, label="Femur Model")
        self.last_updated_point = None
        sphere_actor = None
        color_map = {
            0: [0.0, 1.0, 0.0],  # Green for condition 0
            1: [1.0, 1.0, 0.0],  # Yellow for condition 1
            2: [1.0, 0.0, 0.0],  # Red for condition 2
            4: [64 / 255, 224 / 255, 208 / 255],  # Turquoise Blue for condition 4
        }

        stl_color = np.array([127 / 255, 1, 0])  # Chartreuse Green for STL points
        stl_points = pv.PolyData(hip_STL_obj_points)

        # Create a point cloud mesh for STL points and assign color
        stl_points["colors"] = np.tile(stl_color, (hip_STL_obj_points.shape[0], 1))

        # Increase the size of the points by using 'glyph' to represent the points as spheres
        sphere_radius = 2  # Set sphere size to 20
        glyph = stl_points.glyph(geom=pv.Sphere(radius=sphere_radius), scale=True, orient=False)

        # Add the STL points to the plotter with the new size
        self.plotter.add_mesh(glyph, scalars="colors", rgb=True, label="STL Points")
        source_points = []
        while True:
            try:
                if not self.point_queue.empty():
                    point = self.point_queue.get()
                    if point == 'exit':
                        # revision_point_cloud_file_Path = os.path.join(self.current_dir, '..', '..',
                        #                                               'registration_data', 'revisionPointCloud.csv')
                        # # Ensure the directory exists
                        # os.makedirs(os.path.dirname(revision_point_cloud_file_Path), exist_ok=True)
                        # self.save_to_file(points=source_points, filename=revision_point_cloud_file_Path)
                        #
                        # revision_point_cloud_file_path = os.path.join('..', '..', 'registration_data',
                        #                                               'revisionPointCloud.csv')
                        # target_file_path = os.path.join('..', '..', 'registration_data', 'GRADE_2B_HipRev.txt')
                        #
                        # source_points = load_point_cloud_from_csv(revision_point_cloud_file_path)
                        # target_points = load_point_cloud_from_txt(target_file_path)

                        self.plotter.close()
                        break
                    if point == 'no data':
                        print('Pointer is not visible')
                        self.last_updated_point = None  # Clear the last updated point

                        # Remove the existing marker if it exists
                        if sphere_actor is not None:
                            self.plotter.remove_actor(sphere_actor)
                            sphere_actor = None  # Ensure the reference is cleared
                    else:
                        # Convert the point to a numpy array and apply the transformation
                        point = np.array(eval(point), dtype=np.float32)
                        color_code = int(point[3])  # Ensure `colour` is an integer

                        point = [point[0], -point[1], point[2]]  # Adjust Z axis orientation if needed

                        transformed_point = np.dot(self.transformation_matrix[:3, :3],
                                                   point) + self.transformation_matrix[:3, 3]

                        self.last_updated_point = transformed_point

                        source_points.append(transformed_point)

                        closest_point_idx = self.mesh.find_closest_point(tuple(transformed_point))
                        self.mesh["colors"][closest_point_idx] = color_map.get(color_code, initial_color)
                        self.color_state[closest_point_idx] = color_code  # Update the color state

                        # Update the mesh with the new colors
                        self.plotter.update()


                else:
                    self.last_updated_point = None

                # Handle marker visibility based on the last updated point
                if self.last_updated_point is None:
                    # Remove the marker if it exists when no valid point is available
                    if sphere_actor is not None:
                        self.plotter.remove_actor(sphere_actor)
                        sphere_actor = None  # Clear the reference
                else:
                    # Create and display the marker for the current valid point
                    # Remove the old marker if it exists
                    if sphere_actor is not None:
                        self.plotter.remove_actor(sphere_actor)

                    height = 30
                    shaft_radius = 0.5
                    head_radius = 1

                    head = pv.Sphere(center=self.last_updated_point, radius=2)
                    half_height = height / 2

                    # Define the new center for the shaft, shifting by half the height along the x-axis
                    shaft_center = (
                        self.last_updated_point[0] + half_height, self.last_updated_point[1],
                        self.last_updated_point[2])

                    shaft = pv.Cylinder(center=shaft_center, direction=(-1, 0, 0), radius=0.25,
                                        height=height - head_radius)

                    # Combine the shaft and the head (sphere) to form the complete marker
                    marker = shaft + head
                    sphere_actor = self.plotter.add_mesh(marker, color=(0.75, 0.75, 0.75), label="Rotary Burr")

                # Update the mesh with the new colors
                self.plotter.render()

                # Process events to keep the UI responsive
                self.plotter.app.processEvents()

            except Exception as e:
                print(f"Error: {e}")

    def load_updated_mesh(self, mesh_file_path, color_state_file_path):
        try:
            # Load the saved mesh from file
            self.mesh = pv.read(mesh_file_path)

            # Load the color state from the pickle file
            with open(color_state_file_path, 'rb') as f:
                self.color_state = pickle.load(f)

            print("Updated mesh and color state loaded successfully.")
        except Exception as e:
            print(f"Error loading mesh and color state: {e}")

    def revisionKneepointcolor_update(self):
        # File path for the 3D femur model
        while True:
            # Check if femure_plane has been updated
            if self.femure_plane is not None:
                # print("femure_plane has been updated!")
                break  # Exit the loop when the condition is met

            # Log and wait for a short duration before checking again
            # print("Waiting for femure_plane to be updated...")
            time.sleep(0.5)  # Avoid tight looping and reduce CPU usage
        self.color_state = {}  # Dictionary to store the color state of each point
        distance_threshold = 5  # Define your distance threshold here
        obj_file_path = r'D:\SpineSurgery\pythonProject\app\static\images\hip-bone\3D\3Dhip\pelvis.obj'
        self.mesh = pv.read(obj_file_path)
        # Initialize the plotter in full-screen mode
        self.plotter = pvqt.BackgroundPlotter()
        self.plotter.window().window().setMenuBar(None)
        self.plotter.window().window().findChild(QtWidgets.QToolBar)
        self.plotter.window().setWindowFlags(Qt.FramelessWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.plotter.set_background("black")

        self.plotter.show()
        # self.plotter.window_size = (1920, 1080)
        self.plotter.app_window.showMaximized()

        try:
            Hip_variable_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                             'hip_variables.pickle')
            with open(Hip_variable_path, 'rb') as handle:
                hip_dict = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")

        FO = utils.find_new_point_location(old_plane_points=hip_dict['FO']['Plane'],
                                           new_plane_points=self.femure_plane,
                                           old_marked_point=hip_dict['FO']['C'])
        SU = utils.find_new_point_location(old_plane_points=hip_dict['SU']['Plane'],
                                           new_plane_points=self.femure_plane,
                                           old_marked_point=hip_dict['SU']['C'])
        CA = utils.find_new_point_location(old_plane_points=hip_dict['CA']['Plane'],
                                           new_plane_points=self.femure_plane,
                                           old_marked_point=hip_dict['CA']['C'])
        AT = utils.find_new_point_location(old_plane_points=hip_dict['AT']['Plane'],
                                           new_plane_points=self.femure_plane,
                                           old_marked_point=hip_dict['AT']['C'])
        PT = utils.find_new_point_location(old_plane_points=hip_dict['PT']['Plane'],
                                           new_plane_points=self.femure_plane,
                                           old_marked_point=hip_dict['PT']['C'])
        camera_reference = np.array([
            FO,
            SU,
            CA,
            AT,
            PT
        ])
        camera_reference[:, 1] = -np.abs(camera_reference[:, 1])
        hip_STL_obj_points = np.array([
            [-45.5239716, -2.5871658, -154.1749573],  # F0
            [-63.6134491, 29.2462463, -157.989151],  # SU
            [-22.7026062, 2.6151695, -149.2989197],  # CA
            [-30.7326965, 8.4291382, -133.5983887],  # AT
            [-15.77771, 17.0255318, -159.7479401],  # PT
        ])
        self.transformation_matrix = self.calculate_transformation_matrix_with_scaling(camera_reference,
                                                                                       hip_STL_obj_points)
        initial_color = [240 / 255, 220 / 255, 170 / 255]  # RGB normalized for bone color
        colors = np.tile(initial_color, (self.mesh.n_points, 1))
        self.mesh["colors"] = colors
        stl_color = np.array([127 / 255, 1, 0])  # Chartreuse Green for STL points
        stl_points = pv.PolyData(hip_STL_obj_points)

        # Create a point cloud mesh for STL points and assign color
        stl_points["colors"] = np.tile(stl_color, (hip_STL_obj_points.shape[0], 1))

        # Increase the size of the points by using 'glyph' to represent the points as spheres
        sphere_radius = 2  # Set sphere size to 20
        glyph = stl_points.glyph(geom=pv.Sphere(radius=sphere_radius), scale=True, orient=False)

        # Add the STL points to the plotter with the new size
        self.plotter.add_mesh(glyph, scalars="colors", rgb=True, label="STL Points")
        # Add the femur model (mesh) with per-point colors
        self.plotter.add_mesh(self.mesh, scalars="colors", rgb=True, label="Femur Model")
        try:
            femure_point_cloud_file_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                                        'points_dict.pickle')
            with open(femure_point_cloud_file_path, 'rb') as handle:
                point_cloud_dict = pickle.load(handle)
                print("Femur point cloud loaded successfully")
        except Exception as e:
            print(f"Error loading files: {e}")
            return
        point_data = []
        for key in point_cloud_dict:
            old_marked_point = point_cloud_dict[key]['C']  # Old marked point
            old_plane_points = point_cloud_dict[key]['Plane']  # Plane points

            new_location = utils.find_new_point_location(old_plane_points, self.femure_plane, old_marked_point)
            new_location[1] = -new_location[1]
            transformed_point = np.dot(self.transformation_matrix[:3, :3],
                                       new_location) + self.transformation_matrix[:3, 3]
            point_data.append(transformed_point)
            # stl_transformed_points.append(transformed_point)
            closest_point_idx = self.mesh.find_closest_point(tuple(transformed_point))
            self.mesh["colors"][closest_point_idx] = [64 / 255, 224 / 255, 208 / 255]

        self.last_updated_point = None
        sphere_actor = None
        color_map = {
            0: [0.0, 1.0, 0.0],  # Green for condition 0
            1: [1.0, 1.0, 0.0],  # Yellow for condition 1
            2: [1.0, 0.0, 0.0],  # Red for condition 2
        }
        # extra_object_point = np.array([
        #     [-30.2084045, 24.9668236, -176.967453],
        #     [-52.2232208, 26.014534, -172.1065063],
        #     [-55.1667786, 22.2795334, -136.0496216],
        #     [-22.6136017,  3.6976357, - 142.1740723],
        #     [ -44.6776581,  15.8541069, -130.1384888],
        #     [-60.5101013,   27.5547848, - 143.8187714],
        #     [-57.6914825,  24.7275887, - 138.7390442],
        #     [-17.1403198,  20.8043327, - 164.3099823]
        #
        # ])
        file_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                 'accetabulum_Stl_points.txt')
        points = []

        # Open the file and read line by line
        with open(file_path, 'r') as file:
            for line in file:
                # Strip the square brackets and split by spaces
                line = line.strip()[1:-1]  # Remove the square brackets
                point = np.array([float(i) for i in line.split()])  # Convert to float and create an array
                points.append(point)

        # Convert the list of points into a numpy array
        points_array = np.array(points)
        # threshold = 5.0  # Adjust this value based on your requirements
        #
        # # # hip_STL_obj_points = np.vstack((hip_STL_obj_points, point_data))
        # # hip_STL_obj_points = np.vstack((point_data, points_array))
        # for i in point_data:
        #     distances = np.linalg.norm(self.mesh.points - i, axis=1)
        #
        #     # Find all points within the threshold distance
        #     threshold_indices = np.where(distances <= threshold)[0]
        #
        #     # For each point within the threshold distance, update its color
        #     for idx in threshold_indices:
        #         self.mesh["colors"][idx] = [64 / 255, 224 / 255, 208 / 255]  # Color assignment

        # hull = ConvexHull(hip_STL_obj_points)

        # # Visualize the hull
        #
        # hull_mesh = pv.PolyData(hip_STL_obj_points)
        # hull_faces = [[len(simp)] + list(simp) for simp in hull.simplices]
        # hull_mesh.faces = np.hstack(hull_faces)
        #
        # # Add the initial mesh to the plotter
        # self.plotter.add_mesh(hull_mesh, color="white", opacity=0.5, label="Boundary Mesh")
        # time.sleep(5)  # Pause for 2 seconds
        # self.plotter.add_mesh(hull_mesh, color="green", opacity=0.5, label="Boundary Mesh")

        # Main loop to process points and update mesh
        while True:
            try:
                if not self.point_queue.empty():
                    point = self.point_queue.get()
                    if point == 'exit':
                        self.plotter.close()
                        break
                    if point == 'no data':
                        print('Pointer is not visible')
                        self.last_updated_point = None  # Clear the last updated point

                        # Remove the existing marker if it exists
                        if sphere_actor is not None:
                            self.plotter.remove_actor(sphere_actor)
                            sphere_actor = None  # Ensure the reference is cleared
                    else:
                        # Convert the point to a numpy array and apply the transformation
                        point = np.array(eval(point), dtype=np.float32)
                        color_code = int(point[3])  # Ensure `colour` is an integer

                        point = [point[0], - point[1], point[2]]  # Adjust Z axis orientation if needed
                        transformed_point = np.dot(self.transformation_matrix[:3, :3],
                                                   point) + self.transformation_matrix[:3, 3]

                        self.last_updated_point = transformed_point

                        distances = np.linalg.norm(self.mesh.points - self.last_updated_point, axis=1)

                        # Find points within the distance threshold
                        points_in_range = np.where(distances <= distance_threshold)[0]

                        # Iterate through the points in range and update their colors
                        for idx in points_in_range:
                            current_state = self.color_state.get(idx, -1)  # -1 means no state yet

                            if current_state < color_code:  # Allow forward progression only
                                self.mesh["colors"][idx] = color_map.get(color_code, [1.0, 1.0, 1.0])
                                self.color_state[idx] = color_code  # Update the color state
                        print(
                            f'transformed_point {transformed_point} color_code{color_code} points_in_range {points_in_range} ')
                        # Update the mesh with the new colors
                        self.plotter.update()

                else:
                    self.last_updated_point = None
                # Handle marker visibility based on the last updated point
                if self.last_updated_point is None:
                    # Remove the marker if it exists when no valid point is available
                    if sphere_actor is not None:
                        self.plotter.remove_actor(sphere_actor)
                        sphere_actor = None  # Clear the reference
                else:
                    # Create and display the marker for the current valid point
                    # Remove the old marker if it exists
                    if sphere_actor is not None:
                        self.plotter.remove_actor(sphere_actor)

                    height = 15
                    shaft_radius = 0.5
                    head_radius = 5

                    # Create the semi-spherical hammerhead
                    head = pv.Sphere(center=self.last_updated_point, radius=head_radius)
                    plane_origin = (
                        self.last_updated_point[0] - head_radius, self.last_updated_point[1],
                        self.last_updated_point[2])
                    plane_normal = (-1, 0, 0)  # Plane cutting along the x-axis
                    semi_head = head.clip(normal=plane_normal, origin=plane_origin)

                    # Adjust the shaft
                    half_height = height / 2
                    shaft_center = (
                        self.last_updated_point[0] + half_height - head_radius,  # Shift by head_radius for alignment
                        self.last_updated_point[1],
                        self.last_updated_point[2]
                    )
                    shaft = pv.Cylinder(center=shaft_center, direction=(1, 0, 0), radius=shaft_radius,
                                        height=height - head_radius)

                    # Combine the shaft and semi-sphere head
                    marker = shaft + semi_head
                    sphere_actor = self.plotter.add_mesh(marker, color=(0.75, 0.75, 0.75), label="Rotary Burr")

                # Update the mesh with the new colors
                # self.plotter.update()

                # Process events to keep the UI responsive
                self.plotter.app.processEvents()

            except Exception as e:
                print(f"Error: {e}")

    def hip_TP2_variable_MarkingPipeline(self):
        try:
            pickle_path_TP = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                          'tp_variable.pickle')
            with open(pickle_path_TP, 'rb') as handle:
                TP = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")

        variable_dict = {}
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        point_reg = ['TP2']
        framecount = 0
        pointer_data = []
        while True:
            value = utils.checkLoopExitCondition()
            if value != 'TP2_marking':
                return
            ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                            500)
            ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                              stFrameInfo, 500)
            if ret == 0 and ret2 == 0:
                img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

                rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)
                try:
                    detections_right = utils.find_contours_Kmeans(rec_img_right)
                    detections_left = utils.find_contours_Kmeans(rec_img_left)
                    if len(detections_right) == 6 and 6 == len(detections_left):
                        detections_right = sorted(detections_right, key=lambda x: x[0])
                        detections_left = sorted(detections_left, key=lambda x: x[0])
                        # Extract first 3 coordinates
                        first_three_right = detections_right[:3]
                        first_three_left = detections_left[:3]
                        next_three_right = detections_right[-3:]
                        next_three_left = detections_left[-3:]

                        Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                         rec_img_left)
                        Pointer_traker.find_depth()
                        pointer_tip_gcs = Pointer_traker.getStylusPoint()
                        pointer_data.append(pointer_tip_gcs)
                        if len(pointer_data) < 20:
                            continue
                        pointer_tip_gcs = utils.filter_point_registration_data(pointer_data)
                        femure = TriangleTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right,
                                                 rec_img_left)
                        femure.find_depth()
                        F_points = femure.getLEDcordinates()

                        if F_points[1][1] >= F_points[0][1]:
                            self.FemurePlane = [F_points[2], F_points[0], F_points[1]]
                        else:
                            self.FemurePlane = [F_points[2], F_points[1], F_points[0]]

                        pointRegistered = utils.find_new_point_location(old_plane_points=tuple(self.FemurePlane),
                                                                        new_plane_points=TP['TP']['Plane'],
                                                                        old_marked_point=pointer_tip_gcs)
                        if framecount == 0:
                            print(f'Point registered {point_reg[framecount]}')

                            pickle_path_TP2 = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                                           'tp2_variable.pickle')
                            os.makedirs(os.path.dirname(pickle_path_TP2), exist_ok=True)
                            variable_memory = {'C': pointRegistered, 'Plane': self.FemurePlane}
                            variable_dict[point_reg[framecount]] = variable_memory
                            with open(pickle_path_TP2, 'wb') as handle:
                                pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
                            print("Pickle dump successful")

                            utils.play_notification_sound()
                            utils.writeRegisteredPoint(point_reg[framecount])
                            print(f'Point registered {point_reg[framecount]}')
                            framecount += 1
                    else:
                        print("no data[0x%x]" % ret)
                    if framecount == 1:
                        break
                except Exception as e:
                    print(f'Error {e}')
        self.dual_cam.stop_grabbing()
        return 0

    def handle_position(self):

        USE_ROBOT = True
        # try:
        #     pickle_path_TP = os.path.join(self.current_dir, '..', '..', 'registration_data',
        #                                   'tp_variable.pickle')
        #     with open(pickle_path_TP, 'rb') as handle:
        #         TP = pickle.load(handle)
        #         print("TP variables loaded successfully")
        # except Exception as e:
        #     print(f"Error loading TP variables {e}")
        try:
            Hip_variable_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                             'hip_variables.pickle')
            with open(Hip_variable_path, 'rb') as handle:
                hip_dict = pickle.load(handle)
                print("hip variables loaded successfully")
            Hip_point_cloud_file = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                                'RingPointCloud.pickle')
            with open(Hip_point_cloud_file, 'rb') as handle:
                pointCloud_dict = pickle.load(handle)
                print("hip PointCloud  loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")

        for key, value in pointCloud_dict.items():
            locals()[key] = value['C']
        point_reg = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        pointer_data = []
        while True:
            value = utils.checkLoopExitCondition()
            if value.lower() != 'handle-position':
                return
            ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                            500)
            ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                              stFrameInfo, 500)
            if ret == 0 and ret2 == 0:
                img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

                rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)
                try:
                    detections_right = utils.find_contours_Kmeans(rec_img_right)
                    detections_left = utils.find_contours_Kmeans(rec_img_left)
                    if len(detections_right) == 6 and 6 == len(detections_left):
                        # Sort by the second coordinate (y-axis)
                        detections_right = sorted(detections_right, key=lambda x: x[1], reverse=True)
                        detections_left = sorted(detections_left, key=lambda x: x[1], reverse=True)
                        first_three_right = detections_right[:3]
                        first_three_left = detections_left[:3]
                        first_three_right = sorted(first_three_right, key=lambda x: x[0])
                        first_three_left = sorted(first_three_left, key=lambda x: x[0])
                        detections_right = detections_right[3:]
                        detections_left = detections_left[3:]

                        # Sort the detections by the first coordinate (x-axis)
                        detections_right = sorted(detections_right, key=lambda x: x[0])
                        detections_left = sorted(detections_left, key=lambda x: x[0])
                        # Extract first 3 detections based on x-coordinate (left to right)
                        next_three_right = detections_right[:3]
                        next_three_left = detections_left[:3]

                        # Extract the last 2 detections based on the first coordinate (x-axis)
                        # last_two_right = detections_right[-2:]
                        # last_two_left = detections_left[-2:]
                        #
                        # # Optionally, you can sort the last two by y-coordinate if needed
                        # last_two_right = sorted(last_two_right, key=lambda x: x[0])
                        # last_two_left = sorted(last_two_left, key=lambda x: x[0])
                        #
                        # EndEffector = TriangleTracker(tuple(last_two_right), tuple(last_two_left), rec_img_right,
                        #                               rec_img_left)
                        # EndEffector.find_depth()
                        # # pointer_tip_gcs = Pointer_traker.getStylusPoint()
                        # p = EndEffector.get2LEDcordinates()

                        # pointer_data.append(pointer_tip_gcs)
                        # if len(pointer_data) < 10:
                        #     continue
                        # pointer_tip_gcs = utils.filter_point_registration_data(pointer_data)
                        femure = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                 rec_img_left)
                        femure.find_depth()
                        F_points = femure.getLEDcordinates()

                        robot_tracker = TriangleTracker(tuple(first_three_right), tuple(first_three_left),
                                                        rec_img_right,
                                                        rec_img_left)
                        robot_tracker.find_depth()
                        R_points = robot_tracker.getLEDcordinates()
                        # data = read_encoder_data()
                        #
                        # if '6' in data:  # Check if key '6' exists
                        #     data['6'] = -data['6']
                        #     data['5'] = data['5'] + 12.33   # Toggle the sign
                        #     # data['6'] = data['6'] + 4.95  # Toggle the sign
                        #
                        # if '1' in data:  # Check if key '1' exists
                        #     data['1'] = -data['1']  # Toggle the sign
                        # print(data)
                        # anteviersiondiff = data['4'] + data['5'] + data['6']
                        # inclinationdiff =  data['1'] + data['3']
                        # print(f'F_points {F_points}')
                        # print(f'R_points {R_points}')
                        # break
                        Mid_point_RoboTracker = np.abs((R_points[1] + R_points[0]) / 2)
                        RoboTracker_y = Mid_point_RoboTracker - R_points[2]
                        RoboTracker_x = Mid_point_RoboTracker - R_points[1]

                        RoboTrackervector_z = np.cross(RoboTracker_x, RoboTracker_y)

                        # Normalize the Z-axis vector (to make it a unit vector)
                        RoboTrackernorm_z = np.linalg.norm(RoboTrackervector_z)
                        RoboTrackernormalized_z = RoboTrackervector_z / RoboTrackernorm_z
                        RoboTrackernorm_x = np.linalg.norm(RoboTracker_x)
                        RoboTrackernorm_y = np.linalg.norm(RoboTracker_y)
                        RoboTrackernormalized_x = RoboTracker_x / RoboTrackernorm_x if RoboTrackernorm_x != 0 else RoboTracker_x
                        RoboTrackernormalized_y = RoboTracker_y / RoboTrackernorm_y if RoboTrackernorm_y != 0 else RoboTracker_y
                        if F_points[1][1] >= F_points[0][1]:
                            hip_plane = [F_points[2], F_points[0], F_points[1]]
                        else:
                            hip_plane = [F_points[2], F_points[1], F_points[0]]

                        mid_femure = np.abs((F_points[1] + F_points[0]) / 2)

                        # print(f' F_points {F_points}  mid_femure {mid_femure}')

                        scale = 68 / utils.distance_3d(mid_femure, F_points[2])

                        SU = utils.find_new_point_location(hip_dict['SU']['Plane'], hip_plane, hip_dict['SU']['C'])
                        CA = utils.find_new_point_location(hip_dict['CA']['Plane'], hip_plane, hip_dict['CA']['C'])
                        AT = utils.find_new_point_location(hip_dict['AT']['Plane'], hip_plane, hip_dict['AT']['C'])
                        PT = utils.find_new_point_location(hip_dict['PT']['Plane'], hip_plane, hip_dict['PT']['C'])

                        # A, B, C = Pointer_traker.getLEDcordinates()
                        # Mid_point = np.abs((AT + PT) / 2)
                        # mid = (B + A) / 2

                        pointCloud = []
                        for char in point_reg:
                            result = utils.find_new_point_location(
                                pointCloud_dict[char]['Plane'],
                                hip_plane,
                                pointCloud_dict[char]['C']
                            )
                            pointCloud.append(result)

                        pointCloud_array = np.array(pointCloud)
                        # accetabulumn Local cordinate system with normalization
                        accetabulumnAxis_x, com = fit_plane(pointCloud_array)
                        accetabulumnAxisXnorm = accetabulumnAxis_x / np.linalg.norm(accetabulumnAxis_x)
                        # print(f'SU {SU} CA {CA}')
                        accetabulumnAxis_y = np.array(SU) - np.array(CA)  # Directly subtract the two arrays
                        accetabulumnAxis_ynorm = accetabulumnAxis_y / np.linalg.norm(accetabulumnAxis_y)
                        accetabulumnAxis_z = np.cross(accetabulumnAxis_x, accetabulumnAxis_y)
                        # Normalize the resulting normal vector (Z-axis)
                        accetabulumnAxis_z_norm = accetabulumnAxis_z / np.linalg.norm(accetabulumnAxis_z)

                        # accetabulumn diameter calculation
                        disance_SU_CA = utils.distance_3d(SU, CA)
                        # accetabulumn_diameterinMM= disance_At_PT * scale1
                        accetabulumn_diameterinMM = disance_SU_CA * scale

                        # accetabulumn Angle Calculations
                        #def calculate_ANTERVERSION_INCLINATION_angles(y_LCSr, x_ACS, z_ACS):
                        # Calculate INCLINATION angle
                        # y_LCSr_proj_xy = y_LCSr - np.dot(y_LCSr, z_ACS) * z_ACS
                        # cos_INCLINATION = np.dot(y_LCSr_proj_xy, x_ACS) / (
                        #             np.linalg.norm(y_LCSr_proj_xy) * np.linalg.norm(x_ACS))
                        # INCLINATION_angle = 90 - np.degrees(np.arccos(np.clip(cos_INCLINATION, -1.0, 1.0)))

                        ANTERVERSION_angle, INCLINATION_angle = utils.calculate_ANTERVERSION_INCLINATION_angles(
                            RoboTrackernormalized_y, accetabulumnAxisXnorm,
                            accetabulumnAxis_z_norm)

                        def angle_between_vectors(u, v):
                            # # Normalize the vectors
                            # u = u / np.linalg.norm(u)
                            # v = v / np.linalg.norm(v)

                            # Compute the dot product
                            dot_product = np.dot(u, v)

                            # Compute the angle using arccos
                            angle = np.arccos(dot_product)

                            return math.degrees(angle)  # Angle in radians

                        INCLINATION_angle = angle_between_vectors(RoboTrackernormalized_y, accetabulumnAxis_ynorm)
                        # print(f"ANTERVERSION angle: {ANTERVERSION_angle:.2f} degrees  serial diff {anteviersiondiff}     {(abs(ANTERVERSION_angle) - abs(anteviersiondiff)):.2f}")
                        # print(f"INCLINATION angle: {INCLINATION_angle:.2f} degrees serail diff {inclinationdiff}    {(INCLINATION_angle - abs(inclinationdiff)):.2f}")
                        if USE_ROBOT:
                            from xarm.wrapper import XArmAPI

                            # Connect to the xArm6
                            arm = XArmAPI('*************')

                            # Establish connection to the arm
                            arm.connect()

                            if arm.connected:
                                arm.motion_enable(enable=True)  # Enable motion
                                # arm.set_mode(0)  # Set to position control mode
                                # arm.set_state(0)  # Set to ready state
                                # code, position = arm.get_position()
                                # code, position = arm.set_position_aa()
                                code, position = arm.get_position_aa()

                                if code == 0:  # Successful retrieval
                                    x, y, z, rx, ry, rz = position  # RX, RY, RZ are in radians
                                    print(f"Current Position:\nX: {x} mm\nY: {y} mm\nZ: {z} mm")
                                    print(f"RX: {rx} radians\nRY: {ry} radians\nRZ: {rz} radians")
                                    data = {"X": x, "Y": y, "Z": z, "RX": rx, "RY": ry, "RZ": rz}

                                    with open("position_data.json", "w") as file:
                                        json.dump(data, file, indent=4)

                                    print("Position data saved successfully.")

                                data1 = abs(ANTERVERSION_angle) - rx
                                data2 = abs(INCLINATION_angle) - (180 - ry)

                            print(f"ANTERVERSION angle: {ANTERVERSION_angle:.2f} degrees")
                            print(f"INCLINATION angle: {INCLINATION_angle:.2f} degrees")
                            arm.disconnect()
                            data = [int(data2), int(data1), int(accetabulumn_diameterinMM)]
                            utils.SendDataToFrontEnd(data)
                            # time.sleep(0.5)
                    else:
                        print("no data[0x%x]" % ret)
                except Exception as e:
                    print(f'Error {e}')

    def final_cup_position(self):
        try:
            pickle_path_TP = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                          'tp_variable.pickle')
            with open(pickle_path_TP, 'rb') as handle:
                TP = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")
        try:
            Hip_variable_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                             'hip_variables.pickle')
            with open(Hip_variable_path, 'rb') as handle:
                hip_dict = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")

        variable_dict = {}
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        framecount = 0
        pointer_data = []
        while True:
            value = utils.checkLoopExitCondition()
            if value.lower() != 'final-cup-position':
                return
            ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                            500)
            ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                              stFrameInfo, 500)
            if ret == 0 and ret2 == 0:
                img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

                rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)
                try:
                    detections_right = utils.find_contours_Kmeans(rec_img_right)
                    detections_left = utils.find_contours_Kmeans(rec_img_left)
                    if len(detections_right) == 6 and 6 == len(detections_left):
                        detections_right = sorted(detections_right, key=lambda x: x[0])
                        detections_left = sorted(detections_left, key=lambda x: x[0])
                        # Extract first 3 coordinates
                        first_three_right = detections_right[:3]
                        first_three_left = detections_left[:3]
                        next_three_right = detections_right[-3:]
                        next_three_left = detections_left[-3:]

                        Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                         rec_img_left)
                        Pointer_traker.find_depth()
                        pointer_tip_gcs = Pointer_traker.getStylusPoint()
                        pointer_data.append(pointer_tip_gcs)
                        if len(pointer_data) < 20:
                            continue
                        pointer_tip_gcs = utils.filter_point_registration_data(pointer_data)
                        femure = TriangleTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right,
                                                 rec_img_left)
                        femure.find_depth()
                        F_points = femure.getLEDcordinates()

                        if F_points[1][1] >= F_points[0][1]:
                            hip_plane = [F_points[2], F_points[0], F_points[1]]
                        else:
                            hip_plane = [F_points[2], F_points[1], F_points[0]]

                        mid_femure = np.abs((F_points[1] + F_points[0]) / 2)

                        # print(f' F_points {F_points}  mid_femure {mid_femure}')

                        scale = 107.5 / utils.distance_3d(mid_femure, F_points[2])

                        SU = utils.find_new_point_location(hip_dict['SU']['Plane'], hip_plane, hip_dict['SU']['C'])
                        CA = utils.find_new_point_location(hip_dict['CA']['Plane'], hip_plane, hip_dict['CA']['C'])
                        AT = utils.find_new_point_location(hip_dict['AT']['Plane'], hip_plane, hip_dict['AT']['C'])
                        PT = utils.find_new_point_location(hip_dict['PT']['Plane'], hip_plane, hip_dict['PT']['C'])

                        A, B, C = Pointer_traker.getLEDcordinates()
                        Mid_point = np.abs((AT + PT) / 2)
                        mid = (B + A) / 2

                        disance_At_PT = utils.distance_3d(SU, CA)
                        # # accetabulumn_diameterinMM= disance_At_PT * scale1
                        accetabulumn_diameterinMM = disance_At_PT * scale

                        print(f'Mid_point{Mid_point} tip {pointer_tip_gcs} ')
                        v_AT_to_mid_points = AT - Mid_point

                        v_mid_points_Tracker = A - pointer_tip_gcs
                        dot_product = np.dot(v_AT_to_mid_points, v_mid_points_Tracker)
                        magnitude_v_AT_to_mid_points = np.linalg.norm(v_AT_to_mid_points)
                        magnitude_v_mid_points_Tracker = np.linalg.norm(v_mid_points_Tracker)
                        # Calculate the cosine of the angle
                        cos_theta = dot_product / (magnitude_v_AT_to_mid_points * magnitude_v_mid_points_Tracker)
                        # Calculate the angle in radians
                        angle_radians = np.arccos(
                            np.clip(cos_theta, -1.0, 1.0))  # Use np.clip to handle numerical errors
                        # Convert angle to degrees
                        angle_degrees = np.degrees(angle_radians)
                        print(f'angle_degrees {90 - angle_degrees}')

                        print(
                            f'angle_degrees {angle_degrees} accetabulumn_diameterinMM {accetabulumn_diameterinMM}')
                        a = 41
                        b = 15.6
                        data = [round(a, 3), round(b, 4), round(accetabulumn_diameterinMM, 3)]

                        # Load from file
                        with open("position_data.json", "r") as file:
                            loaded_data = json.load(file)
                        axis_angle_pose = [
                            loaded_data["X"],
                            loaded_data["Y"],
                            loaded_data["Z"],
                            loaded_data["RX"],
                            loaded_data["RY"],
                            loaded_data["RZ"]
                        ]
                        from xarm.wrapper import XArmAPI

                        # Connect to the xArm6
                        arm = XArmAPI('*************')

                        # Establish connection to the arm
                        arm.connect()

                        if arm.connected:
                            arm.motion_enable(enable=True)  # Enable motion
                            arm.set_mode(0)  # Set to position control mode
                            arm.set_state(0)  # Set to ready state
                            arm.set_position_aa(axis_angle_pose, speed=25, mvacc=500, wait=True)
                            print("Retrieved Position Data:", loaded_data)

                        utils.SendDataToFrontEnd(data)
                    else:
                        print("no data[0x%x]" % ret)
                except Exception as e:
                    print(f'Error {e}')
        self.dual_cam.stop_grabbing()
        return 0

    def anterior_pelvic(self):
        try:
            pickle_path_TP = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                          'tp_variable.pickle')
            with open(pickle_path_TP, 'rb') as handle:
                TP = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")
        try:
            Hip_variable_path = os.path.join(self.current_dir, '..', '..', 'registration_data',
                                             'hip_variables.pickle')
            with open(Hip_variable_path, 'rb') as handle:
                hip_dict = pickle.load(handle)
                print("TP variables loaded successfully")
        except Exception as e:
            print(f"Error loading TP variables {e}")

        variable_dict = {}
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        framecount = 0
        pointer_data = []
        while True:
            value = utils.checkLoopExitCondition()
            if value.lower() != 'anterior-pelvic':
                return
            ret = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left, stFrameInfo,
                                                            500)
            ret2 = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                              stFrameInfo, 500)
            if ret == 0 and ret2 == 0:
                img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))
                img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1))

                rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)
                try:
                    detections_right = utils.find_contours_Kmeans(rec_img_right)
                    detections_left = utils.find_contours_Kmeans(rec_img_left)
                    if len(detections_right) == 6 and 6 == len(detections_left):
                        detections_right = sorted(detections_right, key=lambda x: x[0])
                        detections_left = sorted(detections_left, key=lambda x: x[0])
                        # Extract first 3 coordinates
                        first_three_right = detections_right[:3]
                        first_three_left = detections_left[:3]
                        next_three_right = detections_right[-3:]
                        next_three_left = detections_left[-3:]

                        Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                         rec_img_left)
                        Pointer_traker.find_depth()
                        pointer_tip_gcs = Pointer_traker.getStylusPoint()
                        pointer_data.append(pointer_tip_gcs)
                        if len(pointer_data) < 20:
                            continue
                        pointer_tip_gcs = utils.filter_point_registration_data(pointer_data)
                        femure = TriangleTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right,
                                                 rec_img_left)
                        femure.find_depth()
                        F_points = femure.getLEDcordinates()

                        if F_points[1][1] >= F_points[0][1]:
                            hip_plane = [F_points[2], F_points[0], F_points[1]]
                        else:
                            hip_plane = [F_points[2], F_points[1], F_points[0]]

                        mid_femure = np.abs((F_points[1] + F_points[0]) / 2)

                        # print(f' F_points {F_points}  mid_femure {mid_femure}')

                        scale = 107.5 / utils.distance_3d(mid_femure, F_points[2])

                        SU = utils.find_new_point_location(hip_dict['SU']['Plane'], hip_plane, hip_dict['SU']['C'])
                        CA = utils.find_new_point_location(hip_dict['CA']['Plane'], hip_plane, hip_dict['CA']['C'])
                        AT = utils.find_new_point_location(hip_dict['AT']['Plane'], hip_plane, hip_dict['AT']['C'])
                        PT = utils.find_new_point_location(hip_dict['PT']['Plane'], hip_plane, hip_dict['PT']['C'])

                        A, B, C = Pointer_traker.getLEDcordinates()
                        Mid_point = np.abs((AT + PT) / 2)
                        mid = (B + A) / 2

                        disance_At_PT = utils.distance_3d(SU, CA)
                        # # accetabulumn_diameterinMM= disance_At_PT * scale1
                        accetabulumn_diameterinMM = disance_At_PT * scale

                        print(f'Mid_point{Mid_point} tip {pointer_tip_gcs} ')
                        v_AT_to_mid_points = AT - Mid_point

                        v_mid_points_Tracker = A - pointer_tip_gcs
                        dot_product = np.dot(v_AT_to_mid_points, v_mid_points_Tracker)
                        magnitude_v_AT_to_mid_points = np.linalg.norm(v_AT_to_mid_points)
                        magnitude_v_mid_points_Tracker = np.linalg.norm(v_mid_points_Tracker)
                        # Calculate the cosine of the angle
                        cos_theta = dot_product / (magnitude_v_AT_to_mid_points * magnitude_v_mid_points_Tracker)
                        # Calculate the angle in radians
                        angle_radians = np.arccos(
                            np.clip(cos_theta, -1.0, 1.0))  # Use np.clip to handle numerical errors
                        # Convert angle to degrees
                        angle_degrees = np.degrees(angle_radians)
                        # print(
                        #     f'angle_degrees {angle_degrees} accetabulumn_diameterinMM {accetabulumn_diameterinMM}')
                        data = [round(angle_degrees, 3), round(angle_degrees, 4), round(accetabulumn_diameterinMM, 3)]
                        utils.SendDataToFrontEnd(data)
                        time.sleep(1)
                    else:
                        print("no data[0x%x]" % ret)
                except Exception as e:
                    print(f'Error {e}')
        self.dual_cam.stop_grabbing()
        return 0


#
if __name__ == "__main__":
    processName = sys.argv[1] if len(sys.argv) > 1 else None  # Capture g_bExit from command-line args
    if processName == "system-setup":
        PrimaryHipMarking = HipMarking()
        PrimaryHipMarking.hip_system_setup()
    if processName == "hip_tp_register":
        PrimaryHipMarking = HipMarking()
        PrimaryHipMarking.hip_TP_variable_MarkingPipeline()
    if processName == "TP2_marking":
        PrimaryHipMarking = HipMarking()
        PrimaryHipMarking.hip_TP2_variable_MarkingPipeline()
    if processName == "hip_ThreePoint_register":
        PrimaryHipMarking = HipMarking()
        PrimaryHipMarking.hip_ASIS_three_points_MarkingPipeline()
    if processName == "mid-axial-body-plane":
        PrimaryHipMarking = HipMarking()
        PrimaryHipMarking.mid_axial_body_plane()

    if processName == "hip_FivePoint_register":
        PrimaryHipMarking = HipMarking()
        PrimaryHipMarking.hip_FivePoint_MarkingPipeline()
        PrimaryHipMarking.hipCollectRingPointCloudMarkingPipeline()
        PrimaryHipMarking.collect_pointCloud()
        # PrimaryHipMarking = HipMarking()
        # thread_collect = threading.Thread(target=PrimaryHipMarking.hipCollectRingPointCloudMarkingPipeline)
        # thread_collect.start()
        # PrimaryHipMarking.hipCollectRingPointCloudplotter()
        # thread_collect.join()
        # thread_collect = threading.Thread(target=PrimaryHipMarking.collect_pointCloud)
        # thread_collect.start()
        # PrimaryHipMarking.plot_pointCloud_with_background()
        # utils.writeRegisteredPoint('STEP6')
        # thread_collect.join()
    if processName == "hip_FivePoint_register_2":
        PrimaryHipMarking = HipMarking()
        PrimaryHipMarking.hip_FivePoint_register_2()
    if processName == "handle-position":
        PrimaryHipMarking = HipMarking()
        PrimaryHipMarking.handle_position()
    if processName == "final-cup-position":
        PrimaryHipMarking = HipMarking()
        PrimaryHipMarking.final_cup_position()

    if processName == "anterior-pelvic":
        PrimaryHipMarking = HipMarking()
        PrimaryHipMarking.anterior_pelvic()

    if processName == "hip_Revision_SixPoint_register":
        PrimaryHipMarking = HipMarking()
        PrimaryHipMarking.hip_Revision_SixPoint_register()
        PrimaryHipMarking = HipMarking()
        thread_collect = threading.Thread(target=PrimaryHipMarking.collect_pointCloud_revision)
        thread_collect.start()
        PrimaryHipMarking.plot_pointCloud_with_background()
        utils.writeRegisteredPoint('RESULT')
        thread_collect.join()

#
# PrimaryHipMarking = HipMarking()
# # PrimaryHipMarking.hip_TP_variable_MarkingPipeline()
# # PrimaryHipMarking = HipMarking()
# # PrimaryHipMarking.hip_FivePoint_MarkingPipeline()
# thread_collect = threading.Thread(target=PrimaryHipMarking.collect_pointCloud_revision)
# thread_collect.start()
# PrimaryHipMarking.plot_pointCloud_with_background()
# # utils.writeRegisteredPoint('STEP6')
# thread_collect.join()
# time.sleep(5)

# #
# thread_collect = threading.Thread(target=PrimaryHipMarking.hip_reming_process)
# thread_collect.start()
# PrimaryHipMarking.revisionKneepointcolor_update()
# thread_collect.join()


##################################### Revision Knee point deletion Cloud and model matching #########################

# PrimaryHipMarking = HipMarking()
# PrimaryHipMarking.hip_TP_variable_MarkingPipeline()
# PrimaryHipMarking = HipMarking()
# PrimaryHipMarking.hip_Revision_SixPoint_register()
# PrimaryHipMarking = HipMarking()
# thread_collect = threading.Thread(target=PrimaryHipMarking.collect_pointCloud_revision)
# thread_collect.start()
# PrimaryHipMarking.Revisionplot_pointCloud_with_background()
# thread_collect.join()
# time.sleep(5)


#
# PrimaryHipMarking = HipMarking()
# # thread_collect = threading.Thread(target=PrimaryHipMarking.hipCollectRingPointCloudMarkingPipeline)
# # thread_collect.start()
# # PrimaryHipMarking.hipCollectRingPointCloudplotter()
# # thread_collect.join()
# PrimaryHipMarking.hipCollectRingPointCloudplotter()

# PrimaryHipMarking = HipMarking()
# PrimaryHipMarking.hipCollectRingPointCloudMarkingPipeline()
# PrimaryHipMarking.collect_pointCloud()


#
# PrimaryHipMarking = HipMarking()
# PrimaryHipMarking.hip_Revision_SixPoint_register()
# PrimaryHipMarking = HipMarking()
# thread_collect = threading.Thread(target=PrimaryHipMarking.collect_pointCloud_revision)
# thread_collect.start()
# PrimaryHipMarking.plot_pointCloud_with_background()
# utils.writeRegisteredPoint('RESULT')
# thread_collect.join()

# utils.cleanup_old_data('')
# utils.SetLoopExitCondition('hip_tp_register')
# PrimaryHipMarking = HipMarking()
# PrimaryHipMarking.hip_TP_variable_MarkingPipeline()
# utils.cleanup_old_data('')
# utils.SetLoopExitCondition('hip_FivePoint_register')
# # PrimaryHipMarking = HipMarking()
# PrimaryHipMarking.hip_FivePoint_MarkingPipeline()
# PrimaryHipMarking.hipCollectRingPointCloudMarkingPipeline()
# PrimaryHipMarking.collect_pointCloud()
# utils.cleanup_old_data('')
# utils.SetLoopExitCondition('handle-position')
# PrimaryHipMarking = HipMarking()
# PrimaryHipMarking.handle_position()

#
# from xarm.wrapper import XArmAPI
#
# # Connect to xArm6
# arm = XArmAPI('*************')  # Replace with your xArm6 IP
#
# # Enable the robot and set motion mode
# arm.motion_enable(enable=True)
# arm.set_mode(0)  # Position control mode
# arm.set_state(0)  # Enable motion
#
# code, position = arm.get_position_aa()
#
# if code == 0:  # Successful retrieval
#     x, y, z, rx, ry, rz = position  # RX, RY, RZ are in radians
#     print(f"Current Position:\nX: {x} mm\nY: {y} mm\nZ: {z} mm")
#     print(f"RX: {rx} degrees\nRY: {ry} degrees\nRZ: {rz} degrees")
#     data = {"X": x, "Y": y, "Z": z, "RX": rx, "RY": ry, "RZ": rz}
#
#     #
#     # # Define the target position using Axis-Angle representation
#     # x = 345.710022   # X position in mm
#     # y = -219.959961  # Y position in mm
#     # z = 252.188187   # Z position in mm
#     #
#     # # Rotation in Axis-Angle (radians)
#     # rx = -36.483259
#     # ry = 126.909693
#     # rz = 25.217362
#
#     RX = -28.000677
#     RY = 120.746501
#     RZ = -2.793685
#
#     # Move the robot
#     axis_angle_pose = [x, y, z, RX, RY, RZ]  # List format
#
#     # Move the robot using the corrected function call
#     arm.set_position_aa(axis_angle_pose, speed=25, mvacc=500, wait=True)
#
#     arm.set_servo_detach(1)
#     arm.set_servo_detach(2)
#     arm.set_servo_detach(3)
#
#     # Print confirmation
#     print("Robot moved to the new position using Axis-Angle")
#
#     time.sleep(60)
#
#     arm.set_servo_attach(1)
#     arm.set_servo_attach(2)
#     arm.set_servo_attach(3)
#
#     arm.disconnect()



# utils.cleanup_old_data('')
# utils.SetLoopExitCondition('system-setup')
# PrimaryHipMarking = HipMarking()
# PrimaryHipMarking.hip_system_setup()