// static/app.js

let websocket = new WebSocket("ws://127.0.0.1:8000/ws");

websocket.onopen = function(event) {
    console.log("Connection opened.");
};

websocket.onmessage = function(event) {
    let messagesDiv = document.getElementById("messages");
    let message = document.createElement("p");
    message.textContent = event.data;
    messagesDiv.appendChild(message);
};

websocket.onclose = function(event) {
    console.log("Connection closed.");
};

function sendMessage() {
    websocket.send("Hello from the client!");
}
