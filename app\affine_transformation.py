import numpy as np
import cv2 as cv
import os

current_dir = os.path.dirname(os.path.abspath(__file__))

def load_data(file_path):
    led_points = []
    robot_points = []

    with open(file_path, "r") as file:
        for line in file:
            if "LED" in line and "robot" in line:
                led_part = line.split("LED [")[1].split("]")[0].split()
                robot_part = line.split("robot (")[1].split(")")[0].split(", ")

                # Convert to float
                led_x, led_y, led_z = map(float, led_part)
                robot_x, robot_y, robot_z = map(float, robot_part)

                led_points.append([led_x, led_y, led_z])
                robot_points.append([robot_x, robot_y, robot_z])

    return np.array(led_points), np.array(robot_points)
    
def getAffineTransform3D(src, dst):
    """
    Computes 3D affine transformation matrix H (4x4) such that: dst ≈ H @ src
    src and dst are Nx3 arrays with N >= 4
    """
    assert src.shape == dst.shape and src.shape[1] == 3
    N = src.shape[0]
    
    A = []
    b = []

    for i in range(N):
        x, y, z = src[i]
        x_, y_, z_ = dst[i]

        A.append([x, y, z, 0, 0, 0, 0, 0, 0, 1, 0, 0])
        A.append([0, 0, 0, x, y, z, 0, 0, 0, 0, 1, 0])
        A.append([0, 0, 0, 0, 0, 0, x, y, z, 0, 0, 1])
        b.extend([x_, y_, z_])
    
    A = np.array(A)
    b = np.array(b)
    x = np.linalg.pinv(A) @ b
    H = np.vstack([x.reshape(3, 4), [0, 0, 0, 1]])  # 4x4 matrix
    return H 

if __name__ == '__main__':
    robot_calib_data = os.path.join(
        current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
    )
    led_points, robot_points = load_data(file_path=robot_calib_data)
    
    print('led_points : \n', led_points, '\n')
    print('robot_points : \n', robot_points, '\n')
    led_points = led_points[:, [0, 2, 1]]
    # led_points[:, 0] *= -1

    print(led_points)
    H = getAffineTransform3D(led_points, robot_points)
    print('H : \n', H)

    led_points = np.hstack([led_points, np.ones((led_points.shape[0], 1))])
    print(led_points)
    transformed_points = (H @ led_points.T).T

    np.set_printoptions(suppress=True, precision=6)

    print('transformed_points : \n', transformed_points)