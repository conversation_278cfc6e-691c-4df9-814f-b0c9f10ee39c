<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Registration Form</title>
    <style>
        body {
            background-color: #000;
            font-family: Arial, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
        }

        .container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 75%;
        }

        .logo-container {
            flex: 1;
            text-align: center;
        }

        .logo-container img {
            max-width: 550px;
            height: auto;
        }

        .form-container {
            flex: 1;
            background-color: #1A1A1A;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0px 0px 20px rgba(255, 255, 255, 0.2);
            max-width: 450px;
        }
                /*
        .form-header {
            color: #e56e19;
            text-align: center;
            margin-bottom: 20px;
            font-size: 22px;

        }
        */
        .form-header {
            color: #e56e19;
            text-align: center;
            margin-bottom: 20px;
            font-size: 22px;
            font-family: "Arial", "Helvetica", "Roboto", sans-serif;
            font-weight: 600;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-shadow: 0px 0px 1px rgba(0, 0, 0, 0.2);
            letter-spacing: 0.5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            color: #fff;
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-group input,
        .form-group select {
            width: 100%; /* Ensures all fields have the same width */
            padding: 12px;
            border: none;
            border-radius: 6px;
            background-color: #333;
            color: #fff;
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
            box-sizing: border-box; /* Ensures uniform sizing */
        }

        .form-button {
            background-color: #e56e19;
            color: #fff;
            padding: 14px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            width: 100%;
            font-size: 20px;
            /*font-weight: bold;*/
        }

        .form-button:hover {
            background-color: #e69500;
        }

        .form-group input[type="date"] {
            position: relative;
            padding-right: 30px;  /* Space for calendar icon */
            cursor: pointer;
        }

        .form-group input[type="date"]::-webkit-calendar-picker-indicator {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            filter: invert(1);  /* Make calendar icon white */
            opacity: 0.8;
        }

        .form-group input[type="date"]::-webkit-calendar-picker-indicator:hover {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo-container">
            <img src="/static/images/artikon_logo.png" alt="Artikon Logo">
        </div>
        <div class="form-container">
            <div class="form-header">
                <h2>Patient Details</h2>
            </div>
            <form id="registration-form" onsubmit="return handleSubmit(event)">
                <div class="form-group">
                    <label for="name">Patient Name:</label>
                    <input type="text" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="age">Age:</label>
                    <input type="number" id="age" name="age" required>
                </div>
                <div class="form-group">
                    <label for="Side">Side:</label>
                    <select id="side" name="side" required>
                        <option value="">Select Side</option>
                        <option value="Left">Left</option>
                        <option value="Right">Right</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="gender">Gender:</label>
                    <select id="gender" name="gender" required>
                        <option value="">Select Gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="date">Date:</label>
                    <input type="date" id="date" name="date" required>
                </div>
                <div class="form-group">
                    <button type="submit" class="form-button">Submit</button>
                </div>
            </form>
        </div>
    </div>

<script>
    // Set today's date when page loads but allow changes
    window.addEventListener('DOMContentLoaded', function() {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const formattedDate = `${year}-${month}-${day}`;

        const dateInput = document.getElementById('date');
        dateInput.value = formattedDate;  // Set default value to today
        dateInput.max = formattedDate;    // Prevent future dates
    });

    function handleSubmit(event) {
        event.preventDefault();  // Prevents default form submission

        // Get form values
        const patientData = {
            name: document.getElementById('name').value,
            age: document.getElementById('age').value,
            side: document.getElementById('side').value,
            gender: document.getElementById('gender').value,
            date: document.getElementById('date').value
        };

        // Store data in localStorage
        localStorage.setItem('patientData', JSON.stringify(patientData));
        localStorage.setItem('patientName', patientData.name);

        // Redirect to the second page
        window.location.href = 'landing-page.html';
    }
</script>
</body>
</html>
