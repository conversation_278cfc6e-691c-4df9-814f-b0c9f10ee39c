<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>
<body>
    <div class="tbl" id="content">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Mid Axial Body Plane</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center align-items-start">
                                        <div class="col-6">
                                            <div class="grid NOTE0 d-none">
                                                <img src="../static/images/hip-bone/statement0.jpg" class="img-fluid" alt="">
                                            </div>
                                            <div class="grid NOTE1 d-none">
                                                <img src="../static/images/hip-bone/statement1.jpg" class="img-fluid" alt="">
                                            </div>
                                            <div class="grid NOTE2 d-none">
                                                <img src="../static/images/hip-bone/statement2.jpg" class="img-fluid" alt="">
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="note new-note text-start">
                                                <p class="note_txt">Notes:</p>
                                                <ol>
                                                    <li class="NOTE1 mb-3">Place the tracker on the patient’s mid axis (chest or acromion), press the Select button to record</li>
                                                    <li class="NOTE2">Place the tracker on the patient’s greater trochanter , press the Select button to record.</li>
                                                </ol>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="btn green_border pointer_bg">
                                        <span class="mr-20"><img src="../static/images/icon/check.png" /></span>Pointer
                                    </div>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a id="backBtn" href="#" onclick="handleBackClick()"><span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back</a>
                                    </div>
									<script>
									function handleBackClick() {
										const selectedValue = localStorage.getItem('selectedPlane');
										console.log(selectedValue); // Debugging line

										let redirectUrl = '';
										if (selectedValue === 'coupon_question1') {
											redirectUrl = `pelvis-registration.html?selectedPlane=${encodeURIComponent(selectedValue)}`
										} else if (selectedValue === 'coupon_question2') {
											redirectUrl = `pelvis-registration.html?selectedPlane=${encodeURIComponent(selectedValue)}`
										} else if (selectedValue === 'coupon_question3') {
											redirectUrl = `pelvis-registration-2.html?selectedPlane=${encodeURIComponent(selectedValue)}`; // Adjust as needed
										}

										if (redirectUrl) {
											window.location.href = redirectUrl; // Perform the redirect
										} else {
											alert('Please make a selection before proceeding.'); // Alert if no selection found
										}
									};
									</script>
                                   <div class="btn">
										<a id="nextBtn" href="#" onclick="handleNextClick()"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
									</div>
									
									<script>
									function handleNextClick() {
									redirectToNextPage();
									};
									</script>
									
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                               <a id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
                            </li>
                           <li class="footer_btn_three">
								<a id="powerbuttonLink">
									<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
								</a>
								<script>
									document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
										event.preventDefault();  // Prevent default anchor behavior
										window.location.href = '/';  // Redirect to root path
									});
								</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
	
	<script>
        // Function to get query parameters
        function getQueryParams() {
            const params = {};
            const queryString = window.location.search.substring(1);
            const pairs = queryString.split('&');

            pairs.forEach(pair => {
                const [key, value] = pair.split('=');
                params[key] = decodeURIComponent(value); // Decode the value
            });

            return params;
        }

        // Load the selected option when the page loads
        window.onload = function() {
            const queryParams = getQueryParams();
            const selectedValue = queryParams.selected;

            const contentDiv = document.getElementById('content');
            if (selectedValue) {
                if (selectedValue === 'coupon_question1') {
                    contentDiv.innerHTML = "<p>You selected: Anatomical plane</p>";
                    // Additional functionality for Anatomical plane
                } else if (selectedValue === 'coupon_question2') {
                    contentDiv.innerHTML = "<p>You selected: Functional plane</p>";
                    // Additional functionality for Functional plane
                } else if (selectedValue === 'coupon_question3') {
                    contentDiv.innerHTML = "<p>You selected: Both (anatomical+functional)</p>";
                    // Additional functionality for Both
                }
            } else {
                contentDiv.innerHTML = "<p>No selection was made.</p>";
            }
        };
    </script>
	
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>

<script type="text/javascript">
    function redirectToNextPage() {
        const selectedValue = localStorage.getItem('selectedPlane');
        let redirectUrl = '';

        if (selectedValue === 'coupon_question2') {
            redirectUrl = `register-acetabulum.html?selectedPlane=${encodeURIComponent(selectedValue)}`;
        } else if (selectedValue === 'coupon_question3') {
            redirectUrl = `anterior-pelvic.html?selectedPlane=${encodeURIComponent(selectedValue)}`;
        }

        if (redirectUrl) {
            window.location.href = redirectUrl; // Perform the redirect
        } else {
            alert('Please make a selection before proceeding.');
        }
    }

    let socket;
    let reconnecting = false; // Flag to track if we're currently trying to reconnect

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            socket = new WebSocket('ws://127.0.0.1:8000/ws'); // Update this URL for production

            socket.onopen = function() {
                console.log('✅ WebSocket opened for ' + index);
                socket.send(JSON.stringify({ file: window.location.pathname.split('/').pop() }));
            };
            socket.onmessage = function (event) {
                
                const values = event.data.split(',');
                const abbreviation = values[0];
				console.log('Received message:', event.data);
				console.log('abbreviation :', abbreviation);
                // Show the corresponding grid element and highlight the relevant list item
                $('.grid').addClass('d-none');
                $('.' + abbreviation).removeClass('d-none');
                $('li.' + abbreviation).addClass('turquoise-blue');

                // If 'exit' is received, perform the redirect immediately
                if (abbreviation === 'NOTE2') {
                    setTimeout(() => {
					redirectToNextPage();
				}, 100); // 2000 milliseconds = 2 seconds
                }
            };

            socket.onerror = function (error) {
                console.error('Socket error:', error);
                // Do not attempt reconnection immediately, let the onclose event handle it
            };

            socket.onclose = function (event) {
                console.log('Socket closed:', event);
                if (!reconnecting) {
                    reconnecting = true;
                    handleReconnect(); // Try reconnecting when the socket closes
                }
            };
        }
    }

    // Attempt to reopen WebSocket in a loop if it is closed
    function handleReconnect() {
        console.log('Reopening WebSocket...');
        // Delay the reconnection attempt
        setTimeout(function () {
            startServer('hip_mid_axial_body_plane'); // Reopen the connection
        }, 100); // Attempt reconnect after 1 second
    }
	
	
	function handleNavigation(event) {
		event.preventDefault();
		const targetUrl = event.currentTarget.href;

		if (socket && socket.readyState === WebSocket.OPEN) {
			socket.send(JSON.stringify({ file: " " }));
			socket.addEventListener('close', function () {
				window.location.href = targetUrl;
			}, { once: true });

			socket.close();
		} else {
			window.location.href = targetUrl;
		}
	};
	document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
    document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);
	
	// Camera functionality
    document.addEventListener('DOMContentLoaded', function() {
        const cameraButton = document.querySelector('.footer_btn_one .btn.first');
        const cameraModal = document.getElementById('cameraModal');
        const closeCamera = document.querySelector('.close-camera');
        const videoStream = document.getElementById('videoStream');
        let mediaStream = null;

        // Function to start the camera
        async function startCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });
                mediaStream = stream;
                videoStream.srcObject = stream;
                videoStream.play();
            } catch (err) {
                console.error('Error accessing camera:', err);
            }
        }

        // Function to stop the camera
        function stopCamera() {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                videoStream.srcObject = null;
                mediaStream = null;
            }
        }

        // Open camera modal
        cameraButton.addEventListener('click', function(e) {
            e.preventDefault();
            cameraModal.style.display = 'block';
            startCamera();
        });

        // Close camera modal
        closeCamera.addEventListener('click', function() {
            cameraModal.style.display = 'none';
            stopCamera();
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === cameraModal) {
                cameraModal.style.display = 'none';
                stopCamera();
            }
        });
    });

    // Start the server automatically on page load
    window.onload = function () {
        startServer('hip_mid_axial_body_plane');
    };
</script>


</html>