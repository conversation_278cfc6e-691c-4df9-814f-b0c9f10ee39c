<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>3D Tibia Model Viewer - WebSocket Marker Trail</title>


    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>

    <style>
        body { margin: 0; overflow: hidden; }
        #canvas-container {
            background-color: black;
            width: 100vw;
            height: 100vh;
            display: block;
        }
    </style>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.128/examples/js/loaders/OBJLoader.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.128/examples/js/controls/OrbitControls.js"></script>
</head>
<body>
  <div id="canvas-container"></div>
  <div class="head_wrap">
    <div class="header">
        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
         <div class="second_head head_bg">
            <div class="inner_head one">
                <span><img src="../static/images/calendar.png" /></span><span id="date">24/11/2022</span>
            </div>
            <div class="inner_head">
                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
            </div>
        </div>
        <div class="third_head head_bg">
            <span><img src="../static/images/settings.png" /></span>Settings
        </div>
    </div>
    </div>

  <script>
  let scene, camera, renderer, controls, modelGroup, modelCenter;
  let dynamicMarker = null;
  let socket;
  let modelVertices = []; // ⭐ Store all model vertices

  function init() {
    scene = new THREE.Scene();
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(-97, 0, -15);
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    document.getElementById("canvas-container").appendChild(renderer.domElement);

    controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);
    const dirLight = new THREE.DirectionalLight(0xffffff, 0.7);
    dirLight.position.set(5, 5, 5);
    scene.add(dirLight);

    // Load OBJ model
    const loader = new THREE.OBJLoader();
    loader.load('/static/images/hip-bone/3D/TIBIA_FULL.obj', (obj) => {
      const box = new THREE.Box3().setFromObject(obj);
      modelCenter = box.getCenter(new THREE.Vector3());

      modelGroup = new THREE.Group();
      scene.add(modelGroup);

      obj.position.sub(modelCenter);
      obj.traverse(child => {
        if (child.isMesh) {
          // ⭐ Extract vertices
          const position = child.geometry.attributes.position;
          for (let i = 0; i < position.count; i++) {
            const vertex = new THREE.Vector3().fromBufferAttribute(position, i);
            modelVertices.push(vertex.clone().sub(modelCenter)); // center-adjusted
          }

          child.material = new THREE.MeshStandardMaterial({ color: 0xf5deb3, side: THREE.DoubleSide });
        }
      });

      modelGroup.add(obj);
      addHardcodedPoints();
      setupWebSocket();
    });

    window.addEventListener("resize", onWindowResize);
    animate();
  }

  function addHardcodedPoints() {
    const objPoints = [
      /*[-64.6756134, -2.9603553, -17.7387486],
      [-64.6940308, 0.605574, -17.0766163],
      [-65.52034, -5.8293805, -17.9646893],
      [-64.2572708, -4.0632067, -14.2518415],
      [-63.9681778, -5.3495793, -12.3823586],
      [-0.7417173, -8.5226688, -13.8344755],
      [-0.2158346, -9.4181175, -22.6858425],*/
    ];

    objPoints.forEach((point) => {
      const sphere = new THREE.Mesh(
        new THREE.SphereGeometry(0.2, 8, 8),
        new THREE.MeshBasicMaterial({ color: 0xff0000 })
      );
      sphere.position.set(
        point[0] - modelCenter.x,
        point[1] - modelCenter.y,
        point[2] - modelCenter.z
      );
      modelGroup.add(sphere);
    });
  }

  function setupWebSocket() {
    socket = new WebSocket("ws://localhost:8000/ws");

    socket.addEventListener("open", () => {
      const currentUrl = window.location.href;
      let pageName = 'rev' + currentUrl.split('/').pop().split('?')[0];
      socket.send(JSON.stringify({ file: pageName }));
    });

    socket.addEventListener("message", (event) => {
      handleSocketMessage(event.data);
    });

    socket.addEventListener("close", () => {
      console.log("WebSocket closed. Reconnecting in 3s...");
      setTimeout(setupWebSocket, 3000);
    });

    socket.addEventListener("error", (err) => {
      console.error("WebSocket error:", err);
      socket.close();
    });
  }

  function handleSocketMessage(data) {
    try {
      data = data.trim();
      if (!data || data.toLowerCase() === "null") {
        removeDynamicMarker();
        return;
      }

      if (data.toLowerCase().startsWith("exit")) {
        window.location.href = "Aori_type.html";
        return;
      }

      let point = null;
      if (/^-?\d+(\.\d+)?,-?\d+(\.\d+)?,-?\d+(\.\d+)?$/.test(data)) {
        point = data.split(",").map(Number);
      } else {
        try {
          const obj = JSON.parse(data);
          if (typeof obj === "object" && "x" in obj && "y" in obj && "z" in obj) {
            point = [obj.x, obj.y, obj.z];
          }
        } catch (err) {
          console.warn("Invalid JSON format.");
          removeDynamicMarker();
          return;
        }
      }

      if (point && point.length === 3) {
        // ⭐ Compute closest vertex and use it instead
        const nearest = findNearestVertex(new THREE.Vector3(
          point[0] - modelCenter.x,
          point[1] - modelCenter.y,
          point[2] - modelCenter.z
        ));

        if (nearest) {
          createOrUpdateDynamicMarker(nearest);
          createPersistentPoint(nearest);
        }

      } else {
        removeDynamicMarker();
      }

    } catch (error) {
      console.error("Error processing WebSocket message:", error);
      removeDynamicMarker();
    }
  }

  // ⭐ Finds nearest vertex
  function findNearestVertex(targetVec3) {
    if (!modelVertices.length) return null;

    let minDist = Infinity;
    let nearest = null;

    for (let i = 0; i < modelVertices.length; i++) {
      const dist = targetVec3.distanceToSquared(modelVertices[i]);
      if (dist < minDist) {
        minDist = dist;
        nearest = modelVertices[i];
      }
    }

    return nearest;
  }

  function createOrUpdateDynamicMarker(vec3) {
    if (!dynamicMarker) {
      const geometry = new THREE.SphereGeometry(0.2, 8, 8);
      const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
      dynamicMarker = new THREE.Mesh(geometry, material);
      modelGroup.add(dynamicMarker);
    }

    dynamicMarker.position.copy(vec3);
    dynamicMarker.visible = true;
  }

  function removeDynamicMarker() {
    if (dynamicMarker) {
      dynamicMarker.visible = false;
    }
  }

  function createPersistentPoint(vec3) {
    const geometry = new THREE.SphereGeometry(0.2, 12, 12);
    const material = new THREE.MeshBasicMaterial({ color: 0xffff00 }); // Yellow trail
    const trailPoint = new THREE.Mesh(geometry, material);
    trailPoint.position.copy(vec3);
    modelGroup.add(trailPoint);
  }

  function animate() {
    requestAnimationFrame(animate);
    controls.update();
    renderer.render(scene, camera);
  }

  function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
  }

  init();
</script>


  <div class="bottom_btn">
        <div class="blank"></div>
        <div class="btn">
            <a id="backBtn" href="Free_point_collection_femur.html">
                <span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back
            </a>
        </div>
        <div class="btn">
            <a id="nextBtn" href="Aori_type.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
        </div>
    </div>







    <div class="footer_wrap">
        <div class="footer">
            <ul>
                <li class="copy_right">
                    © <span id="year">2023</span> Artikon AGS
                    <span class="top_txt">Auto Guided Surgery</span>
                </li>
                <li class="footer_btn_one">
                    <a href="#">
                        <div class="btn-group" role="group" aria-label="Basic example">
                            <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                            <button type="button" class="btn second">F</button>
                            <button type="button" class="btn third">T</button>
                        </div>
                    </a>
                </li>
                <li class="footer_btn_two">
                    <a href="#">
                        <span><img src="../static/images/home.png" /></span>Main Menu
                    </a>
                </li>
                <li class="footer_btn_three">
                    <a href="#">
                        <span><img src="../static/images/union.png" /></span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>
</html>
