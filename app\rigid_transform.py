import os
import numpy as np

current_dir = os.path.dirname(os.path.abspath(__file__))

class SrcDstSizeMismatchError(Exception):
    pass


class InvalidPointDimError(Exception):
    pass


class NotEnoughPointsError(Exception):
    pass


class RankDeficiencyError(Exception):
    pass

def check_handedness_reflection_scale(led_points, robot_points):
    """
    Given paired points from LED and robot coordinate systems, this function:
    1. Computes the similarity transformation (scale, rotation, translation) using SVD.
    2. Checks if the rotation matrix has reflection (determinant -1).
    3. Checks handedness using cross and dot product test.

    Args:
        led_points (np.array): Nx3 array of points in LED coordinate system
        robot_points (np.array): Nx3 array of points in Robot coordinate system

    Returns:
        dict: with keys 'scale', 'rotation_matrix', 'translation_vector',
              'determinant', 'handedness', 'reflection'
    """
    assert led_points.shape == robot_points.shape, "Point sets must have the same shape"
    assert led_points.shape[1] == 3, "Points must be 3D"

    # Compute centroids
    centroid_led = np.mean(led_points, axis=0)
    centroid_robot = np.mean(robot_points, axis=0)

    # Center the points
    led_centered = led_points - centroid_led
    robot_centered = robot_points - centroid_robot

    # Variance of LED points
    var_led = np.sum(led_centered ** 2)

    # Compute covariance matrix
    H = np.dot(led_centered.T, robot_centered)

    # SVD
    U, S, Vt = np.linalg.svd(H)

    # Compute rotation
    R = np.dot(Vt.T, U.T)

    # Check for reflection
    det = np.linalg.det(R)
    reflection = False
    if det < 0:
        reflection = True
        Vt[-1, :] *= -1
        R = np.dot(Vt.T, U.T)

    # Compute scale
    scale = np.sum(S) / var_led

    # Compute translation
    t = centroid_robot - scale * np.dot(R, centroid_led)

    # Handedness check using cross and dot product
    v1 = R[:, 0]
    v2 = R[:, 1]
    v3 = R[:, 2]
    handedness_value = np.dot(np.cross(v1, v2), v3)
    handedness = 'right-handed' if handedness_value > 0 else 'left-handed'

    return {
        'scale': scale,
        'rotation_matrix': R,
        'translation_vector': t,
        'determinant': np.linalg.det(R),
        'handedness': handedness,
        'reflection': reflection
    }

def load_data(file_path):
    led_points = []
    robot_points = []

    with open(file_path, "r") as file:
        for line in file:
            if "LED" in line and "robot" in line:
                led_part = line.split("LED [")[1].split("]")[0].split()
                robot_part = line.split("robot (")[1].split(")")[0].split(", ")

                # Convert to float
                led_x, led_y, led_z = map(float, led_part)
                robot_x, robot_y, robot_z = map(float, robot_part)

                led_points.append([led_x, led_y, led_z])
                robot_points.append([robot_x, robot_y, robot_z])

    return np.array(led_points), np.array(robot_points)

def load_translated_data(file_path):
    led_points = []

    with open(file_path, "r") as file:
        for line in file:
            if "LED" in line:
                led_part = line.split("LED [")[1].split("]")[0].split()

                # Convert to float
                led_x, led_y, led_z = map(float, led_part)

                led_points.append([led_x, led_y, led_z])

    return np.array(led_points)

def rigid_transform(src_pts, dst_pts, calc_scale=False):
    """Calculates the optimal rigid transform from src_pts to dst_pts.

    The returned transform minimizes the following least-squares problem
        r = dst_pts - (R @ src_pts + t)
        s = sum(r**2))

    If calc_scale is True, the similarity transform is solved, with the residual being
        r = dst_pts - (scale * R @ src_pts + t)
    where scale is a scalar.

    Parameters
    ----------
    src_pts: matrix of points stored as rows (e.g. Nx3)
    dst_pts: matrix of points stored as rows (e.g. Nx3)
    calc_scale: if True solve for scale

    Returns
    -------
    R: rotation matrix
    t: translation column vector
    scale: scalar, scale=1.0 if calc_scale=False
    """

    dim = src_pts.shape[1]

    if src_pts.shape != dst_pts.shape:
        raise SrcDstSizeMismatchError(
            f"src and dst points aren't the same matrix size {src_pts.shape=} != {dst_pts.shape=}"
        )

    if not (dim == 2 or dim == 3):
        raise InvalidPointDimError(f"Points must be 2D or 3D, src_pts.shape[1] = {dim}")

    if src_pts.shape[0] < dim:
        raise NotEnoughPointsError(f"Not enough points, expect >= {dim} points")

    # find mean/centroid
    centroid_src = np.mean(src_pts, axis=0)
    centroid_dst = np.mean(dst_pts, axis=0)

    centroid_src = centroid_src.reshape(-1, dim)
    centroid_dst = centroid_dst.reshape(-1, dim)

    # subtract mean
    # NOTE: doing src_pts -= centroid_src will modifiy input!
    src_pts = src_pts - centroid_src
    dst_pts = dst_pts - centroid_dst

    # the cross-covariance matrix minus the mean calculation for each element
    # https://en.wikipedia.org/wiki/Cross-covariance_matrix
    H = src_pts.T @ dst_pts

    rank = np.linalg.matrix_rank(H)

    if dim == 2 and rank == 0:
        raise RankDeficiencyError(
            f"Insufficent matrix rank. For 2D points expect rank >= 1 but got {rank}. Maybe your points are all the same?"
        )
    elif dim == 3 and rank <= 1:
        raise RankDeficiencyError(
            f"Insufficent matrix rank. For 3D points expect rank >= 2 but got {rank}. Maybe your points are collinear?"
        )

    # find rotation
    U, _, Vt = np.linalg.svd(H)
    R = Vt.T @ U.T

    # special reflection case
    # https://en.wikipedia.org/wiki/Kabsch_algorithm
    det = np.linalg.det(R)
    if det < 0:
        print(f"det(R) = {det}, reflection detected!, correcting for it ...")
        S = np.eye(dim)
        S[-1, -1] = -1
        R = Vt.T @ S @ U.T

    if calc_scale:
        scale = np.sqrt(np.mean(dst_pts**2) / np.mean(src_pts**2))
    else:
        scale = 1.0

    t = -scale * R @ centroid_src.T + centroid_dst.T

    return R, t, scale

def apply_similarity_transform(points_src, R, t, scale):
    """
    Apply similarity transformation to a set of 3D points.

    Args:
        points_src (ndarray): Nx3 array of source points.
        R (ndarray): 3x3 rotation matrix.
        t (ndarray): 3x1 or 1x3 translation vector.
        s (float): scaling factor.

    Returns:
        ndarray: Nx3 array of transformed points.
    """
    points_src = np.asarray(points_src)
    t = t.reshape(1, 3)  # Ensure proper shape
    points_dst = scale * (points_src @ R.T) + t  # Apply R, scale, then translate
    return points_dst

# driver code
robot_calib_data = os.path.join(
    current_dir, "..", "..", "robotic_calib_data", "robotic_init_data.txt"
)
led_points, robot_points = load_data(file_path=robot_calib_data)

result = check_handedness_reflection_scale(led_points, robot_points)
# print('scale: ',result['scale'])
# print('rotation matrix: ',result['rotation_matrix'])
# print('translation vector: ',result['translation_vector'])
# print('determinant: ',result['determinant'])
# print('handedness: ',result['handedness'])
# print('reflection: ',result['reflection'])
# led_points = np.append(led_points, [519.25012223, 477.1978664,  333.06544753])
# robot_points = np.append(robot_points, [0, 0, 0])
translated_led_points_path = os.path.join(
    current_dir, "..", "translated_point_cloud_4_7_2025-1000.txt"
)

# led_points = load_translated_data(file_path=translated_led_points_path)

print('led_points : \n', led_points, '\n')
print('robot_points : \n', robot_points, '\n')
# print('translated_led_points : \n', led_points, '\n')

# R, t, scale = rigid_transform(translated_led_points, robot_points, calc_scale=True)

# predicted_points = apply_similarity_transform(translated_led_points, R, t, scale)
# print('predicted_points : \n', predicted_points)


led_points_1 = led_points[:, [0, 2, 1]]
led_points_1[:, 1] *= -1
print('led_points_1 : \n', led_points_1, '\n')

R, t, scale = rigid_transform(led_points_1, robot_points, calc_scale=True)

predicted_points_1 = apply_similarity_transform(led_points_1, R, t, scale)
print('predicted_points_1 : \n', predicted_points_1)

with open('predicted_points.txt', 'w') as file:
        for point in robot_points:
            file.write(
                f'{point}\n')
            
        for point in predicted_points_1:
            file.write(
                f'{point}\n')
# #### CASE 1
# print('============== CASE 1: (-Z, X, Y) ===================')
# led_points_1 = led_points[:, [2, 0, 1]]
# led_points_1[:, 0] *= -1
# print('led_points_1 : \n', led_points_1, '\n')

# R, t, scale = rigid_transform(led_points_1, robot_points, calc_scale=True)

# predicted_points_1 = apply_similarity_transform(led_points_1, R, t, scale)
# print('predicted_points_1 : \n', predicted_points_1)
# print('====================================================')

# #### CASE 2
# print('============== CASE 2: (X, Z, Y) ===================')
# led_points_2 = led_points[:, [0, 2, 1]]
# print('led_points_2 : \n', led_points_2, '\n')

# R, t, scale = rigid_transform(led_points_2, robot_points, calc_scale=True)

# predicted_points_2 = apply_similarity_transform(led_points_2, R, t, scale)
# print('predicted_points_2 : \n', predicted_points_2)
# print('====================================================')

# #### CASE 3
# print('============== CASE 3: (-Z, -X, Y) ===================')
# led_points_3 = led_points[:, [2, 0, 1]]
# led_points_3[:, 0:2] *= -1
# print('led_points_3 : \n', led_points_3, '\n')

# R, t, scale = rigid_transform(led_points_3, robot_points, calc_scale=True)

# predicted_points_3 = apply_similarity_transform(led_points_3, R, t, scale)
# print('predicted_points_3 : \n', predicted_points_3)
# print('====================================================')


