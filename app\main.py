import asyncio
import json
import os
import subprocess
import webbrowser

from PyQt6.QtWebEngineCore import QWebEngineSettings
from fastapi import FastAPI, Request, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates

import sys
from PyQt6.QtCore import QUrl, Qt
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton
from PyQt6.QtWebEngineWidgets import QWebEngineView

from starlette.responses import JSONResponse

global g_bExit
from threading import Timer
# from pythonProject.kneeSequence.marking.primary import knee_marking
from pythonProject.general.common.utils import cleanup_old_data, SetLoopExitCondition, getRegisteredPoint, \
    getanglefromBackend, checkLoopExitCondition, GetDataFromBackEnd, SetplanningInputs, writeRegisteredPoint, \
    SendDataToFrontEnd

from pydantic import BaseModel

current_dir = os.path.dirname(__file__)
# venv_python = os.path.join(current_dir, '../venv/Scripts/python.exe')
venv_python = os.path.abspath(os.path.join(current_dir, '../venv/Scripts/python.exe'))
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust this to your needs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# Use an absolute path for mounting the static files directory
app.mount("/static", StaticFiles(directory=os.path.join(os.path.dirname(__file__), "static")), name="static")

templates = Jinja2Templates(directory=os.path.join(os.path.dirname(__file__), "templates"))
import matplotlib

matplotlib.use('Agg')  # Use a non-interactive backend
from pathlib import Path

# Define the paths relative to main.py
BASE_DIR = Path(__file__).resolve().parent
processes = {}


@app.get("/")
async def read_index(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('')
    return templates.TemplateResponse("index.html", {"request": request})


@app.get("/landing-pagebackup.html")
async def read_landing_page(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('home')
    return templates.TemplateResponse("landing-pagebackup.html", {"request": request})


# Define the model for request data
class ValueModel(BaseModel):
    value: str


@app.post("/send-data/")
async def receive_data(request: Request):
    data = await request.json()
    input_id = data.get('inputId')
    input_value = data.get('inputValue')
    SetplanningInputs(filename=str(input_id), data=str(input_value))
    print(f"Received ID: {input_id}, Value: {input_value}")
    return {"message": f"Received ID: {input_id}, Value: {input_value}"}


# Define a data model for the request
class CheckboxState(BaseModel):
    id: str  # Checkbox ID
    state: bool  # True if checked, False if unchecked


@app.post("/update_checkbox")
async def update_checkbox_state(data: CheckboxState):
    # Process the received data
    # You can store the state in a database or perform other actions
    print(f"Checkbox ID: {data.id}, State: {data.state}")
    SetplanningInputs(filename=str(data.id), data=str(data.state))
    return {"message": "Checkbox state updated successfully"}


##########################################################################################################
#                         Knee Procedures
##########################################################################################################


@app.get("/knee/femur-anterior-chamfer-resection.html")
async def femur_anterior_chamfer_resection(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('anteriorchamferfemurecut')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'anteriorchamferfemurecut'])
    return templates.TemplateResponse("/knee/femur-anterior-chamfer-resection.html", {"request": request})


@app.get("/knee/femur-posterior-resection.html")
async def femur_posterior_resection(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('posteriorfemurecut')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'posteriorfemurecut'])
    return templates.TemplateResponse("/knee/femur-posterior-resection.html", {"request": request})


@app.get("/knee/femur-posterior-chamfer-resection.html")
async def femur_posterior_chamfer_resection(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('posteriorChamferfemurecut')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'posteriorChamferfemurecut'])
    return templates.TemplateResponse("/knee/femur-posterior-chamfer-resection.html", {"request": request})


@app.get("/knee/femur-anterior-resection.html")
async def femur_anterior_resection(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('Anteriorfemurecuts')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'Anteriorfemurecuts'])
    return templates.TemplateResponse("/knee/femur-anterior-resection.html", {"request": request})


@app.get("/knee/femur-distal-femur-cut.html")
async def femur_distal_femur_cut(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('femur_distal_femur_cut')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'femur_distal_femur_cut'])
    return templates.TemplateResponse("/knee/femur-distal-femur-cut.html", {"request": request})


@app.get("/knee/femur-graph-screen.html")
async def femur_graph_screen(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('alignmentangels')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'alignmentangels'])
    return templates.TemplateResponse("/knee/femur-graph-screen.html", {"request": request})


@app.get("/knee/femur-inner-page2.html")
async def femur_inner_page2(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('Tibia_cut')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'Tibia_cut'])
    return templates.TemplateResponse("/knee/femur-inner-page2.html", {"request": request})


@app.get("/knee/femur-inner-page3.html")
async def femur_inner_page3(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('femur-inner-page3')
    return templates.TemplateResponse("/knee/femur-inner-page3.html", {"request": request})


@app.get("/knee/femur-inner-page4.html")
async def femur_inner_page4(request: Request):
    # cleanup_old_data()
    # SetLoopExitCondition('femur-inner-page4')
    return templates.TemplateResponse("/knee/femur-inner-page4.html", {"request": request})


@app.get("/knee/femur-inner-page5.html")
async def femur_inner_page5(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('femur-inner-page5')
    return templates.TemplateResponse("/knee/femur-inner-page5.html", {"request": request})


# Routes for tibia pages
@app.get("/knee/tibia-anterior-chamfer-resection.html")
async def tibia_anterior_chamfer_resection(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('tibia-anterior-chamfer-resection')
    return templates.TemplateResponse("/knee/tibia-anterior-chamfer-resection.html", {"request": request})


@app.get("/knee/tibia-anterior-resection.html")
async def tibia_anterior_resection(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('tibia-anterior-resection')
    return templates.TemplateResponse("/knee/tibia-anterior-resection.html", {"request": request})


@app.get("/knee/tibia-distal-femur-cut.html")
async def tibia_distal_femur_cut(request: Request):
    return templates.TemplateResponse("/knee/tibia-distal-femur-cut.html", {"request": request})


@app.get("/knee/tibia-graph-screen.html")
async def tibia_graph_screen(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('AlignmentAngels')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'AlignmentAngels'])
    return templates.TemplateResponse("/knee/tibia-graph-screen.html", {"request": request})


@app.get("/knee/tibia-graph-screen-1.html")
async def tibia_graph_screen_1(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('tibia-graph-screen')
    return templates.TemplateResponse("/knee/tibia-graph-screen-1.html", {"request": request})


@app.get("/knee/tibia-inner-page2.html")
async def tibia_inner_page2(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('tibia-inner-page2')
    return templates.TemplateResponse("/knee/tibia-inner-page2.html", {"request": request})


@app.get("/knee/tibia-inner-page3.html")
async def tibia_inner_page3(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('tibia-inner-page3')
    return templates.TemplateResponse("/knee/tibia-inner-page3.html", {"request": request})


@app.get("/knee/tibia-inner-page5.html")
async def tibia_inner_page5(request: Request):
    return templates.TemplateResponse("/knee/tibia-inner-page5.html", {"request": request})


@app.get("/knee/tibia-posterior-chamfer-resection.html")
async def tibia_posterior_chamfer_resection(request: Request):
    return templates.TemplateResponse("/knee/tibia-posterior-chamfer-resection.html", {"request": request})


@app.get("/knee/tibia-posterior-resection.html")
async def tibia_posterior_resection(request: Request):
    return templates.TemplateResponse("/knee/tibia-posterior-resection.html", {"request": request})


@app.get("/knee/robot_position.html")
async def robot_workspace(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('robot_workspace')
    # value = checkLoopExitCondition()
    # if value == -1 or value == False:
    #     SetLoopExitCondition(False)
    # print(f'Setting Loop To False so now we can start {value}')
    # knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    # processId = subprocess.Popen([venv_python, knee_marking_script, 'robot_workspace'])
    return templates.TemplateResponse("/knee/robot_position.html", {"request": request})


@app.get("/knee/robot_workspace.html")
async def robot_workspace_position(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('robot_workspace_position')
    # knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    # processId = subprocess.Popen([venv_python, knee_marking_script, 'robot_workspace'])
    return templates.TemplateResponse("/knee/robot_workspace.html", {"request": request})


@app.get("/knee/Robot_intoduction.html")
async def Robot_intoduction(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('Robot_intoduction')
    return templates.TemplateResponse("/knee/Robot_intoduction.html", {"request": request})


# Routes for TKR screens
@app.get("/knee/tkr-screen-2.html")
async def tkr_screen_2(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('LandmarkVerification')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'LandMarkVerification'])
    return templates.TemplateResponse("/knee/tkr-screen-2.html", {"request": request})


@app.get("/knee/tkr-screen-3.html")
async def tkr_screen_3(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('tkr-screen-3')
    return templates.TemplateResponse("/knee/tkr-screen-3.html", {"request": request})


# Routes for other pages
@app.get("/knee/inner-page.html")
async def inner_page(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('inner-page')
    return templates.TemplateResponse("/knee/inner-page.html", {"request": request})


@app.get("/knee/inner-page6.html")
async def inner_page6(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('AlignmentAngels')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'AlignmentAngels'])
    return templates.TemplateResponse("/knee/inner-page6.html", {"request": request})


@app.get("/knee/multiple-page.html")
async def multiple_page(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('multiple-page')
    return templates.TemplateResponse("/knee/multiple-page.html", {"request": request})


@app.get("/knee/multiple-page2.html")
async def multiple_page2(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('KneeMarking')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'KneeMarking'])
    return templates.TemplateResponse("/knee/multiple-page2.html", {"request": request})


##########################################################################################################
#                         Revision Knee Procedures
##########################################################################################################

@app.get("/revision-knee/balance-in-extension.html")
async def balance_in_extension(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('balance-in-extension')
    return templates.TemplateResponse("/revision-knee/balance-in-extension.html", {"request": request})


@app.get("/revision-knee/balance-in-flexion.html")
async def balance_in_flexion(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('balance-in-flexion')
    return templates.TemplateResponse("/revision-knee/balance-in-flexion.html", {"request": request})


@app.get("/revision-knee/conflict-message.html")
async def conflict_message(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('conflict-message')
    return templates.TemplateResponse("/revision-knee/conflict-message.html", {"request": request})


@app.get("/revision-knee/femur-im-canal-ream.html")
async def femur_im_canal_ream(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('femur-im-canal-ream')
    return templates.TemplateResponse("/revision-knee/femur-im-canal-ream.html", {"request": request})


@app.get("/revision-knee/graph-screen.html")
async def graph_screen(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('graph-screen')
    return templates.TemplateResponse("/revision-knee/graph-screen.html", {"request": request})


@app.get("/revision-knee/guidance-distal-femur-cut.html")
async def guidance_distal_femur_cut(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('verification-distal-femur-cut')
    return templates.TemplateResponse("/revision-knee/guidance-distal-femur-cut.html", {"request": request})


@app.get("/revision-knee/revision-knee-burr.html")
async def guidance_distal_femur_cut(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('verification-distal-femur-cut')
    return templates.TemplateResponse("/revision-knee/revision-knee-burr.html", {"request": request})


@app.get("/revision-knee/revision-knee-burrFemur.html")
async def guidance_distal_femur_cut(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('verification-distal-femur-cut')
    return templates.TemplateResponse("/revision-knee/revision-knee-burrFemur.html", {"request": request})


@app.get("/revision-knee/guidance-proximal-tibia-cut.html")
async def guidance_proximal_tibia_cut(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('guidance-proximal-tibia-cut')
    # knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    # processId = subprocess.Popen([venv_python, knee_marking_script, 'guidance-proximal-tibia-cut_test'])

    return templates.TemplateResponse("/revision-knee/guidance-proximal-tibia-cut.html", {"request": request})


@app.get("/revision-knee/inner-page6.html")
async def inner_page6(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('AlignmentAngels')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'AlignmentAngels'])
    return templates.TemplateResponse("/revision-knee/inner-page6.html", {"request": request})


@app.get("/revision-knee/ml-size-acquisition.html")
async def ml_size_acquisition(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('ml-size-acquisition')
    return templates.TemplateResponse("/revision-knee/ml-size-acquisition.html", {"request": request})


@app.get("/revision-knee/Aori_type.html")
async def Aori_type(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('Aori_type')
    return templates.TemplateResponse("/revision-knee/Aori_type.html", {"request": request})


@app.get("/revision-knee/Free_point_collection_femur.html")
async def Free_point_collection_femur(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('Free_point_collection_femur')
    return templates.TemplateResponse("/revision-knee/Free_point_collection_femur.html", {"request": request})


@app.get("/revision-knee/Free_point_collectionTibia.html")
async def Free_point_collection_femurn(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('Free_point_collectionTibia')
    return templates.TemplateResponse("/revision-knee/Free_point_collectionTibia.html", {"request": request})


@app.get("/revision-knee/multiple-page.html")
async def multiple_page(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('multiple-page')
    return templates.TemplateResponse("/revision-knee/multiple-page.html", {"request": request})


@app.get("/revision-knee/multiple-page2.html")
async def multiple_page2(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('KneeMarking')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'KneeMarking'])
    return templates.TemplateResponse("/revision-knee/multiple-page2.html", {"request": request})


@app.get("/revision-knee/planning-distal-femur-cut.html")
async def planning_distal_femur_cut(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('planning-distal-femur-cut')
    return templates.TemplateResponse("/revision-knee/planning-distal-femur-cut.html", {"request": request})


@app.get("/revision-knee/planning-proximal-tibia-cut.html")
async def planning_proximal_tibia_cut(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('planning-proximal-tibia-cut')
    return templates.TemplateResponse("/revision-knee/planning-proximal-tibia-cut.html", {"request": request})


@app.get("/revision-knee/remove-primary-implants.html")
async def remove_primary_implants(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('remove-primary-implants')
    return templates.TemplateResponse("/revision-knee/remove-primary-implants.html", {"request": request})


@app.get("/revision-knee/tibia-im-canal-ream.html")
async def tibia_im_canal_ream(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('tibia-im-canal-ream')
    return templates.TemplateResponse("/revision-knee/tibia-im-canal-ream.html", {"request": request})


# Routes for TKR screens
@app.get("/revision-knee/tkr-screen-2.html")
async def tkr_screen_2(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('LandMarkVerification')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'LandMarkVerification'])
    return templates.TemplateResponse("/revision-knee/tkr-screen-2.html", {"request": request})


@app.get("/revision-knee/tkr-screen-3.html")
async def tkr_screen_3(request: Request):
    return templates.TemplateResponse("/revision-knee/tkr-screen-3.html", {"request": request})


@app.get("/revision-knee/verification-distal-femur-cut.html")
async def verification_distal_femur_cut(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('verification-distal-femur-cut')
    return templates.TemplateResponse("/revision-knee/verification-distal-femur-cut.html", {"request": request})


@app.get("/revision-knee/verification-proximal-tibia-cut.html")
async def verification_proximal_tibia_cut(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('verification-proximal-tibia-cut')
    return templates.TemplateResponse("/revision-knee/verification-proximal-tibia-cut.html", {"request": request})


##########################################################################################################
#                         Uni Knee Procedures
##########################################################################################################

@app.get("/uni-knee/multiple-page2.html")
async def multiple_page2(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('UniKneeMarking')
    # SetLoopExitCondition('unimultiple-page2')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'UniKneeMarking'])
    return templates.TemplateResponse("/uni-knee/multiple-page2.html", {"request": request})


@app.get("/uni-knee/multiple-page.html")
async def multiple_page(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('unimultiple-page')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'unimultiple-page'])
    return templates.TemplateResponse("uni-knee/multiple-page.html", {"request": request})


@app.get("/uni-knee/tkr-screen-2.html")
async def tkr_screen2(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('unitkr-screen-2')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'unitkr-screen-2'])
    return templates.TemplateResponse("uni-knee/tkr-screen-2.html", {"request": request})


@app.get("/uni-knee/inner-page6.html")
async def tkr_screen2(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('uniinner-page6')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'uniinner-page6'])
    return templates.TemplateResponse("uni-knee/inner-page6.html", {"request": request})


@app.get("/uni-knee/Final_leg_Alignment.html")
async def Final_leg_Alignment(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('uniFinal_leg_Alignment')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'uniFinal_leg_Alignment'])
    return templates.TemplateResponse("uni-knee/Final_leg_Alignment.html", {"request": request})


@app.get("/uni-knee/femur-distal-femur-cut.html")
async def femur_distal_femur_cut(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('unifemur_distal_femur_cut')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'unifemur_distal_femur_cut'])
    return templates.TemplateResponse("uni-knee/femur-distal-femur-cut.html", {"request": request})


@app.get("/uni-knee/accp_femure_cut.html")
async def femur_distal_femur_cut(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('uni_accp_femur_cut')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'uni_accp_femur_cut'])
    return templates.TemplateResponse("uni-knee/accp_femure_cut.html", {"request": request})


@app.get("/uni-knee/tibia-cut.html")
async def femur_distal_femur_cut(request: Request):
    cleanup_old_data('')
    SetLoopExitCondition('uni_tibia-cut')
    knee_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/knee_marking.py'))
    processId = subprocess.Popen([venv_python, knee_marking_script, 'uni_tibia-cut'])
    return templates.TemplateResponse("uni-knee/tibia-cut.html", {"request": request})


##########################################################################################################
#                         Hip Procedures
##########################################################################################################

@app.get("/hip/operating-positio-screen.html")
async def read_operating_position_screen(request: Request):
    return templates.TemplateResponse("/hip/operating-positio-screen.html", {"request": request})


@app.get("/hip/acetabulum-component-placement.html")
async def read_acetabulum_component_placement(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('hip_FivePoint_register_2')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'hip_FivePoint_register_2'])
    return templates.TemplateResponse("/hip/acetabulum-component-placement.html", {"request": request})


@app.get("/hip/anterior-pelvic.html")
async def read_anterior_pelvic(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('anterior-pelvic')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'anterior-pelvic'])
    return templates.TemplateResponse("/hip/anterior-pelvic.html", {"request": request})


@app.get("/hip/femur-registration.html")
async def read_femur_registration(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('hip_tp_register')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'hip_tp_register'])
    return templates.TemplateResponse("/hip/femur-registration.html", {"request": request})


@app.get("/hip/final-cup-position.html")
async def read_final_cup_position(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('final-cup-position')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'final-cup-position'])
    return templates.TemplateResponse("/hip/final-cup-position.html", {"request": request})


@app.get("/hip/handle-position.html")
async def read_handle_position(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('handle-position')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'handle-position'])
    return templates.TemplateResponse("/hip/handle-position.html", {"request": request})


@app.get("/hip/leg-length-assessment.html")
async def read_leg_length_assessment(request: Request):
    cleanup_old_data()
    return templates.TemplateResponse("/hip/leg-length-assessment.html", {"request": request})


@app.get("/hip/mid-axial-body-plane.html")
async def read_mid_axial_body_plane(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('mid-axial-body-plane')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'mid-axial-body-plane'])
    return templates.TemplateResponse("/hip/mid-axial-body-plane.html", {"request": request})


@app.get("/hip/pelvis-registration.html")
async def read_pelvis_registration(request: Request):
    cleanup_old_data()
    return templates.TemplateResponse("/hip/pelvis-registration.html", {"request": request})


@app.get("/hip/pelvis-registration-2.html")
async def read_pelvis_registration_2(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('hip_ThreePoint_register')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'hip_ThreePoint_register'])
    return templates.TemplateResponse("/hip/pelvis-registration-2.html", {"request": request})


@app.get("/hip/pelvis-registration-3.html")
async def read_pelvis_registration_3(request: Request):
    return templates.TemplateResponse("/hip/pelvis-registration-3.html", {"request": request})


@app.get("/hip/register-acetabulum.html")
async def register_acetabulum(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('hip_FivePoint_register')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'hip_FivePoint_register'])
    return templates.TemplateResponse("/hip/register-acetabulum.html", {"request": request})


@app.get("/hip/system-setup.html")
async def system_setup(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('system-setup')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'system-setup'])
    return templates.TemplateResponse("/hip/system-setup.html", {"request": request})


@app.get("/hip/TP2_marking.html")
async def tp2_marking(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('TP2_marking')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'TP2_marking'])
    return templates.TemplateResponse("/hip/TP2_marking.html", {"request": request})


@app.get("/hip/system-setup-2.html")
async def system_setup_2(request: Request):
    return templates.TemplateResponse("/hip/system-setup-2.html", {"request": request})


##########################################################################################################
#                         Revision Hip Procedures
##########################################################################################################
@app.get("/revision-thr/operating-positio-screen.html")
async def operating_positio_screen(request: Request):
    return templates.TemplateResponse("/revision-thr/operating-positio-screen.html", {"request": request})


@app.get("/revision-thr/acetabulum-component-placement.html")
async def acetabulum_component_placement(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('hip_FivePoint_register_2')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'hip_FivePoint_register_2'])
    return templates.TemplateResponse("/revision-thr/acetabulum-component-placement.html", {"request": request})


@app.get("/revision-thr/anterior-pelvic.html")
async def anterior_pelvic(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('anterior-pelvic')
    return templates.TemplateResponse("/revision-thr/anterior-pelvic.html", {"request": request})


@app.get("/revision-thr/femur-registration.html")
async def femur_registration(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('hip_tp_register')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'hip_tp_register'])
    return templates.TemplateResponse("/revision-thr/femur-registration.html", {"request": request})


@app.get("/revision-thr/final-cup-position.html")
async def final_cup_position(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('final-cup-position')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'final-cup-position'])
    return templates.TemplateResponse("/revision-thr/final-cup-position.html", {"request": request})


@app.get("/revision-thr/handle-position.html")
async def handle_position(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('handle-position')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'handle-position'])
    return templates.TemplateResponse("/revision-thr/handle-position.html", {"request": request})


@app.get("/revision-thr/leg-length-assessment.html")
async def leg_length_assessment(request: Request):
    return templates.TemplateResponse("/revision-thr/leg-length-assessment.html", {"request": request})


@app.get("/revision-thr/mid-axial-body-plane.html")
async def mid_axial_body_plane(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('mid-axial-body-plane')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'mid-axial-body-plane'])
    return templates.TemplateResponse("/revision-thr/mid-axial-body-plane.html", {"request": request})


@app.get("/revision-thr/pelvis-registration.html")
async def pelvis_registration(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('pelvis-registration')
    return templates.TemplateResponse("/revision-thr/pelvis-registration.html", {"request": request})


@app.get("/revision-thr/pelvis-registration-2.html")
async def pelvis_registration_2(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('hip_ThreePoint_register')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'hip_ThreePoint_register'])
    return templates.TemplateResponse("/revision-thr/pelvis-registration-2.html", {"request": request})


@app.get("/revision-thr/pelvis-registration-3.html")
async def pelvis_registration_3(request: Request):
    return templates.TemplateResponse("/revision-thr/pelvis-registration-3.html", {"request": request})


@app.get("/revision-thr/register-acetabulum.html")
async def register_acetabulum(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('hip_Revision_SixPoint_register')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'hip_Revision_SixPoint_register'])
    return templates.TemplateResponse("/revision-thr/register-acetabulum.html", {"request": request})


@app.get("/revision-thr/system-setup.html")
async def system_setup(request: Request):
    return templates.TemplateResponse("/revision-thr/system-setup.html", {"request": request})


@app.get("/revision-thr/TP2_marking.html")
async def tp2_marking(request: Request):
    cleanup_old_data()
    SetLoopExitCondition('TP2_marking')
    hip_marking_script = os.path.abspath(os.path.join(current_dir, '../kneeSequence/marking/primary/hip_marking.py'))
    processId = subprocess.Popen([venv_python, hip_marking_script, 'TP2_marking'])
    return templates.TemplateResponse("/revision-thr/TP2_marking.html", {"request": request})


@app.get("/revision-thr/system-setup-2.html")
async def system_setup_2(request: Request):
    return templates.TemplateResponse("/revision-thr/system-setup-2.html", {"request": request})


@app.get("/revision-thr/result.html")
async def result(request: Request):
    return templates.TemplateResponse("/revision-thr/result.html", {"request": request})


async def send_tuple(websocket: WebSocket, data: tuple):
    serialized_data = ','.join(map(str, data))
    await websocket.send_text(serialized_data)


# Define the function to introduce a delay
async def delayed_send(websocket: WebSocket, delay: float):
    await asyncio.sleep(delay)


class EventData(BaseModel):
    selectedPlane: str
    event: str
    pageName: str  # Add pageName to the event data


@app.post("/api/log_event")
async def log_event(event_data: EventData):
    # global processId
    # value = checkLoopExitCondition()
    # if value == -1 or value == False:
    #     SetLoopExitCondition(True)
    writeRegisteredPoint('exit')
    writeRegisteredPoint('exit')
    print(f"Event logged: {event_data}")
    # if processId:
    #     processId.terminate()  # Or use process.kill() if terminate doesn't work
    #     processId.wait()  # Wait for the process to exit

    return {"message": "Event logged successfully", "data": event_data}


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    # global web=WebSocket
    await websocket.accept()
    try:
        point = checkLoopExitCondition().lower()
        if point in ['kneemarking', 'unikneemarking', 'unimultiple-page']:
            data = getRegisteredPoint()

            if data == -1:
                return
            await send_tuple(websocket, (data, 0.4, 5.6, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            # if data == 'NOTE2':
            #     writeRegisteredPoint('exit')
            #     await send_tuple(websocket, ('exit', 0.4, 5.6, 7.3))
            return

        if point == 'LandmarkVerification'.lower():
            data = GetDataFromBackEnd()
            print(f'data {data}')
            if data != -1:
                await send_tuple(websocket, ('data', abs(data), 5.6, 7.3))
                await delayed_send(websocket, 1)  # 0.5 second delay
            else:
                await send_tuple(websocket, ('data', 'Error', 5.6, 7.3))
            return
        if point == 'unitkr-screen-2':
            data = GetDataFromBackEnd()
            # print(f'data******************************* {data}')
            if data != -1:
                await send_tuple(websocket, ('data', abs(data), 5.6, 7.3))
                await delayed_send(websocket, 1)  # 0.5 second delay
            else:
                await send_tuple(websocket, ('data', 'Error', 5.6, 7.3))
            return
        if point == 'robot_workspace':
            data = GetDataFromBackEnd()
            print(f'data {data}')
            await send_tuple(websocket, ('data', data, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            return
        if point == 'robot_workspace_position':
            data = GetDataFromBackEnd()
            print(f'data {data}')
            await send_tuple(websocket, ('data', data, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            return
        if point == 'femur_distal_femur_cut':
            data = getanglefromBackend()
            data1 = getanglefromBackend('ROM2')
            data2 = getanglefromBackend('ROM3')
            data3 = getanglefromBackend('ROM4')

            print(f'data {data}')
            # if data != -1:
            await send_tuple(websocket, ('femur-inner-page5-2', abs(int(data)), 5.6, 7.3))
            await send_tuple(websocket, ('femur-inner-page5-3', abs(int(data1)), 5.6, 7.3))
            await send_tuple(websocket, ('femur-inner-page5-5', abs(int(data2)), 5.6, 7.3))
            # await send_tuple(websocket, ('femur-inner-page5-6', abs(int(data3)), 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            # await send_tuple(websocket, ('RED', abs(data), 5.6, 7.3))
            # await delayed_send(websocket, 10)  # 0.5 second delay
            return
        if point == 'Anteriorfemurecuts'.lower():
            data = GetDataFromBackEnd()
            print(f'data {data}')
            # if data != -1:
            await send_tuple(websocket, (data, data, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            return
        if point == 'posteriorChamferfemurecut'.lower():
            data = GetDataFromBackEnd()
            print(f'data {data}')
            # if data != -1:
            await send_tuple(websocket, ('GREEN', abs(data), 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('RED', abs(data), 5.6, 7.3))
            # await send_tuple(websocket, ('RED', abs(data), 5.6, 7.3))
            return
        if point == 'posteriorfemurecut':
            data = GetDataFromBackEnd()
            print(f'data {data}')
            if data != -1:
                await send_tuple(websocket, ('GREEN', abs(data), 5.6, 7.3))
                await delayed_send(websocket, 1)  # 0.5 second delay
                await send_tuple(websocket, ('RED', abs(data), 5.6, 7.3))
        if point == 'anteriorchamferfemurecut':
            data = GetDataFromBackEnd()
            print(f'data {data}')
            # if data != -1:
            await send_tuple(websocket, ('GREEN', abs(data), 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('RED', abs(data), 5.6, 7.3))
            return
        if point == 'Tibia_cut'.lower():
            data = GetDataFromBackEnd()
            print(f'data {data}')
            if data != -1:
                await send_tuple(websocket, ('GREEN', abs(data), 5.6, 7.3, abs(data), 5.6, 7.3))
                await delayed_send(websocket, 1)  # 0.5 second delay
                await send_tuple(websocket, ('RED', abs(data), 5.6, 7.3, abs(data), 5.6, 7.3))
        if point == 'AlignmentAngels'.lower():
            data = getanglefromBackend()
            data1 = getanglefromBackend('ROM2')
            # data2 = getanglefromBackend('ROM3')
            # data3 = getanglefromBackend('ROM4')
            # data4 = getanglefromBackend('ROM5')
            # data5 = getanglefromBackend('ROM6')

            if data != -1:
                # await send_tuple(websocket, ('', data, 5.6, 7.3))
                # await delayed_send(websocket, 0.5)  # 0.5 second delay
                # print(f'ROM1 {data}')
                await send_tuple(websocket, ('ROM1', abs(int(data)), 5.6, 7.3))
                print(f'ROM1 {data}')
                await send_tuple(websocket, ('ROM2', abs(int(data1)), 5.6, 7.3))
                # await send_tuple(websocket, ('ROM3', abs(int(data2)), 5.6, 7.3))
                # await send_tuple(websocket, ('ROM4', abs(int(data3)), 5.6, 7.3))
                # await send_tuple(websocket, ('ROM5', abs(int(data4)), 5.6, 7.3))
                # await send_tuple(websocket, ('ROM6', abs(int(data5)), 5.6, 7.3))
                await delayed_send(websocket, 0.5)  # 0.5 second delay
            return
        if point == 'uniinner-page6':
            data = getanglefromBackend()
            data1 = getanglefromBackend('ROM2')
            if data != -1:
                await send_tuple(websocket, ('ROM1', abs(int(data)), 5.6, 7.3))
                print(f'ROM1 {data}')
                await send_tuple(websocket, ('ROM2', abs(int(data1)), 5.6, 7.3))
                await delayed_send(websocket, 0.5)  # 0.5 second delay
            return

        if point == 'unifemur_distal_femur_cut':
            data = GetDataFromBackEnd()
            print(f'data {data}')
            if data != -1:
                await send_tuple(websocket, ('GREEN', abs(data), 5.6, 7.3, abs(data), 5.6, 7.3))
                await delayed_send(websocket, 5)  # 0.5 second delay
                await send_tuple(websocket, ('YELLOW', abs(data), 5.6, 7.3, abs(data), 5.6, 7.3))
                await delayed_send(websocket, 5)  # 0.5 second delay
                await send_tuple(websocket, ('RED', abs(data), 5.6, 7.3, abs(data), 5.6, 7.3))
                await delayed_send(websocket, 5)  # 0.5 second delay
        if point == 'uni_tibia-cut':
            data = GetDataFromBackEnd()
            print(f'data {data}')
            if data != -1:
                await send_tuple(websocket, ('GREEN', abs(data), 5.6, 7.3, abs(data), 5.6, 7.3))
                await delayed_send(websocket, 5)  # 0.5 second delay
                await send_tuple(websocket, ('YELLOW', abs(data), 5.6, 7.3, abs(data), 5.6, 7.3))
                await delayed_send(websocket, 5)  # 0.5 second delay
                await send_tuple(websocket, ('RED', abs(data), 5.6, 7.3, abs(data), 5.6, 7.3))
                await delayed_send(websocket, 5)  # 0.5 second delay
        if point == 'uni_accp_femur_cut':
            data = GetDataFromBackEnd()
            print(f'data {data}')
            if data != -1:
                # await send_tuple(websocket, ('Init', abs(data), 5.6, 7.3, abs(data), 5.6, 7.3))
                # await delayed_send(websocket, 5)  # 0.5 second delay
                await send_tuple(websocket, ('RED', abs(data), 5.6, 7.3))
                await delayed_send(websocket, 5)  # 0.5 second dela
                await send_tuple(websocket, ('RED', abs(data), 5.6, 7.3))
                await delayed_send(websocket, 5)  # 0.5 second delay
                await send_tuple(websocket, ('GREEN', abs(data), 5.6, 7.3))
                await delayed_send(websocket, 5)  # 0.5 second delay
        # Hip
        if point in ['hip_tp_register', 'hip_threepoint_register', 'mid-axial-body-plane', 'hip_fivepoint_register',
                     'hip_fivepoint_register_2', 'tp2_marking', 'hip_revision_sixpoint_register', 'system-setup']:
            data = getRegisteredPoint()
            if data == -1:
                return
            await send_tuple(websocket, (data, 0.4, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            # if data == 'NOTE2':
            #     writeRegisteredPoint('exit')
            #     await send_tuple(websocket, ('exit', 0.4, 5.6, 7.3))
            return
        # if point == 'hip_tp_register':
        #     data = GetDataFromBackEnd()
        #     print(f'data {data}')
        #     if data != -1:
        #         # await send_tuple(websocket, ('Init', abs(data), 5.6, 7.3, abs(data), 5.6, 7.3))
        #         # await delayed_send(websocket, 5)  # 0.5 second delay
        #         await send_tuple(websocket, ('RED', abs(data), 5.6, 7.3))
        #         await delayed_send(websocket, 5)  # 0.5 second dela
        #         await send_tuple(websocket, ('RED', abs(data), 5.6, 7.3))
        #         await delayed_send(websocket, 5)  # 0.5 second delay
        #         await send_tuple(websocket, ('GREEN', abs(data), 5.6, 7.3))
        #         await delayed_send(websocket, 5)  # 0.5 second delay
        if point == 'anterior-pelvic':
            data = getRegisteredPoint()
            if data == -1:
                return
            if data == 'exit':
                await send_tuple(websocket, (data, data[0], data[2], 7.3))
            else:
                data = GetDataFromBackEnd()
                if not isinstance(data, list):
                    # Set default values or return an error response
                    data = [0, 0]  # Default values if data is not a list
                await send_tuple(websocket, ('anterior-pelvic-2', data[0], data[2], 7.3))
                await send_tuple(websocket, ('anterior-pelvic-3', data[1], data[2], 7.3))
        if point == 'handle-position':
            data = getRegisteredPoint()
            if data == 'exit':
                await send_tuple(websocket, (data, 4, 5, 7.3))
            else:
                data = GetDataFromBackEnd()
                print(f'data :{data}')
                if not isinstance(data, list):
                    # Set default values or return an error response
                    data = [0, 0, 0]  # Default values if data is not a list
                await send_tuple(websocket, ('handle-position-1', data[0], data[2], 7.3))
                await send_tuple(websocket, ('handle-position-2', data[1], data[2], 7.3))
                await send_tuple(websocket, ('size-update', data[2], data[2], 7.3))
        if point == 'mid-axial-body-plane':
            data = getRegisteredPoint()
            if data == -1:
                return
            if data == 'exit':
                await send_tuple(websocket, (data, 4.0, 5.0, 7.3))
            else:
                data = GetDataFromBackEnd()
                if not isinstance(data, list):
                    # Set default values or return an error response
                    data = [0, 0, 0]  # Default values if data is not a list
                await send_tuple(websocket, (data[0], 4.0, 5.0, 7.3))
                await send_tuple(websocket, (data[1], data[1], data[2], 7.3))
                await send_tuple(websocket, (data[2], data[0], data[2], 7.3))
        if point == 'final-cup-position':
            data = getRegisteredPoint()
            if data == 'exit':
                await send_tuple(websocket, (data, 4.5, 9.5, 7.3))
            else:
                data = GetDataFromBackEnd()
                if not isinstance(data, list):
                    # Set default values or return an error response
                    data = [0, 0, 0]  # Default values if data is not a list
                # Return a JSON response with validated data
                await send_tuple(websocket, ('final-cup-position-1', data[0], 5.0, 7.3))
                await send_tuple(websocket, ('final-cup-position-2', data[1], data[2], 7.3))
        # if point == -1:
        #     return
        # await send_tuple(websocket, (point, 0.4, 5.6, 7.3))
        # await delayed_send(websocket, 0.5)  # 0.5 second delay
        # cleanup_old_data()
        # if point == 'AC':
        #     cleanup_old_data()
        # if point == 'LM':
        #     cleanup_old_data('LandmarkVerification')
        # if point == 'NOTE2':
        #     cleanup_old_data('exit')
        # if point == 'STEP6':
        #     SetLoopExitCondition(True)
        # if point == 'PT':
        #     SetLoopExitCondition(True)

    finally:
        await websocket.close()


#

def open_browser():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.showFullScreen()  # Set the window to full screen
    sys.exit(app.exec())


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    try:
        page = checkLoopExitCondition().lower()

        if page == 'multiple-page2':
            await delayed_send(websocket, 10)  # 0.5 second delay
            await send_tuple(websocket, ('HIF', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 4)  # 0.5 second delay

            await send_tuple(websocket, ('HC1', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC2', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC3', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC4', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC5', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC6', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC7', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC8', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC9', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC10', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC11', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC12', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC13', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC14', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC15', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC16', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC17', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC18', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC19', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC20', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC21', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('FC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('ME', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('LE', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('FAP', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('MDC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('LDC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('MPC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('LPC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('AC', 4.0, 5.6, 7.3))
        if page == 'multiple-page':
            await send_tuple(websocket, ('TC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('TAP', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('MC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('LC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('MM', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('LM', 4.0, 5.6, 7.3))
        if page == 'tkr-screen-2':
            # data = GetDataFromBackEnd()  # ../../kneeSequence/register_points/data.txt
            data = 0.2  # ../../kneeSequence/register_points/data.txt
            if data == 'exit':
                await send_tuple(websocket, (data, 0.5, 5.6, 7.3))
            await send_tuple(websocket, ('data1', abs(data), 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('data1', abs(0.3), 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data1', abs(0.5), 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data1', abs(0.1), 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', abs(0.1), 5.6, 7.3))
        if page == 'inner-page6':
            data1 = getanglefromBackend('ROM1')  # ../../kneeSequence/register_points/data.txt
            if data1 == 'exit':
                await send_tuple(websocket, (data, 0.5, 5.6, 7.3))
            # data2 = getanglefromBackend('ROM2')
            # data3 = getanglefromBackend('ROM3')
            # data4 = getanglefromBackend('ROM4')
            # data5 = getanglefromBackend('ROM5')
            # data6 = getanglefromBackend('ROM6')
            await send_tuple(websocket, ('ROM1', 10, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 45, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 11, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 46, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 12, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 47, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 13, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 48, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 15, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 49, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 18, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 50, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 20, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 51, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 22, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 52, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 24, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 53, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 26, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 54, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 28, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 55, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 30, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 56, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 32, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 57, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM3', 9, 10, 7.3))

            await send_tuple(websocket, ('ROM4', 10, 9, 7.3))
            await send_tuple(websocket, ('ROM5', 8, 8, 7.3))
            await send_tuple(websocket, ('ROM6', 9.3, 7, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 11, 5.6, 7.3))
            await send_tuple(websocket, ('ROM2', 57, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 34, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 58, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 36, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 60, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 38, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 62, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 40, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 64, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 42, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 66, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 44, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 68, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 42, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 70, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 40, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 72, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 38, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 74, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 36, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 76, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 34, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 78, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 30, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 80, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM2', 82, 5.6, 7.3))
            await send_tuple(websocket, ('ROM3', 8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM4', 10, 5.6, 7.3))
            await send_tuple(websocket, ('ROM5', 7.8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM6', 9.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 28, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 60, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 26, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 58, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 24, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 56, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 22, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 54, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 20, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 52, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 18, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 50, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 15, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 48, 40, 7.3))
            await send_tuple(websocket, ('ROM1', 15, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 45, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM3', 8.5, 8.5, 7.3))
            await send_tuple(websocket, ('ROM4', 10, 5.6, 7.3))
            await send_tuple(websocket, ('ROM5', 7.8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM6', 9.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            # await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            # SendDataToFrontEnd('exit')
        if page == 'ml-size-acquisition':
            await send_tuple(websocket, ('ml-size-acquisition-1', 10, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('ml-size-acquisition-1', 10, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('ml-size-acquisition-1', 10, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('ml-size-acquisition-1', 10, 10, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
        if page == 'tibia-im-canal-ream':
            await send_tuple(websocket, ('tibia-im-canal-ream-1', 11, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('tibia-im-canal-ream-1', 10, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('tibia-im-canal-ream-1', 11, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('tibia-im-canal-ream-1', 12, 10, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
        if page == 'femur-im-canal-ream':
            await send_tuple(websocket, ('femur-im-canal-ream-1', 11, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('femur-im-canal-ream-1', 10, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('femur-im-canal-ream-1', 11, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('femur-im-canal-ream-1', 12, 10, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
        if page == 'planning-proximal-tibia-cut':
            await send_tuple(websocket, ('planning-proximal-tibia-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('planning-proximal-tibia-cut-2', 10, 10, 7.3))
            await send_tuple(websocket, ('planning-proximal-tibia-cut-3', 11, 10, 7.3))
            await send_tuple(websocket, ('planning-proximal-tibia-cut-4', 12, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('planning-proximal-tibia-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('planning-proximal-tibia-cut-2', 10, 10, 7.3))
            await send_tuple(websocket, ('planning-proximal-tibia-cut-3', 11, 10, 7.3))
            await send_tuple(websocket, ('planning-proximal-tibia-cut-4', 12, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('planning-proximal-tibia-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('planning-proximal-tibia-cut-2', 10, 10, 7.3))
            await send_tuple(websocket, ('planning-proximal-tibia-cut-3', 11, 10, 7.3))
            await send_tuple(websocket, ('planning-proximal-tibia-cut-4', 12, 10, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
        if page == 'planning-distal-femur-cut':
            await send_tuple(websocket, ('planning-distal-femur-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('planning-distal-femur-cut-2', 10, 10, 7.3))
            await send_tuple(websocket, ('planning-distal-femur-cut-3', 11, 10, 7.3))
            await send_tuple(websocket, ('planning-distal-femur-cut-4', 12, 10, 7.3))
            await send_tuple(websocket, ('planning-distal-femur-cut-5', 12, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('planning-distal-femur-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('planning-distal-femur-cut-2', 10, 10, 7.3))
            await send_tuple(websocket, ('planning-distal-femur-cut-3', 11, 10, 7.3))
            await send_tuple(websocket, ('planning-distal-femur-cut-4', 12, 10, 7.3))
            await send_tuple(websocket, ('planning-distal-femur-cut-5', 12, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('planning-distal-femur-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('planning-distal-femur-cut-2', 10, 10, 7.3))
            await send_tuple(websocket, ('planning-distal-femur-cut-3', 11, 10, 7.3))
            await send_tuple(websocket, ('planning-distal-femur-cut-4', 12, 10, 7.3))
            await send_tuple(websocket, ('planning-distal-femur-cut-5', 12, 10, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
        if page == 'guidance-proximal-tibia-cut':
            await send_tuple(websocket, ('guidance-proximal-tibia-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('guidance-proximal-tibia-cut-2', 10, 10, 7.3))
            await send_tuple(websocket, ('guidance-proximal-tibia-cut-3', 11, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('guidance-proximal-tibia-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('guidance-proximal-tibia-cut-2', 10, 10, 7.3))
            await send_tuple(websocket, ('guidance-proximal-tibia-cut-3', 11, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('guidance-proximal-tibia-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('guidance-proximal-tibia-cut-2', 10, 10, 7.3))
            await send_tuple(websocket, ('guidance-proximal-tibia-cut-3', 11, 10, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
        if page == 'guidance-distal-femur-cut':
            await send_tuple(websocket, ('guidance-distal-femur-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('guidance-distal-femur-cut-2', 10, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('guidance-distal-femur-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('guidance-distal-femur-cut-2', 10, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('guidance-distal-femur-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('guidance-distal-femur-cut-2', 10, 10, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
        if page == 'verification-proximal-tibia-cut':
            await send_tuple(websocket, ('verification-proximal-tibia-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('verification-proximal-tibia-cut-2', 10, 10, 7.3))
            await send_tuple(websocket, ('verification-proximal-tibia-cut-3', 11, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('verification-proximal-tibia-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('verification-proximal-tibia-cut-2', 10, 10, 7.3))
            await send_tuple(websocket, ('verification-proximal-tibia-cut-3', 11, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('verification-proximal-tibia-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('verification-proximal-tibia-cut-2', 10, 10, 7.3))
            await send_tuple(websocket, ('verification-proximal-tibia-cut-3', 11, 10, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
        if page == 'verification-distal-femur-cut':
            await send_tuple(websocket, ('verification-distal-femur-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('verification-distal-femur-cut-2', 10, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('verification-distal-femur-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('verification-distal-femur-cut-2', 10, 10, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('verification-distal-femur-cut-1', 11, 10, 7.3))
            await send_tuple(websocket, ('verification-distal-femur-cut-2', 10, 10, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
        if page == 'balance-in-extension':
            await send_tuple(websocket, ('balance-in-extension-1', 11, 10, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('balance-in-extension-1', 11, 10, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('balance-in-extension-1', 11, 10, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('balance-in-extension-1', 11, 10, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
        if page == 'balance-in-flexion':
            await send_tuple(websocket, ('balance-in-flexion-1', 11, 10, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('balance-in-flexion-1', 11, 10, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('balance-in-flexion-1', 11, 10, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('balance-in-flexion-1', 11, 10, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
        if page == 'graph-screen':
            data1 = getanglefromBackend('ROM1')  # ../../kneeSequence/register_points/data.txt
            if data1 == 'exit':
                await send_tuple(websocket, (data, 0.5, 5.6, 7.3))
            # data2 = getanglefromBackend('ROM2')
            # data3 = getanglefromBackend('ROM3')
            # data4 = getanglefromBackend('ROM4')
            # data5 = getanglefromBackend('ROM5')
            # data6 = getanglefromBackend('ROM6')
            await send_tuple(websocket, ('ROM1', 10, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 45, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 11, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 46, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 12, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 47, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 13, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 48, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 15, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 49, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 18, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 50, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 20, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 51, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 22, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 52, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 24, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 53, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 26, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 54, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 28, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 55, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 30, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 56, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 32, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 57, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM3', 9, 10, 7.3))

            await send_tuple(websocket, ('ROM4', 10, 9, 7.3))
            await send_tuple(websocket, ('ROM5', 8, 8, 7.3))
            await send_tuple(websocket, ('ROM6', 9.3, 7, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 11, 5.6, 7.3))
            await send_tuple(websocket, ('ROM2', 57, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 34, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 58, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 36, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 60, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 38, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 62, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 40, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 64, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 42, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 66, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 44, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 68, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 42, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 70, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 40, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 72, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 38, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 74, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 36, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 76, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 34, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 78, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 30, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 80, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM2', 82, 5.6, 7.3))
            await send_tuple(websocket, ('ROM3', 8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM4', 10, 5.6, 7.3))
            await send_tuple(websocket, ('ROM5', 7.8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM6', 9.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 28, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 60, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 26, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 58, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 24, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 56, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 22, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 54, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 20, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 52, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 18, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 50, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 15, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 48, 40, 7.3))
            await send_tuple(websocket, ('ROM1', 15, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 45, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM3', 8.5, 8.5, 7.3))
            await send_tuple(websocket, ('ROM4', 10, 5.6, 7.3))
            await send_tuple(websocket, ('ROM5', 7.8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM6', 9.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            # await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            # SendDataToFrontEnd('exit')
        if page == 'robot_workspace':
            await send_tuple(websocket, ('data', '<-- RIGHT', 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'LEFT -->', 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'LEFT -->', 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('data', '<-- RIGHT', 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'OK', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'OK', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 'OK', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
        if page == 'robot_workspace_position':
            await send_tuple(websocket, ('data', 'InProgress.', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress..', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress...', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress....', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress.', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress..', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress...', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress....', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress.', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress..', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress...', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress....', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress.', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress..', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress...', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'InProgress....', 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data', 'OK', 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
        if page == 'femur-inner-page4':
            await send_tuple(websocket, ('femur-inner-page4-1', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('femur-inner-page4-2', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('femur-inner-page4-3', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('femur-inner-page4-1', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('femur-inner-page4-2', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('femur-inner-page4-3', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 11, 5.6, 7.3))
        if page == 'femur-distal-femur-cut':
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 11, 5.6, 7.3))
        if page == 'femur-inner-page5':
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('femur-inner-page5-2', 11, 5.6, 7.3))
            await send_tuple(websocket, ('femur-inner-page5-3', 11, 5.6, 7.3))
            await send_tuple(websocket, ('femur-inner-page5-5', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('femur-inner-page5-2', 11, 5.6, 7.3))
            await send_tuple(websocket, ('femur-inner-page5-3', 11, 5.6, 7.3))
            await send_tuple(websocket, ('femur-inner-page5-5', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('femur-inner-page5-2', 11, 5.6, 7.3))
            await send_tuple(websocket, ('femur-inner-page5-3', 11, 5.6, 7.3))
            await send_tuple(websocket, ('femur-inner-page5-5', 11, 5.6, 7.3))
            await send_tuple(websocket, ('exit', 11, 5.6, 7.3))
        if page == 'femur-anterior-resection':
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 11, 5.6, 7.3))
        if page == 'femur-anterior-chamfer-resection':
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 11, 5.6, 7.3))
        if page == 'femur-posterior-resection':
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
        if page == 'femur-posterior-chamfer-resection':
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 11, 5.6, 7.3))
        if page == 'tibia-inner-page2':
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('tibia-inner-page2-1', 11, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page2-2', 11, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page2-3', 11, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page2-4', 11, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('tibia-inner-page2-1', 11, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page2-2', 11, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page2-3', 11, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page2-4', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('tibia-inner-page2-1', 11, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page2-2', 11, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page2-3', 11, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page2-4', 11, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
        if page == 'femur-inner-page2':
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 11, 5.6, 7.3))
        if page == 'tibia-inner-page3':
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('tibia-inner-page3-1', 5, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page3-2', 10, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page3-3', 4, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page3-4', 9, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('tibia-inner-page3-1', 8, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page3-2', 5, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page3-3', 10, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page3-4', 6, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('tibia-inner-page3-1', 1, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page3-2', 4, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page3-3', 3, 5.6, 7.3))
            await send_tuple(websocket, ('tibia-inner-page3-4', 2, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 11, 5.6, 7.3))
        if page == 'tibia-graph-screen':
            data1 = getanglefromBackend('ROM1')  # ../../kneeSequence/register_points/data.txt
            if data1 == 'exit':
                await send_tuple(websocket, (data, 0.5, 5.6, 7.3))
            # data2 = getanglefromBackend('ROM2')
            # data3 = getanglefromBackend('ROM3')
            # data4 = getanglefromBackend('ROM4')
            # data5 = getanglefromBackend('ROM5')
            # data6 = getanglefromBackend('ROM6')
            await send_tuple(websocket, ('ROM1', 10, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 45, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 11, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 46, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 12, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 47, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 13, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 48, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 15, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 49, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 18, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 50, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 20, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 51, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 22, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 52, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 24, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 53, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 26, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 54, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 28, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 55, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 30, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 56, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 32, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 57, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM3', 9, 10, 7.3))

            await send_tuple(websocket, ('ROM4', 10, 9, 7.3))
            await send_tuple(websocket, ('ROM5', 8, 8, 7.3))
            await send_tuple(websocket, ('ROM6', 9.3, 7, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 11, 5.6, 7.3))
            await send_tuple(websocket, ('ROM2', 57, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 34, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 58, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 36, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 60, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 38, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 62, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 40, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 64, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 42, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 66, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 44, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 68, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 42, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 70, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 40, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 72, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 38, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 74, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 36, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 76, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 34, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 78, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 30, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 80, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM2', 82, 5.6, 7.3))
            await send_tuple(websocket, ('ROM3', 8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM4', 10, 5.6, 7.3))
            await send_tuple(websocket, ('ROM5', 7.8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM6', 9.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 28, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 60, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 26, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 58, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 24, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 56, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 22, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 54, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 20, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 52, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 18, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 50, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 15, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 48, 40, 7.3))
            await send_tuple(websocket, ('ROM1', 15, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 45, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM3', 8.5, 8.5, 7.3))
            await send_tuple(websocket, ('ROM4', 10, 5.6, 7.3))
            await send_tuple(websocket, ('ROM5', 7.8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM6', 9.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            # await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            # SendDataToFrontEnd('exit')
        if page == 'unimultiple-page2':
            await delayed_send(websocket, 10)  # 0.5 second delay
            await send_tuple(websocket, ('HIF', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 4)  # 0.5 second delay

            await send_tuple(websocket, ('HC1', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC2', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC3', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC4', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC5', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC6', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC7', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC8', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC9', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC10', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC11', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC12', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC13', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC14', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC15', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC16', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC17', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC18', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC19', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay

            await send_tuple(websocket, ('HC20', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('HC21', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('HC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('FC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('TM', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('TS1', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('TS2', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('MDC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('MPC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('ME', 4.0, 5.6, 7.3))
        if page == 'unimultiple-page':
            await send_tuple(websocket, ('TC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('AL1', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('AL2', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('MC', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

            await send_tuple(websocket, ('MP', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('MM', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('LM', 4.0, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay

        if page == 'unitkr-screen-2':
            # data = GetDataFromBackEnd()  # ../../kneeSequence/register_points/data.txt
            data = 0.2  # ../../kneeSequence/register_points/data.txt
            if data == 'exit':
                await send_tuple(websocket, (data, 0.5, 5.6, 7.3))
            await send_tuple(websocket, ('data1', abs(data), 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('data1', abs(0.3), 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data1', abs(0.5), 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('data1', abs(0.1), 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', abs(0.1), 5.6, 7.3))
        if page == 'uniinner-page6':
            data1 = getanglefromBackend('ROM1')  # ../../kneeSequence/register_points/data.txt
            if data1 == 'exit':
                await send_tuple(websocket, (data, 0.5, 5.6, 7.3))
            # data2 = getanglefromBackend('ROM2')
            # data3 = getanglefromBackend('ROM3')
            # data4 = getanglefromBackend('ROM4')
            # data5 = getanglefromBackend('ROM5')
            # data6 = getanglefromBackend('ROM6')
            await send_tuple(websocket, ('ROM1', 10, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 45, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 11, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 46, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 12, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 47, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 13, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 48, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 15, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 49, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 18, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 50, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 20, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 51, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 22, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 52, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 24, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 53, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 26, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 54, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 28, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 55, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 30, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 56, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 32, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 57, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM3', 9, 10, 7.3))

            await send_tuple(websocket, ('ROM4', 10, 9, 7.3))
            await send_tuple(websocket, ('ROM5', 8, 8, 7.3))
            await send_tuple(websocket, ('ROM6', 9.3, 7, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 11, 5.6, 7.3))
            await send_tuple(websocket, ('ROM2', 57, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 34, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 58, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 36, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 60, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 38, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 62, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 40, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 64, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 42, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 66, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 44, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 68, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 42, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 70, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 40, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 72, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 38, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 74, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 36, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 76, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 34, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 78, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 30, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 80, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM2', 82, 5.6, 7.3))
            await send_tuple(websocket, ('ROM3', 8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM4', 10, 5.6, 7.3))
            await send_tuple(websocket, ('ROM5', 7.8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM6', 9.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 28, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 60, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 26, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 58, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 24, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 56, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 22, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 54, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 20, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 52, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 18, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 50, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 15, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 48, 40, 7.3))
            await send_tuple(websocket, ('ROM1', 15, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 45, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM3', 8.5, 8.5, 7.3))
            await send_tuple(websocket, ('ROM4', 10, 5.6, 7.3))
            await send_tuple(websocket, ('ROM5', 7.8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM6', 9.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            # await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            # SendDataToFrontEnd('exit')
        if page == 'uniFinal_leg_Alignment'.lower():
            data1 = getanglefromBackend('ROM1')  # ../../kneeSequence/register_points/data.txt
            if data1 == 'exit':
                await send_tuple(websocket, (data, 0.5, 5.6, 7.3))
            # data2 = getanglefromBackend('ROM2')
            # data3 = getanglefromBackend('ROM3')
            # data4 = getanglefromBackend('ROM4')
            # data5 = getanglefromBackend('ROM5')
            # data6 = getanglefromBackend('ROM6')
            await send_tuple(websocket, ('ROM1', 10, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 45, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 11, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 46, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 12, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 47, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 13, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 48, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 15, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 49, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 18, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 50, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 20, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 51, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 22, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 52, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 24, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 53, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 26, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 54, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 28, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 55, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 30, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 56, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 32, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 57, 45, 7.3))
            await delayed_send(websocket, 0.2)  # 0.5 second delay
            await send_tuple(websocket, ('ROM3', 9, 10, 7.3))

            await send_tuple(websocket, ('ROM4', 10, 9, 7.3))
            await send_tuple(websocket, ('ROM5', 8, 8, 7.3))
            await send_tuple(websocket, ('ROM6', 9.3, 7, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 11, 5.6, 7.3))
            await send_tuple(websocket, ('ROM2', 57, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 34, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 58, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 36, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 60, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 38, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 62, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 40, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 64, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 42, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 66, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 44, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 68, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 42, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 70, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 40, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 72, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 38, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 74, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 36, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 76, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 34, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 78, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 30, 10, 7.3))
            await send_tuple(websocket, ('ROM2', 80, 5.6, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM2', 82, 5.6, 7.3))
            await send_tuple(websocket, ('ROM3', 8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM4', 10, 5.6, 7.3))
            await send_tuple(websocket, ('ROM5', 7.8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM6', 9.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 28, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 60, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 26, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 58, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 24, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 56, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 22, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 54, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 20, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 52, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 18, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 50, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM1', 15, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 48, 40, 7.3))
            await send_tuple(websocket, ('ROM1', 15, 45, 7.3))
            await send_tuple(websocket, ('ROM2', 45, 40, 7.3))
            await delayed_send(websocket, 0.5)  # 0.5 second delay
            await send_tuple(websocket, ('ROM3', 8.5, 8.5, 7.3))
            await send_tuple(websocket, ('ROM4', 10, 5.6, 7.3))
            await send_tuple(websocket, ('ROM5', 7.8, 5.6, 7.3))
            await send_tuple(websocket, ('ROM6', 9.5, 5.6, 7.3))
            await delayed_send(websocket, 3)  # 0.5 second delay
            # await send_tuple(websocket, ('exit', 0.5, 5.6, 7.3))
            # SendDataToFrontEnd('exit')

        # if page == 'unifemur-distal-femur-cut':
        #     await delayed_send(websocket, 5)  # 0.5 second delay
        #     await send_tuple(websocket, ('YELLOW', 11, 5.6, 7.3))
        #     await delayed_send(websocket, 2)  # 0.5 second delay
        #     await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
        #     await delayed_send(websocket, 5)  # 0.5 second delay
        #     await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
        #     await delayed_send(websocket, 2)  # 0.5 second delay
        #     await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
        #     await delayed_send(websocket, 2)  # 0.5 second delay
        #     await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
        #     await delayed_send(websocket, 1)  # 0.5 second delay
        #     await send_tuple(websocket, ('exit', 11, 5.6, 7.3))
        if page == 'uniaccp_femure_cut':
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('YELLOW', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 11, 5.6, 7.3))
        if page == 'unitibia-cut':
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('YELLOW', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 5)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('RED', 11, 5.6, 7.3))
            await delayed_send(websocket, 2)  # 0.5 second delay
            await send_tuple(websocket, ('GREEN', 11, 5.6, 7.3))
            await delayed_send(websocket, 1)  # 0.5 second delay
            await send_tuple(websocket, ('exit', 11, 5.6, 7.3))

            # SendDataToFrontEnd('exit')
    except Exception as e:
        print(f'e {e}')


@app.get("/values")
async def get_values():
    try:
        # Open the JSON file and read it
        with open(r"D:\SpineSurgery\pythonProject\values.json", "r") as file:
            data = json.load(file)
        return JSONResponse(content=data)
    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)


import random


@app.get("/get-data")
async def get_data():
    data = GetDataFromBackEnd()
    # Check if data is a list
    if not isinstance(data, list):
        # Set default values or return an error response
        data = [0, 0]  # Default values if data is not a list
    # Return a JSON response with validated data
    return JSONResponse({
        "anteversion": int(data[1]),
        "inclination": int(data[0])
    })


# @app.get("/get-graph_data")
# async def get_data():
#     # data = getanglefromBackend('graph')
#     data = {10: {'left': 18, 'right': 2}, 20: {'left': 29, 'right': 18}, 30: {'left': 11, 'right': 8}, 40: {'left': 19, 'right': 29}, 50: {'left': 1, 'right': 15}, 60: {'left': 18, 'right': 27}, 70: {'left': 29, 'right': 6}}
#     left_data = []
#     right_data = []
#
#     for anteversion, values in data.items():
#         left_data.append({'anteversion': anteversion, 'left': values['left']})
#         right_data.append({'anteversion': anteversion, 'right': values['right']})
#
#     # Construct the response with left and right data
#     return JSONResponse({
#         "left": left_data,
#         "right": right_data
#     })

# Function to generate random values for left and right
def generate_random_data():
    data = {}
    for anteversion in range(0, 120, 10):  # Creating data for 10, 20, ..., 70
        left_value = random.randint(-18, -10)  # Random value for 'left' between -20 and -10
        right_value = random.randint(10, 18)  # Random value for 'right' between 10 and 20
        data[anteversion] = {'left': left_value, 'right': right_value}
    return data


from collections import defaultdict
import numpy as np

# Create a global variable to store historical data
angle_data_history = defaultdict(lambda: {'left': [], 'right': []})
MAX_HISTORY_SIZE = 5  # Number of samples to average

@app.get("/get-graph_data")
async def get_data():
    global angle_data_history
    
    # Get new data from backend
    data = getanglefromBackend('graph')
    print(f'data data data {data}')
    
    if data == -1 or not data:
        # Use example data with values closer to the vertical lines
        data = {10: {'left': -10, 'right': 10}, 20: {'left': -10, 'right': 10}, 30: {'left': -11, 'right': 9},
                40: {'left': -10, 'right': 10}, 50: {'left': -9, 'right': 9}, 60: {'left': -10, 'right': 10},
                70: {'left': -10, 'right': 9}}
    
    # Add new data to history
    for angle, values in data.items():
        # Add left and right values to history
        angle_data_history[angle]['left'].append(values['left'])
        angle_data_history[angle]['right'].append(values['right'])
        
        # Limit history size
        if len(angle_data_history[angle]['left']) > MAX_HISTORY_SIZE:
            angle_data_history[angle]['left'] = angle_data_history[angle]['left'][-MAX_HISTORY_SIZE:]
        if len(angle_data_history[angle]['right']) > MAX_HISTORY_SIZE:
            angle_data_history[angle]['right'] = angle_data_history[angle]['right'][-MAX_HISTORY_SIZE:]
    
    # Calculate averages
    averaged_data = {}
    for angle, values in angle_data_history.items():
        left_avg = np.mean(values['left']) if values['left'] else 0
        right_avg = np.mean(values['right']) if values['right'] else 0
        averaged_data[angle] = {'left': left_avg, 'right': right_avg}
    
    # Prepare response data
    left_data = []
    right_data = []
    for angle, values in averaged_data.items():
        left_data.append({'anteversion': angle, 'left': values['left']})
        right_data.append({'anteversion': angle, 'right': values['right']})
    
    # Return the averaged data
    return {"left": left_data, "right": right_data}


class MyWebEngineView(QWebEngineView):
    def __init__(self):
        super().__init__()
        self.settings().setAttribute(QWebEngineSettings.WebAttribute.JavascriptEnabled, True)  # Enable JavaScript

    def createWindow(self, type_):
        # Prevent opening a new window when a link is clicked
        return self


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Web Application with Close Button")

        # Create a browser view
        self.browser = MyWebEngineView()
        # self.browser.settings().setAttribute(MyWebEngineView.JavascriptEnabled, True)
        self.browser.setUrl(QUrl("http://127.0.0.1:8000"))  # Load the FastAPI page
        self.setCentralWidget(self.browser)

        # Create a close button with a "×" symbol
        self.close_button = QPushButton("×", self)
        self.close_button.setStyleSheet(
            """
            QPushButton {
                background-color: transparent;
                color: gray;
                font-size: 20px;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                color: red;
            }
            """
        )
        self.close_button.setFixedSize(30, 30)
        self.close_button.setToolTip("Close")  # Add a tooltip
        self.close_button.clicked.connect(self.close_window)

        # Position the button in the top-right corner
        self.close_button.move(self.width() - 40, 10)
        self.close_button.raise_()  # Ensure the button is on top

        # Handle resize events to reposition the button dynamically
        self.resizeEvent = self.on_resize

    def close_window(self):
        """Close the application."""
        self.close()

    def on_resize(self, event):
        """Reposition the close button on window resize."""
        super().resizeEvent(event)
        self.close_button.move(self.width() - 40, 10)


import uvicorn

if __name__ == "__main__":
    # Timer(1, open_browser).start()
    uvicorn.run(app, host="127.0.0.1", port=8000, reload=False)
