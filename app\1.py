import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np

# Define the key points
MDC = np.array([560.91873101, 467.45491136, 323.05824753])
MPC = np.array([558.82408474, 464.74147778, 324.36153396])
LDC = np.array([560.83604496, 469.31777322, 329.03150839])
LPC = np.array([558.40144302, 465.56915315, 327.85595968])

# Define medial and lateral path points
medial_path = np.array([
    [559.5, 466.61, 323.34],
    [559.59, 466.46, 323.38],
    [559.69, 466.31, 323.43],
    [559.79, 466.16, 323.47],
    [559.89, 466.01, 323.52],
    [559.98, 465.86, 323.56],
    [560.08, 465.71, 323.61],
    [560.18, 465.56, 323.65],
    [560.28, 465.41, 323.7]
])

lateral_path = np.array([
    [559.24, 468.37, 329.34],
    [559.42, 468.09, 329.42],
    [559.6, 467.82, 329.51],
    [559.78, 467.55, 329.59],
    [559.96, 467.28, 329.68],
    [560.14, 467.0, 329.76],
    [560.32, 466.73, 329.84],
    [560.49, 466.46, 329.93],
    [560.67, 466.19, 330.01]
])

# Create a 3D plot
fig = plt.figure(figsize=(12, 8))
ax = fig.add_subplot(111, projection='3d')

# Plot the key points
ax.scatter(*MDC, color='red', label='MDC')
ax.scatter(*MPC, color='blue', label='MPC')
ax.scatter(*LDC, color='green', label='LDC')
ax.scatter(*LPC, color='orange', label='LPC')
ax.text(*MDC, 'MDC', color='red')
ax.text(*MPC, 'MPC', color='blue')
ax.text(*LDC, 'LDC', color='green')
ax.text(*LPC, 'LPC', color='orange')

# Plot the medial path
ax.plot(medial_path[:,0], medial_path[:,1], medial_path[:,2], color='purple', label='Medial Path')
for i, point in enumerate(medial_path):
    ax.scatter(*point, color='purple')
    ax.text(*point, f'M{i+1}', color='purple')

# Plot the lateral path
ax.plot(lateral_path[:,0], lateral_path[:,1], lateral_path[:,2], color='cyan', label='Lateral Path')
for i, point in enumerate(lateral_path):
    ax.scatter(*point, color='cyan')
    ax.text(*point, f'L{i+1}', color='cyan')

# Labels and legend
ax.set_xlabel('X')
ax.set_ylabel('Y')
ax.set_zlabel('Z')
ax.set_title('3D Plot of Key and Path Points')
ax.legend()
plt.tight_layout()
plt.show()
