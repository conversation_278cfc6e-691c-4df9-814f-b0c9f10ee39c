<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="/static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>
<body>
    <div class="tbl" id="content">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page anterior-pelvic-section">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Anterior Pelvic and Functional Plane</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center justify-content-between">
                                        <div class="col-5">
                                            <div class="grid anterior-pelvic-grid ms-75">
                                                <div class="sub-grid p-0">
                                                    <!--<div class="depth mr-13 raisin-black-bg ">
                                                        <div class="depth_bg">Anteversion</div>
                                                        <div class="depth_btn">
                                                            <ul>
                                                                <li id='li-1'>15</li>
                                                                <sup class="fs-14">0</sup>
                                                            </ul>
                                                        </div>
                                                    </div>-->
                                                    <div class="depth raisin-black-bg ">
                                                        <div class="depth_bg">Inclination</div>
                                                        <div class="depth_btn">
                                                            <ul>
                                                                <li id="li-2">40</li>
                                                                <sup class="fs-14">0</sup>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                                <img src="../static/images/hip-bone/anterior-pelvic-1.jpg" class="img-fluid" alt="">
                                            </div>
                                        </div>
                                        <div class="col-5">
                                            <div class="grid anterior-pelvic-grid me-75">
                                                <img src="../static/images/hip-bone/anterior-pelvic-2.jpg" class="img-fluid" alt="">
                                                <div class="sub-grid p-0">
                                                    <div class="depth raisin-black-bg  mr-13">
                                                        <div class="depth_bg">Anteversion</div>
                                                        <div class="depth_btn">
                                                            <ul>
                                                                <li id="li-3">15</li>
                                                                <sup class="fs-14">0</sup>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <!--div class="depth raisin-black-bg ">
                                                        <div class="depth_bg">Inclination</div>
                                                        <div class="depth_btn">
                                                            <ul>
                                                                <li id="li-4">40</li>
                                                                <sup class="fs-14">0</sup>
                                                            </ul>
                                                        </div>
                                                    </div-->
                                                </div>
                                             
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="grid anterior-pelvic-img-3">
                                                <img src="../static/images/hip-bone/anterior-pelvic-3.jpg" class="img-fluid " alt="">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="btn border-0 pointer_bg">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider round"></span>
                                          </label>
                                        Auto center
                                    </div>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a id="backBtn" href="#" onclick="handleBackClick()"><span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back</a>
                                    </div>
									<script>
									function handleBackClick() {
										const selectedValue = localStorage.getItem('selectedPlane');
										console.log(selectedValue); // Debugging line

										let redirectUrl = '';
										if (selectedValue === 'coupon_question1') {
											redirectUrl = 'pelvis-registration.html'; // Adjust as needed
										} else if (selectedValue === 'coupon_question2') {
											redirectUrl = 'pelvis-registration.html'; // Adjust as needed
										} else if (selectedValue === 'coupon_question3') {
											redirectUrl = `mid-axial-body-plane.html?selectedPlane=${encodeURIComponent(selectedValue)}`; // Adjust as needed
										}

										if (redirectUrl) {
											window.location.href = redirectUrl; // Perform the redirect
										} else {
											alert('Please make a selection before proceeding.'); // Alert if no selection found
										}
									};
									</script>
                                    <div class="btn">
										<a id="nextBtn" href="register-acetabulum.html" onclick="handleNextClick()"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
									</div>
									
									<script>
									function handleNextClick() {
										const selectedValue = localStorage.getItem('selectedPlane');
										const pageName = window.location.pathname.split('/').pop(); // Extracts the current page's name
										// If there's no selection, we don't proceed.
										if (!selectedValue) {
											console.log("No selection made. Cannot proceed.");
											return; // Do not proceed without a selection.
										}
										
										// Create an object containing the event data to send
										const data = {
											selectedPlane: selectedValue,
											event: 'next_button_clicked',
											pageName: pageName // Pass the page name as part of the event data
											
										};
                                        /*
										// Send the event data to the backend via a POST request
										fetch('/api/log_event', {  // Replace '/api/log_event' with your actual FastAPI endpoint
											method: 'POST',
											headers: {
												'Content-Type': 'application/json'
											},
											body: JSON.stringify(data)
										})
										.then(response => response.json())  // Assuming a JSON response from the server
										.then(data => {
											console.log('Event logged successfully:', data);
										})
										.catch(error => {
											console.error('Error logging event:', error);
											// Optionally, you could log the error and continue with the redirection without alerting the user
										});*/
									};
									</script>
									
									
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
							   <a id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
							</li>
						   <li class="footer_btn_three">
								<a id="powerbuttonLink">
									<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
								</a>
								<script>
									document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
										event.preventDefault();  // Prevent default anchor behavior
										window.location.href = '/';  // Redirect to root path
									});
								</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>
		
	<script>
        // Function to get query parameters
        function getQueryParams() {
            const params = {};
            const queryString = window.location.search.substring(1);
            const pairs = queryString.split('&');

            pairs.forEach(pair => {
                const [key, value] = pair.split('=');
                params[key] = decodeURIComponent(value); // Decode the value
            });

            return params;
        }

        // Load the selected option when the page loads
        window.onload = function() {
            const queryParams = getQueryParams();
            const selectedValue = queryParams.selected;

            const contentDiv = document.getElementById('content');
            if (selectedValue) {
                if (selectedValue === 'coupon_question1') {
                    contentDiv.innerHTML = "<p>You selected: Anatomical plane</p>";
                    // Additional functionality for Anatomical plane
                } else if (selectedValue === 'coupon_question2') {
                    contentDiv.innerHTML = "<p>You selected: Functional plane</p>";
                    // Additional functionality for Functional plane
                } else if (selectedValue === 'coupon_question3') {
                    contentDiv.innerHTML = "<p>You selected: Both (anatomical+functional)</p>";
                    // Additional functionality for Both
                }
            } else {
                contentDiv.innerHTML = "<p>No selection was made.</p>";
            }
        };
    </script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>
<script type="text/javascript">
    let socket;

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            socket = new WebSocket('ws://127.0.0.1:8000/ws');

            socket.onopen = function(event) {
                console.log('Socket opened for ');
            };

            socket.onmessage = function(event) {
                const values = event.data.split(',');
                const abbreviation = values[0];
                const data = values[1]; 

                // Directly update the UI without delay
                if (['anterior-pelvic-1', 'anterior-pelvic-2', 'anterior-pelvic-3', 'anterior-pelvic-4'].includes(abbreviation)) {
                    $('#li-' + abbreviation.split('-')[2]).text(data);
                }

                // Check for exit condition
                if (abbreviation === 'exit') {
                    window.location.href = 'register-acetabulum.html'; // Change to your target URL
                }
            };

            socket.onerror = function(error) {
                console.error('Socket error:', error);
            };

            socket.onclose = function() {
                console.log('Socket closed, reconnecting...');
                setTimeout(() => startServer(index), 2000); // Reconnect after 1 second
            };
        }
    }
	
	function handleNavigation(event) {
		event.preventDefault();
		const targetUrl = event.currentTarget.href;

		if (socket && socket.readyState === WebSocket.OPEN) {
			socket.send(JSON.stringify({ file: " " }));
			socket.addEventListener('close', function () {
				window.location.href = targetUrl;
			}, { once: true });

			socket.close();
		} else {
			window.location.href = targetUrl;
		}
	};
	document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
    document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);
	
	// Camera functionality
    document.addEventListener('DOMContentLoaded', function() {
        const cameraButton = document.querySelector('.footer_btn_one .btn.first');
        const cameraModal = document.getElementById('cameraModal');
        const closeCamera = document.querySelector('.close-camera');
        const videoStream = document.getElementById('videoStream');
        let mediaStream = null;

        // Function to start the camera
        async function startCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });
                mediaStream = stream;
                videoStream.srcObject = stream;
                videoStream.play();
            } catch (err) {
                console.error('Error accessing camera:', err);
            }
        }

        // Function to stop the camera
        function stopCamera() {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                videoStream.srcObject = null;
                mediaStream = null;
            }
        }

        // Open camera modal
        cameraButton.addEventListener('click', function(e) {
            e.preventDefault();
            cameraModal.style.display = 'block';
            startCamera();
        });

        // Close camera modal
        closeCamera.addEventListener('click', function() {
            cameraModal.style.display = 'none';
            stopCamera();
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === cameraModal) {
                cameraModal.style.display = 'none';
                stopCamera();
            }
        });
    });

    // Start the server automatically on page load
    window.onload = function() {
        startServer('Anterior_Pelvic_and_Functional_Plane');
    };
</script>

</html>