from general.camera.MvCameraControl_class import *

import cv2
import numpy as np
import sys
from ctypes import *
# from MvCameraControl_class import *

class DualCameraOperation:
    def __init__(self):
        self.frame_rate = 132
        self.cam = None
        self.cam2 = None
        self.nPayloadSize = 0
        self.nPayloadSize2 = 0

    def initialize_cameras(self):
        deviceList = MV_CC_DEVICE_INFO_LIST()
        tlayerType = MV_GIGE_DEVICE | MV_USB_DEVICE

        # Enumerate devices
        ret = MvCamera.MV_CC_EnumDevices(tlayerType, deviceList)
        if ret != 0:
            print(f"Enum devices failed! ret[0x{ret:x}]")
            sys.exit()

        if deviceList.nDeviceNum == 0:
            print("No devices found!")
            sys.exit()

        print(f"Found {deviceList.nDeviceNum} devices!")

        # Initialize two cameras
        self.cam = MvCamera()
        self.cam2 = MvCamera()
        stDeviceList = cast(deviceList.pDeviceInfo[1], POINTER(MV_CC_DEVICE_INFO)).contents
        stDeviceList2 = cast(deviceList.pDeviceInfo[0], POINTER(MV_CC_DEVICE_INFO)).contents

        ret = self.cam.MV_CC_CreateHandle(stDeviceList)
        ret2 = self.cam2.MV_CC_CreateHandle(stDeviceList2)

        if ret != 0 or ret2 != 0:
            print("Create handle failed!")
            sys.exit()

        # Open devices
        ret = self.cam.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
        ret2 = self.cam2.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)

        if ret != 0 or ret2 != 0:
            print("Open device failed!")
            sys.exit()

        # Set camera properties like frame rate
        self.set_camera_properties(self.cam, stDeviceList)
        self.set_camera_properties(self.cam2, stDeviceList2)

    def set_camera_properties(self, camera, device_info):
        # Set packet size for GigE devices
        if device_info.nTLayerType == MV_GIGE_DEVICE:
            nPacketSize = camera.MV_CC_GetOptimalPacketSize()
            if int(nPacketSize) > 0:
                ret = camera.MV_CC_SetIntValue("GevSCPSPacketSize", nPacketSize)
                if ret != 0:
                    print(f"Warning: Set packet size failed! ret[0x{ret:x}]")
            else:
                print(f"Warning: Get packet size failed! ret[0x{nPacketSize:x}]")

        # Enable frame rate
        stBool = c_bool(True)
        ret = camera.MV_CC_GetBoolValue("AcquisitionFrameRateEnable", stBool)
        if ret != 0:
            print(f"Get AcquisitionFrameRateEnable failed! ret[0x{ret:x}]")
            sys.exit()

        # Set frame rate
        ret = camera.MV_CC_SetFloatValue("AcquisitionFrameRate", self.frame_rate)
        if ret != 0:
            print(f"Set frame rate failed! ret[0x{ret:x}]")
            sys.exit()

        # Set trigger mode to off
        ret = camera.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)
        if ret != 0:
            print(f"Set trigger mode failed! ret[0x{ret:x}]")
            sys.exit()

        # Get payload size
        stParam = MVCC_INTVALUE()
        memset(byref(stParam), 0, sizeof(MVCC_INTVALUE))

        ret = camera.MV_CC_GetIntValue("PayloadSize", stParam)
        if ret != 0:
            print(f"Get payload size failed! ret[0x{ret:x}]")
            sys.exit()

        if camera == self.cam:
            self.nPayloadSize = stParam.nCurValue
        else:
            self.nPayloadSize2 = stParam.nCurValue

    def start_grabbing(self):
        # Start grabbing for both cameras
        ret = self.cam.MV_CC_StartGrabbing()
        ret2 = self.cam2.MV_CC_StartGrabbing()

        if ret != 0 or ret2 != 0:
            print("Start grabbing failed!")
            sys.exit()

    def stop_grabbing(self):
        # Stop grabbing for both cameras
        self.cam.MV_CC_StopGrabbing()
        self.cam2.MV_CC_StopGrabbing()
    def resize_and_combine_feeds(self, img_left, img_right, window_width=int(1464/2), window_height=int(1936/2), display="both"):
        # Function to resize an image while maintaining aspect ratio
        def resize_with_aspect_ratio(image, target_width=None, target_height=None):
            h, w = image.shape[:2]
            aspect_ratio = w / h

            if target_width is not None and target_height is None:
                # Resize based on width
                new_width = target_width
                new_height = int(new_width / aspect_ratio)
            elif target_height is not None and target_width is None:
                # Resize based on height
                new_height = target_height
                new_width = int(new_height * aspect_ratio)
            else:
                raise ValueError("Only one of target_width or target_height should be specified.")

            return cv2.resize(image, (new_width, new_height))

        # Handle left image resizing
        if display == "left" or display == "both":
            img_left_resized = resize_with_aspect_ratio(img_left, target_height=window_height)

        # Handle right image resizing
        if display == "right" or display == "both":
            img_right_resized = resize_with_aspect_ratio(img_right, target_height=window_height)

        # Handle displaying based on the selected option
        if display == "left":
            combined_image = img_left_resized
        elif display == "right":
            combined_image = img_right_resized
        elif display == "both":
            # Create a separator between the images (10 pixels wide)
            separator = np.ones((window_height, 10, 3), dtype=np.uint8) * 50  # Gray bar
            # Concatenate the resized images with the separator in between
            combined_image = np.hstack((img_left_resized, separator, img_right_resized))
        else:
            raise ValueError("Invalid display option. Choose 'left', 'right', or 'both'.")

        # Ensure the combined image fits within the window size
        if combined_image.shape[1] > window_width:
            combined_image = resize_with_aspect_ratio(combined_image, target_width=window_width)
        return combined_image

    def open_combined_camera_feed(self):
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))

        data_buf = (c_ubyte * self.nPayloadSize)()
        data_buf2 = (c_ubyte * self.nPayloadSize2)()

        while True:
            # Get frame from camera 1 (left camera)
            ret = self.cam.MV_CC_GetOneFrameTimeout(byref(data_buf), self.nPayloadSize, stFrameInfo, 1000)
            img_left = None
            if ret == 0:
                img_left = np.frombuffer(data_buf, np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1)
                )

            # Get frame from camera 2 (right camera)
            ret2 = self.cam2.MV_CC_GetOneFrameTimeout(byref(data_buf2), self.nPayloadSize2, stFrameInfo, 1000)
            img_right = None
            if ret2 == 0:
                img_right = np.frombuffer(data_buf2, np.uint8).reshape(
                    (stFrameInfo.nHeight, stFrameInfo.nWidth, 1)
                )

            if img_left is not None and img_right is not None:
                # Convert to color images
                img_left = cv2.cvtColor(img_left, cv2.COLOR_GRAY2BGR)
                img_right = cv2.cvtColor(img_right, cv2.COLOR_GRAY2BGR)

                # Resize and combine feeds
                combined_image = self.resize_and_combine_feeds(img_left, img_right)

                # Display the combined image
                cv2.imshow("Combined Camera Feed", combined_image)

            if cv2.waitKey(1) & 0xFF == ord('q'):
                break

        # Release and close
        self.cam.MV_CC_StopGrabbing()
        self.cam2.MV_CC_StopGrabbing()
        self.cam.MV_CC_DestroyHandle()
        self.cam2.MV_CC_DestroyHandle()
        cv2.destroyAllWindows()


if __name__ == "__main__":
    dual_cam = DualCameraOperation()
    dual_cam.initialize_cameras()
    dual_cam.start_grabbing()
    dual_cam.open_combined_camera_feed()
    dual_cam.stop_grabbing()
