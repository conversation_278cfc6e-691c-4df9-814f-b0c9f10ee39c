<!DOCTYPE html>
<html>
<head>
    <title>LED Debug</title>
</head>
<body>
    <h1>Current LEDs</h1>
    <pre id="leds"></pre>
    <button id="calcTBtn">Calculate & Save T</button>
    <script>
        let ws = new WebSocket(`ws://${location.host}/ws`);
        ws.onopen = () => {
            ws.send(JSON.stringify({ action: "led_debug" }));
        };
        ws.onmessage = (event) => {
            document.getElementById('leds').textContent = event.data;
        };

        document.getElementById("calcTBtn").onclick = function() {
            ws.send(JSON.stringify({ action: "calculate_T" }));
        };
    </script>
</body>
</html>