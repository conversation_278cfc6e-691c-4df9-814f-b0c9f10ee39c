import numpy as np
from scipy.optimize import least_squares

# Compute transformation matrix from 3 non-collinear points (<PERSON><PERSON><PERSON> algorithm)
def compute_transformation_matrix(points):
    centroid = np.mean(points, axis=0)
    centered_points = points - centroid
    midpoint = (centered_points[0] + centered_points[1]) / 2
    x_axis = centered_points[2] - midpoint
    x_axis /= np.linalg.norm(x_axis)
    y_axis = centered_points[1] - midpoint
    z_axis = np.cross(x_axis, y_axis)
    # temp_vec = centered_points[2] - centered_points[0]
    # z_axis = np.cross(x_axis, temp_vec)
    # z_axis /= np.linalg.norm(z_axis)
    # y_axis = np.cross(z_axis, x_axis)
    R = np.vstack([x_axis, y_axis, z_axis]).T
    T = np.eye(4)
    T[:3,:3] = R
    T[:3,3] = centroid
    return T

# Transform a point by a 4x4 transformation matrix
def transform_point(T, point):
    p = np.append(point, 1)
    p_transformed = T @ p
    return p_transformed[:3]

# Cost function: sum of distances between transformed points at consecutive frames
def cost_function(cl, TR_Femur):
    total_cost = 0.0
    N = len(TR_Femur)
    for i in range(N-1):
        pos_i = transform_point(TR_Femur[i], cl)
        pos_next = transform_point(TR_Femur[i+1], cl)
        dist = np.linalg.norm(pos_next - pos_i)
        total_cost += dist
    return total_cost

# Residuals for least_squares optimizer
def residuals(cl, TR_Femur):
    res = []
    N = len(TR_Femur)
    for i in range(N-1):
        pos_i = transform_point(TR_Femur[i], cl)
        pos_next = transform_point(TR_Femur[i+1], cl)
        dist = np.linalg.norm(pos_next - pos_i)
        res.append(dist)
    return res

# Main function to estimate hip joint center
def estimate_hip_joint_center(led_coords):
    N = led_coords.shape[0]
    TR_Femur = [compute_transformation_matrix(led_coords[i]) for i in range(N)]
    initial_guess = np.mean(led_coords[0], axis=0)
    radii = [10, 30, 50]  # in mm
    num_samples_per_sphere = 10
    best_cost = np.inf
    best_hjc = None
    for r in radii:
        for _ in range(num_samples_per_sphere):
            direction = np.random.randn(3)
            direction /= np.linalg.norm(direction)
            candidate = initial_guess + r * direction
            result = least_squares(residuals, candidate, args=(TR_Femur,), max_nfev=50)
            if result.cost < best_cost:
                best_cost = result.cost
                best_hjc = result.x
    return best_hjc

# Example usage:
# led_coords = np.array(shape=(N,3,3)) # N frames, 3 LEDs, each with x,y,z
# hjc = estimate_hip_joint_center(led_coords)
# print("Estimated Hip Joint Center:", hjc)