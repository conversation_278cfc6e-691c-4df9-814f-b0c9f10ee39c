import sys
import threading
import queue
import os
from datetime import datetime

import numpy as np
import cv2
from ctypes import c_ubyte, byref, memset
from _ctypes import sizeof
from concurrent.futures import ThreadPoolExecutor

# Assuming proper camera imports
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))
from pythonProject.general.camera.CaptureCameraImage import DualCameraOperation
from pythonProject.general.camera.CameraParams_header import MV_FRAME_OUT_INFO_EX
from pythonProject.general.common.tracker_object import TriangleTracker, pixel_to_point

class KneeMarking(DualCameraOperation):
    def __init__(self):
        super().__init__()

        self.g_bExit = False
        self.frame_queue = queue.Queue(maxsize=78)
        self.num_workers = os.cpu_count() * 4  # Fallback to 4 if os.cpu_count() returns None
        print(f' self.num_workers   {self.num_workers}')
        print(f' self.num_workers   {self.num_workers}')
        print(f' self.num_workers   {self.num_workers}')
        print(f' self.num_workers   {self.num_workers}')
        # self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.num_workers)
        self.executor = ThreadPoolExecutor(max_workers=self.num_workers)

        # Initialize dual camera system
        self.dual_cam = DualCameraOperation()
        self.dual_cam.initialize_cameras()
        self.dual_cam.start_grabbing()

        self.Camera_left = self.dual_cam.cam
        self.Camera_right = self.dual_cam.cam2

        # Prepare buffers for left and right camera data
        self.data_buf_left = (c_ubyte * self.dual_cam.nPayloadSize)()
        self.data_buf_left = byref(self.data_buf_left)
        self.data_buf_right = (c_ubyte * self.dual_cam.nPayloadSize2)()
        self.data_buf_right = byref(self.data_buf_right)

        self.nPayloadSize_left = self.dual_cam.nPayloadSize
        self.nPayloadSize_right = self.dual_cam.nPayloadSize2

        self.stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(self.stFrameInfo), 0, sizeof(self.stFrameInfo))

        # Load stereo calibration parameters
        calib_path = r'D:\SpineSurgery\pythonProject\kneeSequence\marking\primary\stereo_calibration_parameters.npz'
        data = np.load(calib_path)
        self.mtxL, self.distL = data['mtxL'], data['distL']
        self.mtxR, self.distR = data['mtxR'], data['distR']
        self.R, self.T = data['R'], data['T']

    def __exit__(self, exc_type, exc_value, traceback):
        """Cleanup on exit"""
        self.g_bExit = True
        self.dual_cam.stop_grabbing()
        self.executor.shutdown(wait=True)

    def get_frames(self):
        """Retrieve frames from both left and right cameras"""
        ret_left = self.Camera_left.MV_CC_GetOneFrameTimeout(self.data_buf_left, self.nPayloadSize_left,
                                                             self.stFrameInfo, 500)
        ret_right = self.Camera_right.MV_CC_GetOneFrameTimeout(self.data_buf_right, self.nPayloadSize_right,
                                                               self.stFrameInfo, 500)

        if ret_left == 0 and ret_right == 0:
            img_left = np.frombuffer(bytes(self.data_buf_left._obj), np.uint8).reshape(
                (self.stFrameInfo.nHeight, self.stFrameInfo.nWidth, 1))
            img_right = np.frombuffer(bytes(self.data_buf_right._obj), np.uint8).reshape(
                (self.stFrameInfo.nHeight, self.stFrameInfo.nWidth, 1))
            return img_left, img_right
        return None, None

    # def find_contours_and_centroid(self, gray):

    def detect_objects(self, gray):
        """ Detects object contours in a grayscale image using thresholding and morphological operations. """
        blurred = cv2.medianBlur(gray, 5)
        # Apply Gaussian blur to smooth the image
        blurred = cv2.GaussianBlur(blurred, (5, 5), 0)

        # Compute max intensity in grayscale
        max_intensity = np.max(gray)
        if max_intensity < 150:
            return []  # Return empty list if brightness is too low

        # Apply Otsu's Thresholding
        _, thresh_image = cv2.threshold(blurred, max_intensity - 1, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # # Morphological opening to remove small noise
        # kernel = np.ones((5, 5), np.uint8)
        # morph = cv2.morphologyEx(thresh_image, cv2.MORPH_OPEN, kernel, iterations=1)

        # Find contours
        contours, _ = cv2.findContours(thresh_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        return contours

    def find_contours_and_centroid(self,image):
        """ Detects objects, applies convex hull, and fits circles using Hough Transform & minEnclosingCircle. """

        contours = self.detect_objects(image)  # Detect contours
        detections = []
        # result_hough = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)  # Convert grayscale to BGR for visualization
        # result_min_circle = result_hough.copy()

        for contour in contours:
            if cv2.contourArea(contour) < 1:
                continue  # Skip small contours

            # Compute convex hull
            # hull = cv2.convexHull(contour)

            hull = cv2.convexHull(contour)
            hull_pts = hull.squeeze().astype(np.float32)  # Shape: (N, 2)

            epsilon = 0.005 * cv2.arcLength(hull_pts, True)
            spline = cv2.approxPolyDP(hull_pts, epsilon, closed=True)
            spline_pts = spline.squeeze().astype(np.float32)  # Shape: (N, 2)
            # Refine hull vertices at subpixel level
            criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.01)
            # refined_hull = cv2.cornerSubPix(gray, hull_pts, (5, 5), (-1, -1), criteria)
            refined_hull = cv2.cornerSubPix(image, spline_pts, (5, 5), (-1, -1), criteria)
            # refined_hull_int = refined_hull.reshape(-1, 1, 2).astype(np.int32)
            # Compute Min Enclosing Circle
            center, radius = cv2.minEnclosingCircle(refined_hull)
            detections.append((center[0], 1556 - center[1]))
            # cx, cy, rr = int(center[0]), int(center[1]), int(radius)
            # cv2.circle(result_min_circle, (cx, cy), rr, (0, 255, 0), 1)  # Green circle for enclosing

        return detections

    def process_images(self, img_left, img_right):
        """Detect contours from both frames and print them"""
        centroids_left = self.find_contours_and_centroid(img_left)
        centroids_right = self.find_contours_and_centroid(img_right)

        detections_right = sorted(centroids_right, key=lambda x: x[0])
        detections_left = sorted(centroids_left, key=lambda x: x[0])
        Pointer_traker = TriangleTracker(detections_right, detections_left, img_right,
                                         img_left)
        Pointer_traker.find_depth()
        data1 = Pointer_traker.getLEDcordinates()
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]  # Precision to milliseconds
        print(f"[{timestamp}] Left Camera 3 LED Positions: {data1}")
        # print(f"[{timestamp}] Right Camera LED Positions: {centroids_right}")
        return centroids_left, centroids_right

    def worker_loop(self):
        """Worker thread that processes frames from queue"""
        while not self.g_bExit:
            try:
                img_left, img_right = self.frame_queue.get(timeout=1)
                self.process_images(img_left, img_right)
            except queue.Empty:
                continue

    def track_led(self):
        """Main loop to enqueue frames for processing"""
        # Start worker threads
        for _ in range(8):
            self.executor.submit(self.worker_loop)

        while not self.g_bExit:
            left, right = self.get_frames()
            # if left is not None and right is not None:
            #     left_resized = cv2.resize(left, (1280, 960))
            #     right_resized = cv2.resize(right, (1280, 960))
            try:
                self.frame_queue.put_nowait((left, right))
            except queue.Full:
                print(f' queue is full')
                pass  # Drop frame if queue is full

            if cv2.waitKey(1) & 0xFF == ord('q'):
                self.g_bExit = True
                break

        cv2.destroyAllWindows()


if __name__ == '__main__':
    app = KneeMarking()
    app.track_led()
