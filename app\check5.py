import pickle
from xarm.wrapper import XArmAPI
import numpy as np

arm_ip = "*************"  # Change if needed

def collect_robot_translations(num_poses=10, save_path="robot_translations.pkl"):
    arm = XArmAPI(arm_ip)
    arm.connect()
    translations = []

    print("Move the robot to each pose and press Enter to record the translation vector (x, y, z).")
    for i in range(num_poses):
        input(f"Pose {i+1}: Move robot, then press Enter to record...")
        code, pose = arm.get_position(is_radian=False)
        if code == 0:
            x, y, z = pose[:3]
            translations.append([x, y, z])
            print(f"Recorded: {x:.2f}, {y:.2f}, {z:.2f}")
        else:
            print("Failed to get pose. Try again.")
            
            i -= 1  # Retry this pose

    arm.disconnect()
    translations = np.array(translations)
    with open(save_path, "wb") as f:
        pickle.dump(translations, f)
    print(f"Saved {len(translations)} translation vectors to {save_path}")

if __name__ == "__main__":
    collect_robot_translations(num_poses=10)