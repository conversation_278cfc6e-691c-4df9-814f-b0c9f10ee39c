import numpy as np
from math import atan2, degrees

def evaluate_pointer_pose(P1, P2, P3, pitch_thresh=20.0, yaw_thresh=15.0, z_spread_thresh=4.0):
    """
    Evaluate the quality of a pointer pose using 3 IR LED 3D points.

    Returns pitch, yaw, z-spread, confidence score (0–1), and acceptability.
    """

    max_z = 290
    min_z = 270
    buffer = 10
    z_upper = max_z + buffer
    z_lower = min_z - buffer
    within_z = True

    if P1[2] > z_upper or P1[2] < z_lower or P2[2] > z_upper or P2[2] < z_lower or P3[2] > z_upper or P3[2] < z_lower:
        within_z = False
        

    top_z = (P1[2] + P2[2]) / 2
    bottom_z = P3[2]
    z_difference = top_z - bottom_z
    print(top_z, bottom_z)

    # Step 1: Compute normal vector of the pointer plane
    v1 = P2 - P1
    v2 = P3 - P1
    normal = np.cross(v1, v2)
    normal /= np.linalg.norm(normal)
    nx, ny, nz = normal

    # Step 2: Compute pitch and yaw from normal
    pitch_rad = atan2(ny, nz)
    pitch_deg = 180 + degrees(pitch_rad)

    yaw_rad = atan2(nx, nz)
    yaw_deg = 180 - degrees(yaw_rad)

    # Step 3: Compute Z-spread
    z_values = np.array([P1[2], P2[2], P3[2]])
    z_spread = np.max(z_values) - np.min(z_values)

    # Step 4: Confidence scoring
    pitch_score = max(0.0, 1.0 - abs(pitch_deg) / pitch_thresh)
    yaw_score = max(0.0, 1.0 - abs(yaw_deg) / yaw_thresh)
    z_score = max(0.0, 1.0 - z_spread / z_spread_thresh)

    confidence = (pitch_score + yaw_score + z_score) / 3.0

    # Step 5: Acceptance check
    # accepted = abs(pitch_deg) <= pitch_thresh and abs(yaw_deg) <= yaw_thresh and z_spread <= z_spread_thresh and (top_z >= bottom_z)
    accepted = within_z # (top_z >= bottom_z) and (z_difference <= 4)

    # Output summary
    result = {
        "normal_vector": normal,
        "pitch_deg": pitch_deg,
        "yaw_deg": yaw_deg,
        "z_spread_mm": z_spread,
        "confidence_score": round(confidence, 3),
        "accepted": accepted
    }

    return result



if __name__ == "__main__":
    # Example usage
    P1 = np.array([533.9274686 , 506.63135759, 274.64821929])
    P2 = np.array([541.31822737, 506.73061524, 275.6470108 ])
    P3 = np.array([537.7662068 , 496.0468458 , 278.47917015])
    result = evaluate_pointer_pose(P1, P2, P3)
    print(result)