<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Artikon</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
  <link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css" />
  <link rel="stylesheet" type="text/css" href="../static/css/style.css" />
  <link rel="stylesheet" type="text/css" href="../static/css/camera.css" />
  <style>
    #coords {
      background: #222;
      color: #fff;
      padding: 10px;
      border: 1px solid #444;
      height: 300px;
      overflow-y: auto;
      white-space: pre-wrap;
      margin: 20px;
    }
    h1 {
      text-align: center;
      margin-top: 50px;
      color: #fff;
    }
    body {
  font-family: Arial, sans-serif;
  background: #000; /* Pure black background */
  color: #0f0;
  padding: 20px;
}

  </style>
</head>
<body>

  <div class="head_wrap">
    <div class="header">
      <div class="first_head head_bg">
        Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a>
      </div>
      <div class="second_head head_bg">
        <div class="inner_head one">
          <span><img src="../static/images/calendar.png" alt="Calendar" /></span>
          <span id="date">--/--/----</span>
        </div>
        <div class="inner_head">
          <span><img src="../static/images/time.png" alt="Time" /></span>
          <span id="time">--:--:--</span>
        </div>
      </div>
      <div class="third_head head_bg">
        <span><img src="../static/images/settings.png" alt="Settings" /></span>Settings
      </div>
    </div>
  </div>

  <h1>Check Pointer 3D Coordinate</h1>
  <div id="coords">Waiting for data...</div>

  <div class="bottom_btn">
    <div class="blank"></div>
    <div class="btn">
      <a id="backBtn" href="ml-size-acquisition.html">
        <span class="mr-20"><img src="../static/images/left-arrow.png" alt="Back" /></span>Back
      </a>
    </div>
    <div class="btn">
      <a id="nextBtn" href="Free_point_collectionTibia.html">
        <span class="mr-20">Next</span><img src="../static/images/right-arrow.png" alt="Next" />
      </a>
    </div>
  </div>

  <div class="footer_wrap">
    <div class="footer">
      <ul>
        <li class="copy_right">
          © <span id="year">2023</span> Artikon AGS
          <span class="top_txt">Auto Guided Surgery</span>
        </li>
        <li class="footer_btn_one">
          <a href="#">
            <div class="btn-group" role="group" aria-label="Basic example">
              <button type="button" class="btn first"><img src="../static/images/camera-video.png" alt="Video" /></button>
              <button type="button" class="btn second">F</button>
              <button type="button" class="btn third">T</button>
            </div>
          </a>
        </li>
        <li class="footer_btn_two">
          <a href="#">
            <span><img src="../static/images/home.png" alt="Home" /></span>Main Menu
          </a>
        </li>
        <li class="footer_btn_three">
          <a href="#"><span><img src="../static/images/union.png" alt="Union" /></span></a>
        </li>
      </ul>
    </div>
  </div>

  <script>
    // Date and time updater
    function updateDateTime() {
      const now = new Date();
      document.getElementById("date").textContent = now.toLocaleDateString();
      document.getElementById("time").textContent = now.toLocaleTimeString();
      document.getElementById("year").textContent = now.getFullYear();
    }

    setInterval(updateDateTime, 1000);
    updateDateTime();

    // WebSocket logic
    const currentUrl = window.location.href;
    const pageName = currentUrl.substring(currentUrl.lastIndexOf('/') + 1).split('?')[0];
    const coordsDiv = document.getElementById('coords');
    coordsDiv.textContent = "";

    const socket = new WebSocket('ws://127.0.0.1:8000/ws');

    socket.onopen = function (event) {
      console.log("Socket opened");
      socket.send(JSON.stringify({ file: pageName }));
    };

    socket.onmessage = function (event) {
      try {
        const data = JSON.parse(event.data);
        if ("x" in data && "y" in data && "z" in data) {
          const coordText = `X: ${data.x.toFixed(2)}, Y: ${data.y.toFixed(2)}, Z: ${data.z.toFixed(2)}`;
          const line = document.createElement("div");
          line.textContent = coordText;
          coordsDiv.appendChild(line);
          coordsDiv.scrollTop = coordsDiv.scrollHeight;
        }
      } catch (e) {
        console.error("Invalid data received:", event.data);
      }
    };

    socket.onerror = function (err) {
      console.error("WebSocket error", err);
    };

    socket.onclose = function () {
      console.log("WebSocket closed");
    };
  </script>
</body>
</html>
