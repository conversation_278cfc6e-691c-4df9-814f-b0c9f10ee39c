<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="../css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="../css/style.css">
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut TC d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Tibia</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../images/knee-bone/tibia-points/1-tibia-centre(tc).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="btn green_border pointer_bg">
                                        <span class="mr-20"><img src="../images/icon/check.png" /></span>Pointer
                                    </div>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a href="multiple-page2.html"><span class="mr-20"><img src="../images/left-arrow.png" /></span>Back</a>
                                    </div>
                                    <div class="btn">
                                        <a href="tkr-screen-2.html"><span class="mr-20">Next</span><img src="../images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                Plan pin near the anterior aspect of <br />
                                the all attachment
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut TAA d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Tibia</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../images/knee-bone/tibia-points/2-tibial-ap-axis(tap).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="btn danger_border pointer_bg">
                                        <span class="mr-20"><img src="../images/icon/cross.png" /></span>Pointer
                                    </div>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a href="multiple-page2.html"><span class="mr-20"><img src="../images/left-arrow.png" /></span>Back</a>
                                    </div>
                                    <div class="btn">
                                        <a href="tkr-screen-2.html"><span class="mr-20">Next</span><img src="../images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="middle_section bone_cut MC d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Tibia</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../images/knee-bone/tibia-points/3-medial-compartment(mc).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="btn green_border pointer_bg">
                                        <span class="mr-20"><img src="../images/icon/check.png" /></span>Pointer
                                    </div>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a href="multiple-page2.html"><span class="mr-20"><img src="../images/left-arrow.png" /></span>Back</a>
                                    </div>
                                    <div class="btn">
                                        <a href="tkr-screen-2.html"><span class="mr-20">Next</span><img src="../images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="middle_section bone_cut LC d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Tibia</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../images/knee-bone/tibia-points/4-lateral-compartment(lc).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="btn green_border pointer_bg">
                                        <span class="mr-20"><img src="../images/icon/check.png" /></span>Pointer
                                    </div>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a href="multiple-page2.html"><span class="mr-20"><img src="../images/left-arrow.png" /></span>Back</a>
                                    </div>
                                    <div class="btn">
                                        <a href="tkr-screen-2.html"><span class="mr-20">Next</span><img src="../images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="middle_section bone_cut MM d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Tibia</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../images/knee-bone/tibia-points/5-medial-malleolus(mm).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="btn green_border pointer_bg">
                                        <span class="mr-20"><img src="../images/icon/check.png" /></span>Pointer
                                    </div>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a href="multiple-page2.html"><span class="mr-20"><img src="../images/left-arrow.png" /></span>Back</a>
                                    </div>
                                    <div class="btn">
                                        <a href="tkr-screen-2.html"><span class="mr-20">Next</span><img src="../images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                Place Pointer onto the most prominent<br />
                                aspect of medial malleolus.<br />
                                To record point and Press button.
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut LM d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Tibia</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../images/knee-bone/tibia-points/6-lateral-malleolus(lm).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="btn green_border pointer_bg">
                                        <span class="mr-20"><img src="../images/icon/check.png" /></span>Pointer
                                    </div>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a href="multiple-page2.html"><span class="mr-20"><img src="../images/left-arrow.png" /></span>Back</a>
                                    </div>
                                    <div class="btn">
                                        <a href="tkr-screen-2.html"><span class="mr-20">Next</span><img src="../images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                Place Pointer onto the most prominent<br />
                                aspect of lateral malleolus.<br />
                                To record point and Press button.
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut ANC d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Tibia</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../images/knee-bone/tibia-points/7-ankle-center(anc).jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="btn green_border pointer_bg">
                                        <span class="mr-20"><img src="../images/icon/check.png" /></span>Pointer
                                    </div>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a href="multiple-page2.html"><span class="mr-20"><img src="../images/left-arrow.png" /></span>Back</a>
                                    </div>
                                    <div class="btn">
                                        <a href="tkr-screen-2.html"><span class="mr-20">Next</span><img src="../images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                Place Pointer over centre of ankle.<br />
                                To record point and Press button.
                            </p>
                        </div>
                    </div>
                    <div class="check_box">
                        <div class="new">
                            <form>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question1" class="section-toggle TC" data-section="TC" />
                                    <label for="coupon_question1">Tibia Center</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question2" class="section-toggle TAA" data-section="TAA" />
                                    <label for="coupon_question2">Tibial AP Axis</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question3" class="section-toggle MC" data-section="MC" />
                                    <label for="coupon_question3">Medial Compartment</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question4" class="section-toggle LC" data-section="LC" />
                                    <label for="coupon_question4">Lateral Compartment</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question5" class="section-toggle MM" data-section="MM" />
                                    <label for="coupon_question5">Medial Malleolus</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question6" class="section-toggle LM" data-section="LM" />
                                    <label for="coupon_question6">Lateral Malleolus</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question7" class="section-toggle ANC" data-section="ANC" />
                                    <label for="coupon_question7">Ankle Center</label>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#">
                                    <span><img src="../images/home.png" /></span>Main Menu
                                </a>
                            </li>
                            <li class="footer_btn_three">
                                <a href="#">
                                    <span><img src="../images/union.png" /></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../js/common.js"></script>

<!-- Scripting code starts -->
<script>
    
    let socket;

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
        // socket = new WebSocket('ws://**************:8080/' + index);
        socket = new WebSocket('ws://127.0.0.1:8080/' + index);

        socket.onopen = function(event) {
          console.log('Socket opened for ' + index);
        };

        socket.onmessage = function(event) {
            console.log('Received message:', event.data);
            const values = event.data.split(',');

            const abbriviation = values[0];
            // Hide all sections
            $('.middle_section').addClass('d-none');
            $('.' + values[0]).removeClass('d-none');
            $('.section-toggle.' + values[0]).attr( 'checked', true );
       
        };

        socket.onerror = function(error) {
          console.error('Socket error:', error);
        };
      }
    };

    // // Function to show/hide sections and apply class to labels when checkboxes are clicked
    // $('.section-toggle').on('change', function () {
    //     var targetSection = $(this).data('section');

    //     // Hide all sections
    //     $('.middle_section').addClass('d-none');

    //     // Show the selected section
    //     if ($(this).is(':checked')) {
    //         $('.' + targetSection).removeClass('d-none');
    //     }

    //     // Add or remove the class to/from the label based on the checkbox state
    //     if ($(this).is(':checked')) {
    //         $(this).next('label').addClass('form-group-hover-label');
    //     } else {
    //         $(this).next('label').removeClass('form-group-hover-label');
    //     }
    // });

    // // Apply the class to labels for checkboxes that are checked by default
    // $('.section-toggle:checked').each(function () {
    //     $(this).next('label').addClass('form-group-hover-label');
    // });

    // // Handle the initial state of checkboxes
    // $('.section-toggle').each(function () {
    //     if ($(this).is(':checked')) {
    //         $(this).next('label').addClass('form-group-hover-label');
    //     }
    // });

    // Start the server automatically on page load
    window.onload = function() {
      startServer('tibia');
    };

</script> 
<!-- Scripting code ends -->






















































<!-- Extra js -->

<!-- <script>
    
    let socket;
    let currentIndex = 0;
    let startFetching = false; // Flag to indicate when to start fetching data

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
        // socket = new WebSocket('ws://**************:8080/' + index);
        socket = new WebSocket('ws://**************:8080/' + index);

        socket.onopen = function(event) {
          console.log('Socket opened for ' + index);
        };

        socket.onmessage = function(event) {
         
            const values = event.data.split(',');
            console.log('Received message:', values);
            const abbreviation = values[0];

            if (startFetching && ['TC', 'TAA', 'MC', 'LC', 'MM', 'LM', 'ANC'].includes(abbreviation)) {
      
                // Hide all sections
                $('.middle_section').addClass('d-none');
                $('.' + values[0]).removeClass('d-none');
                $('.section-toggle.' + values[0]).attr( 'checked', true );
            }

            if (!startFetching && abbreviation === 'TC') {
                startFetching = true; // Set flag to start fetching data when 'TC' is received
            }

            // if (event.data.startsWith('AC')) {
            //     startFetching = true; // Set flag to start fetching data
            //     console.log('true');
            // }
                
        };

        socket.onerror = function(error) {
          console.error('Socket error:', error);
        };
      }
    };

    // // Function to show/hide sections and apply class to labels when checkboxes are clicked
    // $('.section-toggle').on('change', function () {
    //     var targetSection = $(this).data('section');

    //     // Hide all sections
    //     $('.middle_section').addClass('d-none');

    //     // Show the selected section
    //     if ($(this).is(':checked')) {
    //         $('.' + targetSection).removeClass('d-none');
    //     }

    //     // Add or remove the class to/from the label based on the checkbox state
    //     if ($(this).is(':checked')) {
    //         $(this).next('label').addClass('form-group-hover-label');
    //     } else {
    //         $(this).next('label').removeClass('form-group-hover-label');
    //     }
    // });

    // // Apply the class to labels for checkboxes that are checked by default
    // $('.section-toggle:checked').each(function () {
    //     $(this).next('label').addClass('form-group-hover-label');
    // });

    // // Handle the initial state of checkboxes
    // $('.section-toggle').each(function () {
    //     if ($(this).is(':checked')) {
    //         $(this).next('label').addClass('form-group-hover-label');
    //     }
    // });

    // Start the server automatically on page load
    window.onload = function() {
      startServer();
    };

</script>
 -->




























<!-- new js -->
 <!-- <script>
    let socket;
    let currentIndex = 0;
    let delay = 1000; // Initial delay of 1 second
    let startFetching = false; // Flag to indicate when to start fetching data

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            // socket = new WebSocket('ws://**************:8080/' + index);
            socket = new WebSocket('ws://**************:8080/' + index);

            socket.onopen = function(event) {
                console.log('Socket opened for ' + index);
            };

            socket.onmessage = function(event) {
                // console.log('Received message:', event.data);
                const values = event.data.split(',');
                const abbreviation = values[0];

                if (startFetching && ['TC', 'TAA', 'MC', 'LC', 'MM', 'LM', 'ANC'].includes(abbreviation)) {
                    setTimeout(() => {
                        // Hide all sections
                        $('.middle_section').addClass('d-none');
                        $('.' + abbreviation).removeClass('d-none');
                        $('.section-toggle.' + abbreviation).attr('checked', true);
                    }, delay);

                    delay += 1000; // Increment the delay for the next message
                }

                if (abbreviation === 'AC') {
                    startFetching = true; // Set flag to start fetching data
                }
            };

            socket.onerror = function(error) {
                console.error('Socket error:', error);
            };
        }
    }

    // Start the server automatically on page load
    window.onload = function() {
        startServer('tibia');
    };
</script>  -->

<!-- new js ends -->

<!-- 
<script>
    let socket;
    let currentIndex = 0;
    let delay = 1000; // Initial delay of 1 second
    let startFetching = false; // Flag to indicate when to start fetching data

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            // socket = new WebSocket('ws://**************:8080/' + index);
            socket = new WebSocket('ws://**************:8080/' + index);

            socket.onopen = function(event) {
                console.log('Socket opened for ' + index);
            };

            socket.onmessage = function(event) {
                // console.log('Received message:', event.data);
                const values = event.data.split(',');
                const abbreviation = values[0];

                if (startFetching && ['TC', 'TAA', 'MC', 'LC', 'MM', 'LM', 'ANC'].includes(abbreviation)) {
                    setTimeout(() => {
                        // console.log("here");
                        // Get the current URL
                        var currentUrl = window.location.href;

                        // Remove the page name from the URL
                        var projectUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);
                        // console.log(projectUrl);
                        if (abbreviation == 'ANC') {
                            window.location.href = projectUrl + 'tkr-screen-2.html';
                        }
                        // Hide all sections
                        $('.middle_section').addClass('d-none');
                        $('.' + abbreviation).removeClass('d-none');
                        $('.section-toggle.' + abbreviation).attr('checked', true);
                    }, delay);

                    delay += 1000; // Increment the delay for the next message
                }

                if (abbreviation === 'AC') {
                    startFetching = true; // Set flag to start fetching data
                }
            };

            socket.onerror = function(error) {
                console.error('Socket error:', error);
            };
        }
    }

    // Start the server automatically on page load
    window.onload = function() {
        startServer('tibia');
    };
</script> -->


</html>



