import numpy as np
import os
import csv
import asyncio
from datetime import datetime
import sys

# Add parent directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from general.common.tracker_object import TriangleTracker, pixel_to_point

async def mark_new_point(marking, websocket, tool=None):
    tip_local, model_leds = load_calibration(filename=tool)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    current_dir = os.path.dirname(os.path.abspath(__file__))
    MARK_FILE = os.path.join(current_dir, "..", "..", "registration_data", f"Marking_{timestamp}.csv")
    os.makedirs(os.path.dirname(MARK_FILE), exist_ok=True)

    count = 0

    try:
        with open(MARK_FILE, mode='w', newline='') as file:
            writer = csv.writer(file)
            writer.writerow(["X", "Y", "Z"])  # CSV header

            while True:
                try:
                    if page != 'pointer.html':
                        print("Exiting mark_new_point: page is not 'pointer.html'")
                        break

                    detections_left, detections_right = await marking.handle_irq_average(n=10)
                    if len(detections_left) != 3 or len(detections_right) != 3:
                        await asyncio.sleep(0.1)
                        continue

                    detections_left = sorted(detections_left, key=lambda d: d[0])
                    detections_right = sorted(detections_right, key=lambda d: d[0])

                    femur = TriangleTracker(detections_right, detections_left)
                    tracker_plane = femur.getLEDcordinates()
                    print(tracker_plane)

                    #Calculate midpoint
                    Mid_point_RoboTracker = (np.array(tracker_plane[1]) + np.array(tracker_plane[2])) / 2

                    #Calculate vectors
                    RoboTracker_y = Mid_point_RoboTracker - tracker_plane[0]
                    RoboTracker_x = np.array(tracker_plane[1]) - np.array(tracker_plane[2])

                    # Calculate norms (for debug)
                    RoboTrackernorm_x = np.linalg.norm(RoboTracker_x)
                    RoboTrackernorm_y = np.linalg.norm(RoboTracker_y)

                    # Calculate z vector using cross product
                    RoboTracker_z = np.cross(RoboTracker_y, RoboTracker_x)

                    # Here you can use tracker_plane and the calculated vectors
                    # Add your additional processing here
                    print(RoboTracker_x)
                except Exception as e:
                    print(f"Error in processing: {e}")
                    continue

    except Exception as e:
        print(f"Error in file operations: {e}")
        return
