# import numpy as np
# from test import TriangleTracker  # Make sure this import works in your environment

# def led_marker_transformation(tracker):
#     """
#     tracker: np.array of shape (3, 3), each row is (x, y, z) of an LED in camera frame
#     Returns: 4x4 transformation matrix (marker pose in camera frame)
#     """
#     # Step 1: Compute midpoint between point 2 and point 1
#     mid_point = (tracker[2] + tracker[1]) / 2.0

#     # Step 2: Define LCS axes
#     LCS_x = tracker[2] - mid_point
#     LCS_y = mid_point - tracker[0]
#     LCS_z = np.cross(LCS_x, LCS_y)

#     # Normalize the axes
#     LCS_x /= np.linalg.norm(LCS_x)
#     LCS_y /= np.linalg.norm(LCS_y)
#     LCS_z /= np.linalg.norm(LCS_z)

#     # Re-orthogonalize LCS_y (optional but safer)
#     LCS_y = np.cross(LCS_z, LCS_x)
#     LCS_y /= np.linalg.norm(LCS_y)

#     # Step 3: Build rotation matrix
#     R = np.column_stack((LCS_x, LCS_y, <PERSON>CS_z))  # Columns are LCS axes

#     # Step 4: Build 4x4 transformation matrix
#     T = np.eye(4)
#     T[:3, :3] = R
#     T[:3, 3] = mid_point
#     return T

# # Example usage with actual detected points:
# if __name__ == "__main__":
#     # Replace this with your actual detection code!
#     detections_left = [
#         [0.0, 0.0, 0.0],
#         [0.0, 1.0, 0.0],
#         [1.0, 0.0, 0.0]
#     ]

#     detections_right = [
#         [0.1, 0.0, 0.0],
#         [0.1, 1.0, 0.0],
#         [1.1, 0.0, 0.0]
#     ]

#     tracker_obj = TriangleTracker(detections_right, detections_left)
#     led_points = tracker_obj.getLEDcordinates()  # Should return a (3, 3) numpy array

#     T_marker = led_marker_transformation(led_points)
#     print("Transformation matrix (marker in camera frame):")
#     print(T_marker)

import numpy as np
import sys
import os
import cv2

# Add parent directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from general.common.tracker_object import TriangleTracker

class RoboTrackerDetection:
    def __init__(self):
        # Initialize with empty detections
        self.detections_right = []
        self.detections_left = []
        self.tracker = None
        self.R_points = None  # Robot tracker points
        self.tracker_plane = None  # Robot tracker plane points
        
        # Load camera calibration data
        try:
            self.calibration_data = np.load(r'D:\SpineSurgery\pythonProject\kneeSequence\marking\primary\stereo_calibration_parameters.npz')
            self.camera_matrix = self.calibration_data['mtxL']
        except:
            print("Warning: Could not load camera calibration data. Using default camera matrix.")
            # Default camera matrix (you should replace these values with your actual camera parameters)
            self.camera_matrix = np.array([
                [1000, 0, 960],
                [0, 1000, 720],
                [0, 0, 1]
            ])

    def set_detections(self, detections_right, detections_left):
        """
        Set the detections for right and left cameras
        
        Args:
            detections_right: List of detection points from right camera
            detections_left: List of detection points from left camera
        """
        # Convert detections to the correct format (x, y, z)
        self.detections_right = [[float(x), float(y), float(z)] for x, y, z in detections_right]
        self.detections_left = [[float(x), float(y), float(z)] for x, y, z in detections_left]
        
        # Initialize tracker with the detections
        self.tracker = TriangleTracker(self.detections_right, self.detections_left)

    def detect_robot_tracker(self):
        """
        Detect robot tracker points and calculate tracker plane
        """
        if self.tracker is None:
            print("Error: Detections not set. Call set_detections() first.")
            return False

        # Get robot tracker points (R_points)
        self.R_points = self.tracker.getLEDcordinates()
        
        if self.R_points is not None and len(self.R_points) >= 3:
            # Calculate tracker plane based on Y-coordinate comparison
            if self.R_points[1][1] >= self.R_points[0][1]:
                self.tracker_plane = [self.R_points[2], self.R_points[0], self.R_points[1]]
            else:
                self.tracker_plane = [self.R_points[2], self.R_points[1], self.R_points[0]]
            return True
        return False

    def calculate_robottracker_vectors(self):
        """
        Calculate RoboTracker coordinate system vectors from tracker plane points.
        
        Returns:
            tuple: (RoboTracker_y, RoboTracker_x, RoboTracker_z) vectors or None if not detected
        """
        if self.tracker_plane is None:
            return None
            
        # Calculate midpoint between point1 and point2
        Mid_point_RoboTracker = (np.array(self.tracker_plane[1]) + np.array(self.tracker_plane[2])) / 2
        
        # Calculate y vector (from point0 to midpoint)
        RoboTracker_y = Mid_point_RoboTracker - self.tracker_plane[0]
        
        # Calculate x vector (from point2 to point1)
        RoboTracker_x = np.array(self.tracker_plane[1]) - np.array(self.tracker_plane[2])
        
        # Calculate z vector using cross product of y and x vectors
        RoboTracker_z = np.cross(RoboTracker_y, RoboTracker_x)
        
        return RoboTracker_y, RoboTracker_x, RoboTracker_z

# Example usage
if __name__ == "__main__":
    # Create detector instance
    detector = RoboTrackerDetection()
    
    # Example detections (replace with your actual detection data)
    # Format: [x, y, z] coordinates for each point
    detections_right = [
        [493.65, 498.59, 307.60],
        [501.33, 498.34, 307.41],
        [497.66, 487.41, 310.92]
    ]
    detections_left = [
        [492.12, 497.12, 306.12],
        [500.12, 497.12, 306.12],
        [496.12, 486.12, 309.12]
    ]
    
    # Set the detections
    detector.set_detections(detections_right, detections_left)
    
    # Detect robot tracker
    if detector.detect_robot_tracker():
        # Calculate vectors
        vectors = detector.calculate_robottracker_vectors()
        if vectors is not None:
            y, x, z = vectors
            print("RoboTracker_y:", y)
            print("RoboTracker_x:", x)
            print("RoboTracker_z:", z)
        else:
            print("Failed to calculate vectors")
    else:
        print("Failed to detect robot tracker")