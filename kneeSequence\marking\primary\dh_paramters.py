import cv2
import numpy as np
import matplotlib.pyplot as plt
# Load the image
image_path = r"D:\SpineSurgery\pythonProject\app\static\images\revision-tkr\proximalTibiacut\gun.jpg"
image = cv2.imread(image_path)



image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

# Function to display pixel value on mouse click
def on_click(event):
    if event.xdata is not None and event.ydata is not None:
        x, y = int(event.xdata), int(event.ydata)
        pixel_value = image[y, x]  # BGR format
        print(f"Pixel at ({x}, {y}): BGR={pixel_value[::-1]} (RGB={pixel_value})")

# Plot the image
fig, ax = plt.subplots()
ax.imshow(image_rgb)
ax.set_title("Click on the image to get pixel values")

# Connect mouse click event to the function
fig.canvas.mpl_connect('button_press_event', on_click)

plt.show()

# Check if the image was loaded successfully
if image is None:
    raise FileNotFoundError(f"Image not found at {image_path}")

# Replace pixels where all RGB channels are 100 with black
mask = np.any(image < 80, axis=-1)  # Mask for pixels with any channel value < 100
image[mask] = [0, 0, 0]  # Update to black in BGR format
# image[mask] = [0, 0, 0]

# Save the updated image
output_path = r"D:\SpineSurgery\pythonProject\app\static\images\revision-tkr\proximalTibiacut\image2_updated.png"
cv2.imwrite(output_path, image)

print(f"Updated image saved at {output_path}")
