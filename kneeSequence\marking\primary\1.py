import time

from xarm.wrapper import XArmAPI

# Connect to xArm6
arm = XArmAPI('*************')  # Replace with your xArm6 IP

# Enable the robot and set motion mode
arm.motion_enable(enable=True)
arm.set_mode(0)  # Position control mode
arm.set_state(0)  # Enable motion

# code, position = arm.get_position_aa()
#
# if code == 0:  # Successful retrieval
#     x, y, z, rx, ry, rz = position  # RX, RY, RZ are in radians
#     print(f"Current Position:\nX: {x} mm\nY: {y} mm\nZ: {z} mm")
#     print(f"RX: {rx} degrees\nRY: {ry} degrees\nRZ: {rz} degrees")
#     data = {"X": x, "Y": y, "Z": z, "RX": rx, "RY": ry, "RZ": rz}


    # # Define the target position using Axis-Angle representation
    # x = 345.710022   # X position in mm
    # y = -219.959961  # Y position in mm
    # z = 252.188187   # Z position in mm
    #
    # # Rotation in Axis-Angle (radians)
    # rx = -36.483259
    # ry = 126.909693
    # rz = 25.217362

    # RX = -112.788289
    # RY = 103.299821
    # RZ = 15.592187

    # Move the robot
    # axis_angle_pose = [x, y, z, RX, RY, RZ]  # List format

    # Move the robot using the corrected function call
    # arm.set_position_aa(axis_angle_pose, speed=25, mvacc=500, wait=True)

    # arm.set_servo_detach(1)
    # arm.set_servo_detach(2)
    # arm.set_servo_detach(3)

    # Print confirmation
    # print("Robot moved to the new position using Axis-Angle")
    #
    # time.sleep(60)
    #
    # arm.set_servo_attach(1)
    # arm.set_servo_attach(2)
    # arm.set_servo_attach(3)

    # arm.disconnect()




code, position = arm.get_position()

if code == 0:  # Successful retrieval
    x, y, z, rx, ry, rz = position  # RX, RY, RZ are in radians
    print(f"Current Position:\nX: {x} mm\nY: {y} mm\nZ: {z} mm")
    print(f"RX: {rx} degrees\nRY: {ry} degrees\nRZ: {rz} degrees")
    data = {"X": x, "Y": y, "Z": z, "RX": rx, "RY": ry, "RZ": rz}
    print(f'data {data}')


#
data = {'X': 152.880402, 'Y': -133.148773, 'Z': 208.222565, 'RX': -172.93482, 'RY': 36.221933, 'RZ': -69.05803}
# Ensure the values are passed correctly to the set_position method
arm.set_position(x=data['X'], y=data['Y'], z=data['Z'],
                 roll=data['RX'], pitch=data['RY'], yaw=data['RZ'])
time.sleep(6)
# arm.set_servo_detach(6)


#
#
# arm.set_servo_attach(6)
# #
# #
# #
# #
# #
#
# data= {'X': 124.43631, 'Y': -258.243256, 'Z': 197.040939, 'RX': -178.897019, 'RY': 27.701822, 'RZ': -36.879001}
#
# # Ensure the values are passed correctly to the set_position method
# arm.set_position(x=data['X'], y=data['Y'], z=data['Z'],
#                  roll=data['RX'], pitch=data['RY'], yaw=data['RZ'])
# time.sleep(8)
# arm.set_servo_detach(6)





# arm.set_servo_attach(6)