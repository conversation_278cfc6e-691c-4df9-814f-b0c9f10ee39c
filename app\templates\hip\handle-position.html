<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Artikon</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="/static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>
<body>
    <div class="tbl" id="content">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                         <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
                    <div class="middle_section bone_cut resect_proximal">
                        <div class="profile">
                            <ul>
                                <li>Acetabular Reamer/ Handle Position</li>
                                <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                </li>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Retrieve the name from local storage or default to 'Guest'
                                        const patientName = localStorage.getItem('patientName') || 'Guest';

                                        // Update the patient name display
                                        const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                        patientNameElements.forEach(element => {
                                            element.textContent = patientName;
                                        });

                                        // If there's an element with id 'greeting', update it
                                        const greetingElement = document.getElementById('greeting');
                                        if (greetingElement) {
                                            greetingElement.innerText = `Hello, ${patientName}!`;
                                        }
                                    });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center asses-box">
                                        <div class="col-8">
											<div class="row">
												<div class="grid col-6 position-relative ">
													<div class="sub-grid">
														<div class="depth btn_box_spacing raisin-black-bg ">
															<div class="depth_bg">Inclination</div>
															<div class="depth_btn">
																<ul>
																	<li id='li-1'>0</li>
																	<sup class="fs-14">0</sup>
																</ul>
															</div>
														</div>
													</div>
													<div class="cup-position-img ">
													<img src="../static/images/hip-bone/remure_position.jpg" />
													</div>
													<div class="txt left">
														M
													</div>
													<div class="txt right">
														L
													</div>
													<!-- h -->
												</div>

											<div class=" grid col-6 position-relative">
                                                <div class="sub-grid">
                                                    <div class="depth btn_box_spacing raisin-black-bg ">
                                                        <div class="depth_bg">Anteversion</div>
                                                        <div class="depth_btn">
                                                            <ul>
                                                                <li id='li-2'>0</li>
                                                                <sup class="fs-14">0</sup>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
												<div class="cup-position-img">
                                                <img src="../static/images//hip-bone/remure_position1.jpg" />
												</div>
                                                <div class="txt left">
                                                    A
                                                </div>
                                                <div class="txt right">
                                                    P
                                                </div>
                                            </div>

											</div>

                                        </div>

										<div class=" col-4">
                                                <canvas id="safeZoneChart" width="100" height="100"></canvas>
											<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
											<script>
												const ctx = document.getElementById('safeZoneChart').getContext('2d');

												// Initialize the chart
												const chart = new Chart(ctx, {
												type: 'scatter',
												data: {
													datasets: [
														{
															label: 'Safe Zone',
															data: createCircleData(15, 40, 10), // Center (15, 40), radius 10
															backgroundColor: 'rgba(0, 255, 0, 0.3)', // Semi-transparent green
															borderColor: 'red',
															borderWidth: 2,
															showLine: true,
															fill: true, // Fill the circle
															pointRadius: 0 // No individual points shown
														},
														{
															label: 'Current Position',
															data: [], // No points initially
															backgroundColor: 'red',
															pointRadius: 6,
														},
														{
															label: '', // Remove legend for the outer circle
															data: createCircleData(15, 40, 5), // Center (15, 40), radius 5
															borderColor: 'yellow',
															borderWidth: 2,
															showLine: true,
															fill: false,
															pointRadius: 0,
														},
														{
															label: '', // Remove legend for the inner circle
															data: createCircleData(15, 40, 2), // Center (15, 40), radius 2
															borderColor: 'orange',
															borderWidth: 2,
															showLine: true,
															fill: false,
															pointRadius: 0,
														},
														{
															label: '', // Remove legend for the center circle
															data: createCircleData(15, 40, 0.5), // Center (15, 40), radius 0.5 (small circle)
															borderColor: 'green',
															borderWidth: 2,
															showLine: true,
															fill: true, // Fill the center circle
															backgroundColor: 'red',
															pointRadius: 0,
														}
													]
												},
												options: {
													scales: {
														x: {
															title: { display: true, text: 'Anteversion (degrees)', color: 'white' },
															ticks: { color: '#fff' }, // Change the X axis ticks to white
															min: 0,
															max: 50,
														},
														y: {
															title: { display: true, text: 'Inclination (degrees)', color: 'white' },
															ticks: { color: '#fff' }, // Change the Y axis ticks to white
															min: 20,
															max: 70,
														}
													},
													plugins: {
														legend: {
															position: 'bottom',
															labels: {
																color: 'white', // Set the legend labels color to white
																filter: (legendItem, data) => {
																	// Exclude datasets with an empty label from the legend
																	return legendItem.text !== '';
																}
															}
														}
													},
													responsive: true,
													maintainAspectRatio: true,
													layout: {
														padding: {
															left: 5,
															right: 5,
															top: 5,
															bottom: 5
														}
													}
												}
											});

											// Function to create circle data
											function createCircleData(centerX, centerY, radius, points = 100) {
												const data = [];
												for (let i = 0; i <= points; i++) {
													const angle = (2 * Math.PI * i) / points;
													data.push({
														x: centerX + radius * Math.cos(angle),
														y: centerY + radius * Math.sin(angle),
													});
												}
												return data;
											}
											</script>


										</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="note hidden-content">
                                                <p class="note_txt">Notes:</p>
                                                <p>Press Pedal to move Robotic guide to cut plane</p>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-end">
                                                <!--div class="depth_bg">Depth</div>
                                                <div class="depth_btn">
                                                    <ul>
                                                        <li><img src="../static/images/icon/minus.png" /></li>
                                                        <li>9<span class="small-txt">mm</span></li>
                                                        <li><img src="../static/images/icon/plus.png" /></li>
                                                    </ul>
                                                </div>-->

												 <p class="diameter-desc">Reamer Diameter:
                                                    <span class="turquoise-blue me-3 ms-3">0mm</span>
                                                    <!--span><img src="../static/images/icon/minus-button.png" alt=""></span>
                                                    <span><img src="../static/images/icon/plus-button.png" alt=""></span-->
                                                </p>
												<p class="diameter-desc">Record Position
                                                    <span class="ms-3 me-5"><img src="../static/images/icon/select.png" alt=""></span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                   <div class="btn" id="targetBtn">Target Position Status</div>
                                    <style>
                                            .btn.processing {
                                            background-color: yellow !important; /* 🟡 Processing */
                                            color: black;
                                        }

                                        .btn.success {
                                            background-color: green !important; /* 🟢 Success */
                                        }

                                        .btn.error {
                                            background-color: red !important; /* 🔴 Error */
                                        }
                                    </style>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a id="backBtn" href="#" onclick="handleBackClick()"><span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back</a>
                                    </div>
									<script>
									function handleBackClick() {
										const selectedValue = localStorage.getItem('selectedPlane');
										console.log(selectedValue); // Debugging line

										let redirectUrl = '';
										if (selectedValue === 'coupon_question1') {
											redirectUrl = 'pelvis-registration.html'; // Adjust as needed
										} else if (selectedValue === 'coupon_question2') {
											redirectUrl = 'pelvis-registration.html'; // Adjust as needed
										} else if (selectedValue === 'coupon_question3') {
											redirectUrl = `anterior-pelvic.html?selectedPlane=${encodeURIComponent(selectedValue)}`; // Adjust as needed
										}

										if (redirectUrl) {
											window.location.href = redirectUrl; // Perform the redirect
										} else {
											alert('Please make a selection before proceeding.'); // Alert if no selection found
										}
									};
									</script>

									<div class="btn">
										<a id="nextBtn" href="final-cup-position.html" onclick="handleNextClick()"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
									</div>

									<!--script>
									function handleNextClick() {
										const selectedValue = localStorage.getItem('selectedPlane');
										const pageName = window.location.pathname.split('/').pop(); // Extracts the current page's name
										// If there's no selection, we don't proceed.
										if (!selectedValue) {
											console.log("No selection made. Cannot proceed.");
											return; // Do not proceed without a selection.
										}

										// Create an object containing the event data to send
										const data = {
											selectedPlane: selectedValue,
											event: 'next_button_clicked',
											pageName: pageName // Pass the page name as part of the event data

										};

										// Send the event data to the backend via a POST request
										fetch('/api/log_event', {  // Replace '/api/log_event' with your actual FastAPI endpoint
											method: 'POST',
											headers: {
												'Content-Type': 'application/json'
											},
											body: JSON.stringify(data)
										})
										.then(response => response.json())  // Assuming a JSON response from the server
										.then(data => {
											console.log('Event logged successfully:', data);
										})
										.catch(error => {
											console.error('Error logging event:', error);
											// Optionally, you could log the error and continue with the redirection without alerting the user
										});
									};
									</script-->

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../static/images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
							   <a id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
							</li>
						   <li class="footer_btn_three">
								<a id="powerbuttonLink">
									<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
								</a>
								<script>
									document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
										event.preventDefault();  // Prevent default anchor behavior
										window.location.href = '/';  // Redirect to root path
									});
								</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
	<script>
        // Function to get query parameters
        function getQueryParams() {
            const params = {};
            const queryString = window.location.search.substring(1);
            const pairs = queryString.split('&');

            pairs.forEach(pair => {
                const [key, value] = pair.split('=');
                params[key] = decodeURIComponent(value); // Decode the value
            });

            return params;
        }

        // Load the selected option when the page loads
        window.onload = function() {
            const queryParams = getQueryParams();
            const selectedValue = queryParams.selected;

            const contentDiv = document.getElementById('content');
            if (selectedValue) {
                if (selectedValue === 'coupon_question1') {
                    contentDiv.innerHTML = "<p>You selected: Anatomical plane</p>";
                    // Additional functionality for Anatomical plane
                } else if (selectedValue === 'coupon_question2') {
                    contentDiv.innerHTML = "<p>You selected: Functional plane</p>";
                    // Additional functionality for Functional plane
                } else if (selectedValue === 'coupon_question3') {
                    contentDiv.innerHTML = "<p>You selected: Both (anatomical+functional)</p>";
                    // Additional functionality for Both
                }
            } else {
                contentDiv.innerHTML = "<p>No selection was made.</p>";
            }
        };
    </script>

<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>

<script>
    let socket;

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            const currentUrl = window.location.href;
            const pageName = currentUrl.substring(currentUrl.lastIndexOf('/') + 1).split('?')[0];

            socket = new WebSocket('ws://127.0.0.1:8000/ws');

            socket.onopen = function (event) {
                console.log('Socket opened for ' + index);

                // Send the current page name to the server
                socket.send(JSON.stringify({ file: pageName }));
            };

            socket.onmessage = function (event) {
                try {
                    const values = JSON.parse(event.data);
                    console.log("Received data:", values);

                    if (typeof values !== "object") {
                        console.error("Invalid data format received:", values);
                        return;
                    }

                    // Extract values
                    const anteversion = values.antiversion || 0;
                    const inclination = values.inclination || 0;
                    const statusValue = values.status_value
                    // ✅ Update UI elements safely
                    if (typeof $ !== "undefined") {
                        $('#li-1').text(anteversion);
                        $('#li-2').text(inclination);
                        $('.turquoise-blue').text((values.diameter || "N/A") + " mm");
                    } else {
                        console.warn("jQuery not found. Skipping DOM updates.");
                    }

                    // ✅ Ensure the chart is defined before updating
                    if (typeof chart !== "undefined" && chart.data) {
                        // 🛠 Correct scatter plot update format
                        chart.data.datasets[1].data = [{ x: inclination, y: anteversion }];

                        // 🛠 Force Chart.js to recognize changes before updating
                        chart.update();
                        console.log("Chart updated with new data:", chart.data.datasets[1].data);
                    } else {
                        console.warn("Chart object not found. Skipping chart update.");
                    }

                    if (statusValue < 0.3) {
                        updateButtonState("error", "Stop Rimming!"); // 🔴 Red
                    } else if (statusValue >= 0.3 && statusValue <= 10) {
                        updateButtonState("success", "Rimming..."); // 🟢 Green
                    } else {
                        updateButtonState("processing", "N/A"); //🟡 Yellow
                    }


                    // ✅ Handle 'exit' command
                    if (values[0] === 'exit') {
                        socket.close();
                        window.location.href = 'final-cup-position.html';
                    }
                } catch (error) {
                    console.error("Error processing received data:", error);
                }
            };

            socket.onerror = function (error) {
                console.error('Socket error:', error);
            };

            socket.onclose = function () {
                console.log('Socket closed, reconnecting in 2 seconds...');
                setTimeout(() => startServer(index), 100); // Reconnect after 2 seconds
            };
        }
    }
	
	function handleNavigation(event) {
		event.preventDefault();
		const targetUrl = event.currentTarget.href;

		if (socket && socket.readyState === WebSocket.OPEN) {
			socket.send(JSON.stringify({ file: " " }));
			socket.addEventListener('close', function () {
				window.location.href = targetUrl;
			}, { once: true });

			socket.close();
		} else {
			window.location.href = targetUrl;
		}
	};
	document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
    document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);
	
	// Camera functionality
    document.addEventListener('DOMContentLoaded', function() {
        const cameraButton = document.querySelector('.footer_btn_one .btn.first');
        const cameraModal = document.getElementById('cameraModal');
        const closeCamera = document.querySelector('.close-camera');
        const videoStream = document.getElementById('videoStream');
        let mediaStream = null;

        // Function to start the camera
        async function startCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });
                mediaStream = stream;
                videoStream.srcObject = stream;
                videoStream.play();
            } catch (err) {
                console.error('Error accessing camera:', err);
            }
        }

        // Function to stop the camera
        function stopCamera() {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                videoStream.srcObject = null;
                mediaStream = null;
            }
        }

        // Open camera modal
        cameraButton.addEventListener('click', function(e) {
            e.preventDefault();
            cameraModal.style.display = 'block';
            startCamera();
        });

        // Close camera modal
        closeCamera.addEventListener('click', function() {
            cameraModal.style.display = 'none';
            stopCamera();
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === cameraModal) {
                cameraModal.style.display = 'none';
                stopCamera();
            }
        });
    });
	
    // Function to update button state
    function updateButtonState(state, text) {
        const btn = document.getElementById("targetBtn");
        btn.classList.remove("processing", "success", "error"); // Reset classes
        btn.classList.add(state); // Apply new state
        btn.innerText = text; // Update text
    }

    // Start WebSocket on page load
    window.onload = function () {
        startServer('Acetabular_Reamer_Handle_Position');
    };
</script>





</html>

