#!/usr/bin/env python
import json

import sys
import threading
import pickle
import msvcrt
import os
import cv2
import time
import numpy as np
from ctypes import *
import sys
import os

# Get the current directory of the script
current_dir = os.path.dirname(os.path.abspath(__file__))

# Construct the absolute path to 'general/camera/MvImport'
mv_import_path = os.path.join(current_dir, 'general', 'camera', 'MvImport')

# Append the constructed path to sys.path
sys.path.append(mv_import_path)

from general.camera.MvCameraControl_class import *
from general.common.tracker_object import TriangleTracker, FemureTracker
from general.common import utils
from general.common import calibration



g_bExit = False


# point_reg = [
#     'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q',
#     'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG',
#     'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV',
#     'AW', 'AX', 'AY', 'AZ', 'BA', 'BB', 'BC', 'BD', 'BE', 'BF'
# ]



point_reg = ['A', 'B', 'C', 'D']


def work_thread(cam=0, pData=0, nDataSize=0, cam2=0, pData2=0, nDataSize2=0, framecount=0):
    variable_dict = {}
    stFrameInfo = MV_FRAME_OUT_INFO_EX()
    memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))

    pointer_data=[]
    while True:
        ret = cam.MV_CC_GetOneFrameTimeout(pData, nDataSize, stFrameInfo, 500)
        ret2 = cam2.MV_CC_GetOneFrameTimeout(pData2, nDataSize2, stFrameInfo, 500)
        
        if ret == 0 and ret2 == 0:

            # Name the images left and right taking into consideration the opening streams.

            img_right = np.frombuffer(bytes(pData._obj), np.uint8).reshape((stFrameInfo.nHeight,stFrameInfo.nWidth,1))
            img_left = np.frombuffer(bytes(pData2._obj), np.uint8).reshape((stFrameInfo.nHeight,stFrameInfo.nWidth,1))
            
            # Rectify images
            
            rec_img_right, rec_img_left = calibration.rectify_images(img_right, img_left)
            
            # Detect contours on both frames

            detections_right = utils.find_contours_Kmeans(rec_img_right)
            detections_left = utils.find_contours_Kmeans(rec_img_left)

            if len(detections_right) == 6 and 6 == len(detections_left):
                detections_right = sorted(detections_right, key=lambda x: x[0])
                detections_left = sorted(detections_left, key=lambda x: x[0])
                # Extract first 3 coordinates
                first_three_right = detections_right[:3]
                first_three_left = detections_left[:3]
                next_three_right = detections_right[-3:]
                next_three_left = detections_left[-3:]

                Pointer_traker = TriangleTracker(tuple(next_three_right), tuple(next_three_left), rec_img_right,
                                                 rec_img_left)
                Pointer_traker.find_depth()
                # Leds= Pointer_traker.geLEDcordinate()

                # pointer_tip_gcs = Pointer_traker.getpointertip()
                pointer_tip_gcs = Pointer_traker.getStylusPoint()
                # print(f'{Leds} tip {pointer_tip_gcs}')
                # continue
                # pointer_data.append(pointer_tip_gcs)
                # if len(pointer_data) < 5:
                #     continue
                # pointer_tip_gcs = utils.filter_point_registration_data(pointer_data)

                femure = FemureTracker(tuple(first_three_right), tuple(first_three_left), rec_img_right, rec_img_left)
                femure.find_depth()
                F_points = femure.getall3Dcordinates()

                Tracker_plane = [F_points[2], F_points[1], F_points[0]]

                if framecount == 0:
                    variable_memory = {'C': pointer_tip_gcs, 'Plane': Tracker_plane}
                    variable_dict[point_reg[framecount]] = variable_memory

                else:
                    pointRegistered = utils.find_new_point_location(old_plane_points=Tracker_plane,
                                                                    new_plane_points=variable_dict['A']['Plane'],
                                                                    old_marked_point=pointer_tip_gcs)
                    variable_memory = {'C': pointRegistered, 'Plane': variable_dict['A']['Plane']}
                    print(f'pointRegistered {pointRegistered}   {pointer_tip_gcs}')
                variable_dict[point_reg[framecount]] = variable_memory
                utils.play_notification_sound()
                time.sleep(1)
                # pointer_data.clear()
                try:
                    with open(
                            f'C:\\Users\<USER>\\Desktop\\MK\\kneeSequence\\PrimaryRoboticKnee\\points\\{point_reg[framecount]}.json',
                            'w') as f:
                        json.dump(point_reg[framecount], f, indent=2)
                        f.close()
                except Exception as e:
                    print(f"Error while openfile")
                # time.sleep(0.05)
                framecount = framecount + 1
        else:
            print ("no data[0x%x]" % ret)
            
        if framecount==4:
            print("All points registered.")

            with open('registration_data/PointCloudbezier.pickle', 'wb') as handle:
                pickle.dump(variable_dict, handle, protocol=pickle.HIGHEST_PROTOCOL)
                
            print("Pickle dump successful")

            break
        
        if g_bExit == True:
            break  

if __name__ == "__main__":

    deviceList = MV_CC_DEVICE_INFO_LIST()
    tlayerType = MV_GIGE_DEVICE | MV_USB_DEVICE
    
    # ch:枚举设备 | en:Enum device
    ret = MvCamera.MV_CC_EnumDevices(tlayerType, deviceList)
    if ret != 0:
        print ("enum devices fail! ret[0x%x]" % ret)
        sys.exit()

    if deviceList.nDeviceNum == 0:
        print ("find no device!")
        sys.exit()

    print ("Find %d devices!" % deviceList.nDeviceNum)

    for i in range(0, deviceList.nDeviceNum):
        mvcc_dev_info = cast(deviceList.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)).contents
        if mvcc_dev_info.nTLayerType == MV_GIGE_DEVICE:
            print ("\ngige device: [%d]" % i)
            strModeName = ""
            for per in mvcc_dev_info.SpecialInfo.stGigEInfo.chModelName:
                strModeName = strModeName + chr(per)
            print ("device model name: %s" % strModeName)

            nip1 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0xff000000) >> 24)
            nip2 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x00ff0000) >> 16)
            nip3 = ((mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x0000ff00) >> 8)
            nip4 = (mvcc_dev_info.SpecialInfo.stGigEInfo.nCurrentIp & 0x000000ff)
            print ("current ip: %d.%d.%d.%d\n" % (nip1, nip2, nip3, nip4))
        elif mvcc_dev_info.nTLayerType == MV_USB_DEVICE:
            print ("\nu3v device: [%d]" % i)
            strModeName = ""
            for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chModelName:
                if per == 0:
                    break
                strModeName = strModeName + chr(per)
            print ("device model name: %s" % strModeName)

            strSerialNumber = ""
            for per in mvcc_dev_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                if per == 0:
                    break
                strSerialNumber = strSerialNumber + chr(per)
            print ("user serial number: %s" % strSerialNumber)

    nConnectionNum = 0 # For device based selections.

    if int(nConnectionNum) >= deviceList.nDeviceNum:
        print ("input error!")
        sys.exit()

    # ch:创建相机实例 | en:Creat Camera Object
    cam = MvCamera()
    cam2 = MvCamera()
    # ch:选择设备并创建句柄 | en:Select device and create handle
    stDeviceList = cast(deviceList.pDeviceInfo[int(nConnectionNum)], POINTER(MV_CC_DEVICE_INFO)).contents
    stDeviceList2 = cast(deviceList.pDeviceInfo[int(1)], POINTER(MV_CC_DEVICE_INFO)).contents

    ret = cam.MV_CC_CreateHandle(stDeviceList)
    ret2 = cam2.MV_CC_CreateHandle(stDeviceList2)
    
    if ret != 0:
        print ("create handle fail! ret[0x%x]" % ret)
        sys.exit()

    # ch:打开设备 | en:Open device
    ret = cam.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
    ret2 = cam2.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
    
    if ret != 0:
        print ("open device fail! ret[0x%x]" % ret)
        sys.exit()
    
    # ch:探测网络最佳包大小(只对GigE相机有效) | en:Detection network optimal package size(It only works for the GigE camera)
    if stDeviceList.nTLayerType == MV_GIGE_DEVICE:
        nPacketSize = cam.MV_CC_GetOptimalPacketSize()
        if int(nPacketSize) > 0:
            ret = cam.MV_CC_SetIntValue("GevSCPSPacketSize",nPacketSize)
            if ret != 0:
                print ("Warning: Set Packet Size fail! ret[0x%x]" % ret)
        else:
            print ("Warning: Get Packet Size fail! ret[0x%x]" % nPacketSize)
            
    # Second device        
    if stDeviceList2.nTLayerType == MV_GIGE_DEVICE:
        nPacketSize2 = cam2.MV_CC_GetOptimalPacketSize()
        if int(nPacketSize2) > 0:
            ret2 = cam2.MV_CC_SetIntValue("GevSCPSPacketSize",nPacketSize2)
            if ret2 != 0:
                print ("Warning: Set Packet Size fail! ret[0x%x]" % ret2)
        else:
            print ("Warning: Get Packet Size fail! ret[0x%x]" % nPacketSize2)

    stBool = c_bool(False)
    
    ret =cam.MV_CC_GetBoolValue("AcquisitionFrameRateEnable", stBool)
    ret2 =cam2.MV_CC_GetBoolValue("AcquisitionFrameRateEnable", stBool)
    
    if ret != 0:
        print ("get AcquisitionFrameRateEnable fail! ret[0x%x]" % ret)
        sys.exit()

    # ch:设置触发模式为off | en:Set trigger mode as off
    ret = cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)
    ret2 = cam2.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)
    
    if ret != 0:
        print ("set trigger mode fail! ret[0x%x]" % ret)
        sys.exit()

    # ch:获取数据包大小 | en:Get payload size
    stParam =  MVCC_INTVALUE()
    memset(byref(stParam), 0, sizeof(MVCC_INTVALUE))
	
    ret = cam.MV_CC_GetIntValue("PayloadSize", stParam)
    ret2 = cam2.MV_CC_GetIntValue("PayloadSize", stParam)
    
    if ret != 0:
       print ("get payload size fail! ret[0x%x]" % ret)
       sys.exit()
    nPayloadSize = stParam.nCurValue

    # ch:开始取流 | en:Start grab image
    ret = cam.MV_CC_StartGrabbing()
    ret2 = cam2.MV_CC_StartGrabbing()
    
    if ret != 0:
        print ("start grabbing fail! ret[0x%x]" % ret)
        sys.exit()

    data_buf = (c_ubyte * nPayloadSize)()
    data_buf2 = (c_ubyte * nPayloadSize)()

    try:
        work_thread(cam, byref(data_buf), nPayloadSize, cam2, byref(data_buf2), nPayloadSize)
        # hThreadHandle = threading.Thread(target=work_thread, args=(cam, byref(data_buf), nPayloadSize))
        # hThreadHandle.start()
    finally:
        # ch:停止取流 | en:Stop grab image
        ret = cam.MV_CC_StopGrabbing()
        ret2 = cam2.MV_CC_StopGrabbing()

        if ret != 0:
            print ("stop grabbing fail! ret[0x%x]" % ret)
            del data_buf
            sys.exit()

        # ch:关闭设备 | Close device
        ret = cam.MV_CC_CloseDevice()
        ret2 = cam2.MV_CC_CloseDevice()

        if ret != 0:
            print ("close deivce fail! ret[0x%x]" % ret)
            del data_buf
            sys.exit()

        # ch:销毁句柄 | Destroy handle
        ret = cam.MV_CC_DestroyHandle()
        ret2 = cam2.MV_CC_DestroyHandle()

        if ret != 0:
            print ("destroy handle fail! ret[0x%x]" % ret)
            del data_buf

            sys.exit()

        del data_buf
        del data_buf2

