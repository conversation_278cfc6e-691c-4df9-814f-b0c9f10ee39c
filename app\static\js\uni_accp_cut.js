const imagesByColor = {
    red: [
        '../static/images/femur/accp/PG_7_ME_Red_1.jpg',
        '../static/images/femur/accp/PG_7_ME_Red_2.jpg',
        '../static/images/femur/accp/PG_7_ME_Red_3.jpg',
        '../static/images/femur/accp/PG_7_ME_Red_4.jpg',
        '../static/images/femur/accp/PG_7_ME_Red_5.jpg',
        '../static/images/femur/accp/PG_7_ME_Red_6.jpg',
        '../static/images/femur/accp/PG_7_ME_Red_7.jpg',
        '../static/images/femur/accp/PG_7_ME_Red_8.jpg',
        '../static/images/femur/accp/PG_7_ME_Red_9.jpg',
        '../static/images/femur/accp/PG_7_ME_Red_10.jpg',
        '../static/images/femur/accp/PG_7_ME_Red_11.jpg',
    ],
    green: [
        '../static/images/femur/accp/PG_7_ME_Green.jpg'
    ],
};

function startImageAnimation(color) {
    const images = imagesByColor[color];
    if (!images) {
        console.error(`No images found for color: ${color}`);
        return; // Exit the function if no images are found
    }

    let index = 0;
    const imgElement = document.getElementById(`animated-image${color.charAt(0).toUpperCase()}${color.slice(1)}`);
    if (!imgElement) {
        console.error(`Image element not found for color: ${color}`);
        return; // Exit if the image element is not found
    }

    setInterval(() => {
        imgElement.src = images[index];
        index = (index + 1) % images.length; // Loop back to the start
    }, 500); // Change image every 1 second
}
//
//const colors = ['red', 'green'];
//
//colors.forEach(color => {
//    startImageAnimation(color);
//});
//
//
//const imagesByColor = {
//    red: [
//        '../static/images/femur/accp/PG_7_ME_Red_1.jpg',
//        '../static/images/femur/accp/PG_7_ME_Red_2.jpg',
//        '../static/images/femur/accp/PG_7_ME_Red_3.jpg',
//        '../static/images/femur/accp/PG_7_ME_Red_4.jpg',
//        '../static/images/femur/accp/PG_7_ME_Red_5.jpg',
//        '../static/images/femur/accp/PG_7_ME_Red_6.jpg',
//        '../static/images/femur/accp/PG_7_ME_Red_7.jpg',
//        '../static/images/femur/accp/PG_7_ME_Red_8.jpg',
//        '../static/images/femur/accp/PG_7_ME_Red_9.jpg',
//        '../static/images/femur/accp/PG_7_ME_Red_10.jpg',
//        '../static/images/femur/accp/PG_7_ME_Red_11.jpg',
//    ],
//    green: [
//        '../static/images/femur/accp/PG_7_ME_Green.jpg'
//    ],
//};
//
//// Function to preload images to prevent flickering
//function preloadImages(color) {
//    if (!imagesByColor[color]) return;
//
//    imagesByColor[color].forEach(src => {
//        const img = new Image();
//        img.src = src;
//    });
//}
//
//// Function to start image animation
//function startImageAnimation(color) {
//    const images = imagesByColor[color];
//
//    if (!images || images.length === 0) {
//        console.error(`❌ No images found for color: ${color}`);
//        return;
//    }
//
//    let index = 0;
//    let imgElement = document.getElementById(`animated-image-${color}`);
//
//    // Create the image element dynamically if it doesn't exist
//    if (!imgElement) {
//        console.warn(`⚠️ Image element for color '${color}' not found. Creating one...`);
//        imgElement = document.createElement("img");
//        imgElement.id = `animated-image-${color}`;
//        imgElement.alt = `${color} animation`;
//        imgElement.style.width = "150px"; // Adjust size as needed
//        imgElement.style.border = "2px solid #ccc";
//        imgElement.style.padding = "5px";
//        document.body.appendChild(imgElement); // Append to body or a specific container
//    }
//
//    // Preload images to avoid flickering
//    preloadImages(color);
//
//    // Set initial image
//    imgElement.src = images[index];
//
//    // Clear any existing interval to prevent multiple loops
//    if (imgElement.dataset.intervalId) {
//        clearInterval(imgElement.dataset.intervalId);
//    }
//
//    // Start the animation
//    const intervalId = setInterval(() => {
//        index = (index + 1) % images.length;
//        imgElement.src = images[index];
//    }, 1000); // Change image every 1 second
//
//    imgElement.dataset.intervalId = intervalId; // Store interval ID to prevent multiple intervals
//}
//
//// Start animations for all colors
//["red", "green"].forEach(color => startImageAnimation(color));
