<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Artikon</title>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
	<link rel="stylesheet" type="text/css" href="../css/bootstrap.min.css">
	<link rel="stylesheet" type="text/css" href="../css/style.css">
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul>
                                <li>Edit and confirm surgical plan before processing</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../images/profile.png" /></span>Johen Mark
                                </li>
                            </ul>
                        </div>
                        <div class="tbl tkr-screen-3-section">
                            <div class="tbl-cell">
                                <div class="row text-center">
                                    <div class="col-8">
                                       <div class="main-Bone-box">
                                            <div class="row">
                                                <div class="col-4">
                                                    <!-- <div class="position-relative">
                                                         <div class="rotate-btn">
                                                            <i class="fa-solid fa-rotate-left"></i>
                                                        </div> 
                                                    </div> -->
                                                    <div class="Bone-box-style pb-80">
                                                        <div class="Bone-text">
                                                            <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                <a class="minus-button cursor-pointer">
                                                                    <img src="../images/icon/minus-button.png" class="w-28" alt="">
                                                                </a>
                                                                <span class="text-success" id="textbox1">0.0</span>
                                                                <sup class="text-success fs-14">0</sup>
                                                                <span class="input-btn-text text-white">var</span>
                                                                <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </div>
                                                        </div> 
                                                        <img src="../images/femur/femur-cut-planning-1.png" class="img-fluid bone-img" alt="">
                                                        <!-- <img src="../images/bone/Bone-img-1.png" class="img-fluid" alt=""> -->
                                                        <div class="Bone-text bottom-input">
                                                            <div class="d-flex align-items-center">
                                                                <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between br-rounded">
                                                                    <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                    <span id="textbox2">0.0</span>
                                                                    <sup class="fs-14">0</sup>
                                                                    <span class="input-btn-text text-white">var</span>
                                                                    <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                                </div>
                                                                <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between bl-rounded">
                                                                    <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                    <span id="textbox3">0.0</span>
                                                                    <sup class="fs-14">0</sup>
                                                                    <span class="input-btn-text text-white">var</span></span>
                                                                    <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                                </div>
                                                            </div>
                                                        </div> 
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="Bone-box-style Bone-center-grid pb-80">
                                                        <div class="Bone-text ">
                                                            <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                <span class="text-danger" id="textbox4">0.0</span>
                                                                <sup class="text-danger fs-14">0</sup>
                                                                <span class="input-btn-text text-white">ext</span>
                                                                <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </div>
                                                        </div> 
                                                        <img src="../images/femur/femur-cut-planning-2.png" class="img-fluid bone-img" alt="">
                                                        <div class="Bone-text bottom-input">
                                                            <div class="d-flex align-items-center">
                                                                <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between br-rounded">
                                                                    <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                    <span id="textbox5">0.0</span>
                                                                    <sup class="fs-14">0</sup>
                                                                    <span class="input-btn-text text-white">mm</span>
                                                                    <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                                </div>
                                                                <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between bl-rounded">
                                                                    <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                    <span id="textbox6">0.0</span>
                                                                    <sup class="fs-14">0</sup>
                                                                    <span class="input-btn-text text-white">mm</span>
                                                                    <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                                </div>
                                                            </div>
                                                        </div> 
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="Bone-box-style pb-80">
                                                        <div class="Bone-text">
                                                            <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                <span id="textbox7">0.0</span>
                                                                <sup class="fs-14">0</sup>
                                                                <span class="input-btn-text text-white">Flex</span>
                                                                <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </div>
                                                        </div> 
                                                        <img src="../images/femur/femur-cut-planning-3.png" class="img-fluid bone-img" alt="">
                                                        <div class="Bone-text bottom-input">
                                                            <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                <span id="textbox8">0.0</span>
                                                                <sup class="fs-14">0</sup>
                                                                <span class="input-btn-text text-white">mm</span>
                                                                <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </div>
                                                        </div> 
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="hka-border">HKA 0.0&deg var</div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="position-relative Bone-box-style">
                                                        <img src="../images/femur/tibia-cut-planning-1.jpg" class="img-fluid bone-img" alt="">
                                                        <div class="Bone-text bottom-input single-bottom-input">
                                                            <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                <span id="textbox9">0.0</span>
                                                                <sup class="fs-14">0</sup>
                                                                <span class="input-btn-text text-white">var</span>
                                                                <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </div>
                                                        </div> 
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="position-relative Bone-box-style">
                                                        <img src="../images/femur/tibia-cut-planning-4.jpg" class="img-fluid bone-img" alt="">
                                                        <div class="Bone-text bottom-input single-bottom-input">
                                                            <div class="d-flex align-items-center">
                                                                <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between br-rounded">
                                                                    <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                    <span id="textbox10">0.0</span>
                                                                    <sup class="fs-14">0</sup>
                                                                    <span class="input-btn-text text-white">mm</span>
                                                                    <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                                </div>
                                                                <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between bl-rounded">
                                                                    <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                    <span id="textbox11">0.0</span>
                                                                    <sup class="fs-14">0</sup>
                                                                    <span class="input-btn-text text-white">mm</span>
                                                                    <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                                </div>
                                                            </div>
                                                        </div> 
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="position-relative Bone-box-style">
                                                        <img src="../images/femur/tibia-cut-planning-3.jpg" class="img-fluid bone-img" alt="">
                                                        <div class="Bone-text bottom-input single-bottom-input">
                                                            <div class="input-btn deg-input-btn d-flex align-items-center justify-content-between">
                                                                <a class="minus-button cursor-pointer"><img src="../images/icon/minus-button.png" class="w-28" alt=""></a>
                                                                <span id="textbox12">0.0</span>
                                                                <sup class="fs-14">0</sup>
                                                                <span class="input-btn-text text-white">post</span>
                                                                <a class="plus-button cursor-pointer"><img src="../images/icon/plus-button.png" class="w-28" alt=""></a>
                                                            </div>
                                                        </div> 
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="position-relative">
                                            <div class="Bone-text graph-text graph-top-left-input">
                                                <div class="input-group graph-input flex-nowrap">
                                                    <input type="text" placeholder="8.5" aria-label="" aria-describedby="" id="graphbox1">
                                                    <span class="input-group-text">mm</span>
                                                </div>
                                            </div>
                                            <div class="Bone-text graph-text graph-top-right-input">
                                                <div class="input-group graph-input flex-nowrap">
                                                    <input type="text" placeholder="9.0" aria-label="" aria-describedby="" id="graphbox2">
                                                    <span class="input-group-text">mm</span>
                                                </div>
                                            </div>
                                            <div class="Bone-text graph-text graph-bottom-left-input">
                                                <div class="input-group graph-input flex-nowrap">
                                                    <input type="text" placeholder="8.5" aria-label="" aria-describedby="" id="graphbox3">
                                                    <span class="input-group-text">mm</span>
                                                </div>
                                            </div>
                                            <div class="Bone-text graph-text graph-bottom-right-input">
                                                <div class="input-group graph-input flex-nowrap">
                                                    <input type="text" placeholder="9.0" aria-label="" aria-describedby="" id="graphbox4">
                                                    <span class="input-group-text">mm</span>
                                                </div>
                                            </div>
                                            <img src="../images/bone/Bone-image-13.png" class="img-fluid graph-img" alt="">
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="blank blank-none"></div>
                                    <div class="btn">
                                        <a href="inner-page6.html">
                                            <span class="mr-20"><img src="../images/left-arrow.png" /></span>Back
                                        </a>
                                    </div>
                                    <div class="btn">
                                        <a href="inner-page.html"><span class="mr-20">Next</span><img src="../images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul class="align-items-center">
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li>
                                <ul class="bottom-icon">
                                    <li><img src="../images/icon/icon-1.png" class="img-fluid icon-img" alt=""></li>
                                    <li><img src="../images/icon/icon-2.png" class="img-fluid icon-img active" alt=""></li>
                                    <li><img src="../images/icon/icon-3.png" class="img-fluid icon-img" alt=""></li>
                                    <li><img src="../images/icon/icon-4.png" class="img-fluid icon-img" alt=""></li>
                                </ul>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#">
                                    <span><img src="../images/home.png" /></span>Main Menu
                                </a>
                            </li>
                            <li class="footer_btn_three">
                                <a href="#">
                                    <span><img src="../images/union.png" /></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../js/common.js"></script>
<script>
    
    // let socket;

    // function startServer(index) {
    //     if (!socket || socket.readyState !== WebSocket.OPEN) {
    //     // socket = new WebSocket('ws://**************:8080/' + index);
    //     socket = new WebSocket('ws://**************:8080/' + index);

    //     socket.onopen = function(event) {
    //       console.log('Socket opened for ' + index);
    //     };

    //     socket.onmessage = function(event) {
    //         console.log('Received message:', event.data);
    //         const values = event.data.split(',');

    //         $('#textbox1').text(values[1]);
    //         $('#textbox2').text(values[2]);
    //         $('#textbox3').text(values[3]);
    //         $('#textbox4').text(values[4]);
    //         $('#textbox5').text(values[5]);
    //         $('#textbox6').text(values[6]);
    //         $('#textbox7').text(values[7]);
    //         $('#textbox8').text(values[8]);
    //         $('#textbox9').text(values[9]);
    //         $('#textbox10').text(values[10]);
    //         $('#textbox11').text(values[11]);
    //         $('#textbox12').text(values[12]);
    //         $('#graphbox1').val(values[13]);
    //         $('#graphbox2').val(values[14]);
    //         $('#graphbox3').val(values[15]);
    //         $('#graphbox4').val(values[16]);

    //     };

    //     socket.onerror = function(error) {
    //       console.error('Socket error:', error);
    //     };
    //   }
    // };




    // new js

    let socket;
    let delay = 1000; // Initial delay of 1 second
    let startFetching = false; // Flag to indicate when to start fetching data

    function startServer(index) {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
        // socket = new WebSocket('ws://**************:8080/' + index);
        socket = new WebSocket('ws://192.168.29.117:8080/' + index);

        socket.onopen = function(event) {
          console.log('Socket opened for ' + index);
        };

        socket.onmessage = function(event) {
            const values = event.data.split(',');
            const abbreviation = values[0];
            const data = values[1]; 

            if (startFetching && ['tkr-screen3-1', 'tkr-screen3-2', 'tkr-screen3-3', 'tkr-screen3-4', 
                                  'tkr-screen3-5', 'tkr-screen3-6', 'tkr-screen3-7', 'tkr-screen3-8',
                                  'tkr-screen3-9', 'tkr-screen3-10', 'tkr-screen3-11','tkr-screen3-12',
                                  'tkr-screen3-GB-1', 'tkr-screen3-GB-2', 'tkr-screen3-GB-3','tkr-screen3-GB-4'].includes(abbreviation)) {
                
                setTimeout(() => {

                    // Get the current URL
                    var currentUrl = window.location.href;
                    // Remove the page name from the URL
                    var projectUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);
                    if (abbreviation == 'tkr-screen3-GB-4') {
                        window.location.href = projectUrl + 'inner-page.html';
                    }
                
                    // Original points assiging code
                    if (abbreviation === 'tkr-screen3-1') {
                        $('#textbox1').text(data);
                    } else if (abbreviation === 'tkr-screen3-2') {
                        $('#textbox2').text(data);
                    } else if (abbreviation === 'tkr-screen3-3') {
                        $('#textbox3').text(data);
                    } else if (abbreviation === 'tkr-screen3-4') {
                        $('#textbox4').text(data);
                    } else if (abbreviation === 'tkr-screen3-5') {
                        $('#textbox5').text(data);
                    } else if (abbreviation === 'tkr-screen3-6') {
                        $('#textbox6').text(data);
                    } else if (abbreviation === 'tkr-screen3-7') {
                        $('#textbox7').text(data);
                    } else if (abbreviation === 'tkr-screen3-8') {
                        $('#textbox8').text(data);
                    } else if (abbreviation === 'tkr-screen3-9') {
                        $('#textbox9').text(data);
                    } else if (abbreviation === 'tkr-screen3-10') {
                        $('#textbox10').text(data);
                    } else if (abbreviation === 'tkr-screen3-11') {
                        $('#textbox11').text(data);
                    } else if (abbreviation === 'tkr-screen3-12') {
                        $('#textbox12').text(data);
                    } else if (abbreviation === 'tkr-screen3-GB-1') {
                        $('#graphbox1').val(data);
                    } else if (abbreviation === 'tkr-screen3-GB-2') {
                        $('#graphbox2').val(data);
                    } else if (abbreviation === 'tkr-screen3-GB-3') {
                        $('#graphbox3').val(data);
                    } else if (abbreviation === 'tkr-screen3-GB-4') {
                        $('#graphbox4').val(data);
                    }

                }, delay);
                delay += 1000; // Increment the delay for the next message
            }

            if (abbreviation === 'ROM6') {
                startFetching = true; // Set flag to start fetching data
                console.log("start fetching");
            }
        };

        socket.onerror = function(error) {
          console.error('Socket error:', error);
        };
      }
    };
    
    // new js ends





    // const values = 'KNEE_PLANNING_SCREEN,1.0,2.0,3.0,4.0,5.0,6.0,7.0,8.0,9.0,10.0,11.0,12.0,13.0,14.0,15.0,16.0'.split(',');
    // $('#textbox1').text(values[1]);
    // $('#textbox2').text(values[2]);
    // $('#textbox3').text(values[3]);
    // $('#textbox4').text(values[4]);
    // $('#textbox5').text(values[5]);
    // $('#textbox6').text(values[6]);
    // $('#textbox7').text(values[7]);
    // $('#textbox8').text(values[8]);
    // $('#textbox9').text(values[9]);
    // $('#textbox10').text(values[10]);
    // $('#textbox11').text(values[11]);
    // $('#textbox12').text(values[12]);
    // $('#graphbox1').val(values[13]);
    // $('#graphbox2').val(values[14]);
    // $('#graphbox3').val(values[15]);
    // $('#graphbox4').val(values[16]);    

    // Start the server automatically on page load
    window.onload = function() {
      startServer('knee_planning_screen');
    };

    // Click event for minus button
    $('.minus-button').on('click', function() {
        var span = $(this).closest('.input-btn').find('span').first();
        var currentValue = parseFloat(span.text());
        if (!isNaN(currentValue)) {
            span.text((currentValue - 0.5).toFixed(1));
        }
    });

    // Click event for plus button
    $('.plus-button').on('click', function() {
        var span = $(this).closest('.input-btn').find('span').first();
        var currentValue = parseFloat(span.text());
        if (!isNaN(currentValue)) {
            span.text((currentValue + 0.5).toFixed(1));
        }
    });


</script>

</html>