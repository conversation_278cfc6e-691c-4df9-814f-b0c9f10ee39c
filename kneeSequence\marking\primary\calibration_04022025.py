import cv2
import numpy as np
import glob


def stereo_calibration(images_left_path, images_right_path, chessboard_size=(9, 6), square_size=0.022):
    objp = np.zeros((chessboard_size[0] * chessboard_size[1], 3), np.float32)
    objp[:, :2] = np.mgrid[0:chessboard_size[0], 0:chessboard_size[1]].T.reshape(-1, 2) * square_size

    objpoints, imgpoints_left, imgpoints_right = [], [], []

    images_left = sorted(glob.glob(images_left_path))
    images_right = sorted(glob.glob(images_right_path))

    if len(images_left) != len(images_right):
        print("Error: The number of left and right images does not match.")
        return None

    criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)

    for img_left, img_right in zip(images_left, images_right):
        # Read as grayscale directly
        grayL = cv2.imread(img_left, cv2.IMREAD_GRAYSCALE)
        grayR = cv2.imread(img_right, cv2.IMREAD_GRAYSCALE)

        # Validate image loading
        if grayL is None or grayR is None:
            print(f"Error loading images: {img_left}, {img_right}")
            continue

        # Check image properties
        print(f"Processing {img_left}, {img_right}")
        print(f"Left Image: Shape={grayL.shape}, Dtype={grayL.dtype}")
        print(f"Right Image: Shape={grayR.shape}, Dtype={grayR.dtype}")

        # Ensure image is 8-bit
        if grayL.dtype != np.uint8:
            print(f"Converting {img_left} to 8-bit")
            grayL = cv2.convertScaleAbs(grayL)

        if grayR.dtype != np.uint8:
            print(f"Converting {img_right} to 8-bit")
            grayR = cv2.convertScaleAbs(grayR)

        # Detect chessboard corners
        retL, cornersL = cv2.findChessboardCorners(grayL, chessboard_size, None)
        retR, cornersR = cv2.findChessboardCorners(grayR, chessboard_size, None)

        if retL and retR:
            objpoints.append(objp)

            cornersL = cv2.cornerSubPix(grayL, cornersL, (11, 11), (-1, -1), criteria)
            cornersR = cv2.cornerSubPix(grayR, cornersR, (11, 11), (-1, -1), criteria)

            imgpoints_left.append(cornersL)
            imgpoints_right.append(cornersR)
        else:
            print(f"Chessboard not detected in images: {img_left}, {img_right}")

    # Monocular Calibration
    retL, mtxL, distL, _, _ = cv2.calibrateCamera(objpoints, imgpoints_left, grayL.shape[::-1], None, None)
    retR, mtxR, distR, _, _ = cv2.calibrateCamera(objpoints, imgpoints_right, grayR.shape[::-1], None, None)

    # Stereo Calibration
    flags = cv2.CALIB_FIX_INTRINSIC
    stereocalib_criteria = (cv2.TERM_CRITERIA_MAX_ITER + cv2.TERM_CRITERIA_EPS, 100, 1e-5)

    ret, mtxL, distL, mtxR, distR, R, T, E, F = cv2.stereoCalibrate(
        objpoints, imgpoints_left, imgpoints_right,
        mtxL, distL, mtxR, distR,
        grayL.shape[::-1],
        criteria=stereocalib_criteria,
        flags=flags
    )

    if not ret:
        print("Stereo calibration failed.")
        return None

    print("Stereo calibration successful!")
    print("Rotation Matrix (R):\n", R)
    print("Translation Vector (T):\n", T)
    # Save parameters to a compressed .npz file
    np.savez('stereo_calibration_parameters.npz',
             mtxL=mtxL, distL=distL,
             mtxR=mtxR, distR=distR,
             R=R, T=T)
    return mtxL, distL, mtxR, distR, R, T


# 2. Stereo Matching for Depth Estimation
def compute_depth_map(img_left, img_right):
    grayL = cv2.cvtColor(img_left, cv2.COLOR_BGR2GRAY)
    grayR = cv2.cvtColor(img_right, cv2.COLOR_BGR2GRAY)

    stereo = cv2.StereoSGBM.create(
        minDisparity=0,
        numDisparities=16 * 5,
        blockSize=5,
        P1=8 * 3 * 5 ** 2,
        P2=32 * 3 * 5 ** 2,
        disp12MaxDiff=1,
        uniquenessRatio=10,
        speckleWindowSize=100,
        speckleRange=32
    )
    disparity = stereo.compute(grayL, grayR).astype(np.float32) / 16.0
    return disparity


# 3. 3D Reconstruction



# Example usage
if __name__ == "__main__":
    right_dir = r'D:\mohan\MK\kneeSequence\PrimaryRoboticKnee\calibration_images_07022024\left'
    # right_dir = r'D:\mohan\MK\kneeSequence\PrimaryRoboticKnee\calibration_images_05022024\right'
    left_dir = r'D:\mohan\MK\kneeSequence\PrimaryRoboticKnee\calibration_images_07022024\right'

    # Assuming stereo_calibration accepts the full path with wildcard
    mtxL, distL, mtxR, distR, R, T = stereo_calibration(f'{left_dir}\\*.bmp', f'{right_dir}\\*.bmp')

    # Save parameters to a compressed .npz file
    data = np.load('stereo_calibration_parameters.npz')
    mtxL = data['mtxL']
    distL = data['distL']
    mtxR = data['mtxR']
    distR = data['distR']
    R = data['R']
    T = data['T']

    imgL = cv2.imread(f'{left_dir}\\left_000.bmp')
    imgR = cv2.imread(f'{right_dir}\\right_000.bmp')

    disparity = compute_depth_map(imgL, imgR)
    baseline = 0.508  # 508 mm = 0.508 m

    import numpy as np

    # Image dimensions
    width = imgL.shape[1]
    height = imgL.shape[0]

    # Camera parameters
    fov_horizontal = 37.88  # in degrees
    baseline = 0.508  # 508 mm = 0.508 m

    # Focal length in pixels
    focal_length = (width / 2) / np.tan(np.radians(fov_horizontal) / 2)

    Q = np.float32([[1, 0, 0, -imgL.shape[1] / 2],
                    [0, 1, 0, -imgL.shape[0] / 2],
                    [0, 0, 0, focal_length],
                    [0, 0, 1 / baseline, 0]])

    points_3D = reconstruct_3d(disparity, Q)
    print(points_3D)
    for i in points_3D:
        print(f'i {i}')

    img_size = (imgL.shape[1], imgL.shape[0])
    R1, R2, P1, P2, Q, _, _ = cv2.stereoRectify(mtxL, distL, mtxR, distR, img_size, R, T, alpha=1)

    # Undistort and rectify maps
    mapLx, mapLy = cv2.initUndistortRectifyMap(mtxL, distL, R1, P1, img_size, cv2.CV_32FC1)
    mapRx, mapRy = cv2.initUndistortRectifyMap(mtxR, distR, R2, P2, img_size, cv2.CV_32FC1)

    # Apply the maps
    rectifiedL = cv2.remap(imgL, mapLx, mapLy, cv2.INTER_LINEAR)
    rectifiedR = cv2.remap(imgR, mapRx, mapRy, cv2.INTER_LINEAR)

    # Compute disparity
    disparity = compute_depth_map(rectifiedL, rectifiedR)

    # Reconstruct 3D points
    points_3D = cv2.reprojectImageTo3D(disparity, Q)
    print(points_3D)
    # # Display 3D points
    # mask = disparity > disparity.min()
    # output_points = points_3D[mask]
    #
    # for point in output_points:
    #     print(f'3D Point: {point}')
