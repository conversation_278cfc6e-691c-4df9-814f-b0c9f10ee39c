<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
    	<link rel="stylesheet" type="text/css" href="../static/css/style.css">
    <link rel="stylesheet" type="text/css" href="../static/css/camera.css">
    <script src="../static/js/camera.js" defer></script>
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../static/images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../static/images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../static/images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page">
					<div class="middle_section bone_cut NoName">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Acetabulum Component Placement</li>
                               <li class="fs-16">
                                    <span class="mr-10">
                                        <img src="../static/images/profile.png" />
                                    </span>
                                    <span id="patientNameDisplay">Johen Mark</span>
                                    <span class="pro_label">Left Leg</span>
                               </li><script>
                                document.addEventListener('DOMContentLoaded', function() {
                                       // Retrieve the name from local storage or default to 'Guest'
                                       const patientName = localStorage.getItem('patientName') || 'Guest';

                                       // Update the patient name display
                                       const patientNameElements = document.querySelectorAll('#patientNameDisplay');
                                       patientNameElements.forEach(element => {
                                           element.textContent = patientName;
                                       });

                                      // If there's an element with id 'greeting', update it
                                       const greetingElement = document.getElementById('greeting');
                                       if (greetingElement) {
                                           greetingElement.innerText = `Hello, ${patientName}!`;
                                       }
                                   });
                                </script>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/hip-bone/revision/image.jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                Plan pin near the anterior aspect of <br />
                                the all attachment
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut ASIS d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Acetabulum Component Placement</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/hip-bone/revision/ASIS.jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                Plan pin near the anterior aspect of <br />
                                the all attachment
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut TF d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Acetabulum Component Placement</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/hip-bone/revision/Tf.jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="middle_section bone_cut PSIS d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Acetabulum Component Placement</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/hip-bone/revision/PSIS.jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="middle_section bone_cut IT d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Acetabulum Component Placement</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/hip-bone/revision/IT.jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="middle_section bone_cut TAL d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Acetabulum Component Placement</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/hip-bone/revision/TAL.jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                Place Pointer onto the most prominent<br />
                                aspect of medial malleolus.<br />
                                To record point and Press button.
                            </p>
                        </div>
                    </div>
                    <div class="middle_section bone_cut PS d-none">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Register Acetabulum Component Placement</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../static/images/profile.png" /></span>Johen Mark
                                    <span class="pro_label">Left Leg</span>
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center">
                                        <div class="col-12">
                                            <div class="grid">
                                                <img src="../static/images/hip-bone/revision/PS.jpg" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="note">
                            <p class="note_txt">Notes:</p>
                            <p>
                                Place Pointer onto the most prominent<br />
                                aspect of lateral malleolus.<br />
                                To record point and Press button.
                            </p>
                        </div>
                    </div>
                    <div class="check_box">
                        <div class="new">
                            <form>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question1" class="section-toggle ASIS" data-section="ASIS" />
                                    <label for="coupon_question1">Anterior Superior Iliac Spine</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question2" class="section-toggle TF" data-section="TF" />
                                    <label for="coupon_question2">Tuffier Point</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question3" class="section-toggle PSIS" data-section="PSIS" />
                                    <label for="coupon_question3">Posterior Superior Iliac Spine</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question4" class="section-toggle IT" data-section="IT" />
                                    <label for="coupon_question4">Ischial Tuberosity</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question5" class="section-toggle TAL" data-section="TAL" />
                                    <label for="coupon_question5">Transverse Acetabular ligament</label>
                                </div>
                                <div class="form-group">
                                    <input type="checkbox" id="coupon_question6" class="section-toggle PS" data-section="PS" />
                                    <label for="coupon_question6">Pubic Symphysis</label>
                                </div>			
                            </form>
                        </div>
                    </div>
                </div>
				
				<div class="bottom_btn">
					<div class="btn green_border pointer_bg">
						<span class="mr-20"><img src="../static/images/icon/check.png" /></span>Pointer
					</div>
                    <div class="blank"></div>
					<div class="btn">
						<a id="backBtn" href="mid-axial-body-plane.html?selected=coupon_question3"><span class="mr-20"><img src="../static/images/left-arrow.png" /></span>Back</a>
					</div>
					<div class="btn">
						<a id="nextBtn" href="result.html"><span class="mr-20">Next</span><img src="../static/images/right-arrow.png" /></a>
					</div>
				</div>
								
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <!-- <span><img src="../static/images/camera-video.png" /></span>Cemera -->
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../static/images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#" id='home'>
								<span><img src="/static/images/home.png" id="home" alt="home Image"></span>Main Menu
								</a>
								<script>
										document.getElementById('home').addEventListener('click', function(event) {
											event.preventDefault();  // Prevent default anchor behavior
											window.location.href = '../landing-page.html';  // Redirect to root path
										});
								</script>
                            </li>
                            <li class="footer_btn_three">
							<a href="#" id="powerbuttonLink">
								<span><img src="../static/images/union.png" id="powerbutton" alt="Union Image" /></span>
							</a>
							<script>
								document.getElementById('powerbuttonLink').addEventListener('click', function(event) {
									event.preventDefault();  // Prevent default anchor behavior
									window.location.href = '/';  // Redirect to root path
								});
							</script>
							</li>
                        </ul>
                    </div>
                </div>
            </div>
		</div>
    </div>
<!-- Camera Modal -->
    <div id="cameraModal" class="camera-modal">
        <div class="camera-modal-content">
            <span class="close-camera">&times;</span>
            <h2>Video Stream</h2>
            <img id="videoStream" />
        </div>
    </div>
</body>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../static/js/common.js"></script>

<!-- Scripting code starts -->





.popup {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0, 0, 0, 0.5); /* Black w/ opacity */
}

.popup-content {
    background-color: #fefefe;
    margin: 15% auto; /* 15% from the top and centered */
    padding: 20px;
    border: 1px solid #888;
    width: 80%; /* Could be more or less, depending on screen size */
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}



<!-- Popup HTML -->
<div id="popup" class="popup">
    <div class="popup-content">
        <span class="close" onclick="closePopup()">&times;</span>
        <div class="middle_section">
            <div class="main_block">
                <div class="row text-center">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-end">
                                <div class="note new-note text-start mt-3">
                                    <p class="note_txt fw-normal mb-0 turquoise-blue STEP6">C: Free Point Collection inside the Acetabulum</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../static/js/common.js"></script>
<script type="text/javascript">
    
    let socket;

    function startServer() {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            
            const currentUrl = window.location.href;
            const pageName = "rev" + currentUrl.substring(currentUrl.lastIndexOf('/') + 1).split('?')[0];
            socket = new WebSocket('ws://127.0.0.1:8000/ws');

            socket.onopen = function(event) {
                console.log('Socket opened for ');
                socket.send(JSON.stringify({ file: pageName }));
            };
			//const dataToSend = 'multiple_page'; // For example, sending screen ID or some command
            //socket.send(dataToSend);
            socket.onmessage = function(event) {
                console.log('Received message:', event.data);
                const values = event.data.split(',');
                const abbreviation = values[0];

                // Get the current URL
                var currentUrl = window.location.href;

                // Remove the page name from the URL
                var projectUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);
                
                if (abbreviation == 'exit') {
					socket.close()
                    window.location.href = projectUrl + 'revhipFreePointCollection.html';
                }

                // Hide all sections
                $('.middle_section').addClass('d-none');
                $('.' + values[0]).removeClass('d-none');
                $('.section-toggle.' + values[0]).attr('checked', true);
            };

            socket.onerror = function(error) {
                console.error('Socket error:', error);
            };
        }
    };
	
	
	    function handleNavigation(event) {
        event.preventDefault();
        const targetUrl = event.currentTarget.href;
    
        if (socket && socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({ file: " " }));
            socket.addEventListener('close', function () {
                window.location.href = targetUrl;
            }, { once: true });
    
            socket.close();
        } else {
            window.location.href = targetUrl;
        }
    };
    document.getElementById('backBtn')?.addEventListener('click', handleNavigation);
    document.getElementById('nextBtn')?.addEventListener('click', handleNavigation);
	
	// Camera functionality
    document.addEventListener('DOMContentLoaded', function() {
        const cameraButton = document.querySelector('.footer_btn_one .btn.first');
        const cameraModal = document.getElementById('cameraModal');
        const closeCamera = document.querySelector('.close-camera');
        const videoStream = document.getElementById('videoStream');
        let mediaStream = null;

        // Function to start the camera
        async function startCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                });
                mediaStream = stream;
                videoStream.srcObject = stream;
                videoStream.play();
            } catch (err) {
                console.error('Error accessing camera:', err);
            }
        }

        // Function to stop the camera
        function stopCamera() {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                videoStream.srcObject = null;
                mediaStream = null;
            }
        }

        // Open camera modal
        cameraButton.addEventListener('click', function(e) {
            e.preventDefault();
            cameraModal.style.display = 'block';
            startCamera();
        });

        // Close camera modal
        closeCamera.addEventListener('click', function() {
            cameraModal.style.display = 'none';
            stopCamera();
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === cameraModal) {
                cameraModal.style.display = 'none';
                stopCamera();
            }
        });
    });

    setInterval(function() {
        startServer();
    }, 100); // Change the interval duration as needed

</script>
</html>


