#!/usr/bin/env python3

import os
import sys
import time
from configparser import ConfigParser

# Add xArm SDK path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
from xarm.wrapper import XArmAPI

# === Load robot IP from config or user input ===
parser = ConfigParser()
parser.read('../robot.conf')
try:
    ip = parser.get('xArm', 'ip')
except:
    ip =  '*************'

# === Connect to the robot ===
arm = XArmAPI(ip)
time.sleep(0.5)
print(f"ROBOT_IP: {ip}, ROBOT_VERSION: {arm.version}")

# === Clear any warnings or errors ===
if arm.warn_code != 0:
    arm.clean_warn()
if arm.error_code != 0:
    arm.clean_error()

# === Define safety boundary (fence) in mm ===
x_min, x_max = 150, 397.3
y_min, y_max = 275, 600
z_min, z_max = 80, 300

# === Use CO3 (D3 / DO3) to control external device ===
DO_PIN = 3  # CO3

# === Control motor logic, handling NPN open-drain correctly ===
# def control_output(run_motor: bool):
#     """
#     run_motor=True  → DO HIGH/Open → Relay OFF → ✅ Motor RUN
#     run_motor=False → DO LOW/GND  → Relay ON  → ❌ Motor STOP
#     """
#     signal = 1 if run_motor else 0
#     code = arm.set_cgpio_digital(DO_PIN, signal)
#     if code == 0:
#         print(f"[DO] CO{DO_PIN} set to {'HIGH/Open (RUN)' if signal else 'LOW/GND (STOP)'}")
#     else:
#         print(f"[ERROR] Failed to set CO{DO_PIN}, Code={code}")

def control_output(run_motor: bool):
    """
    Relay is active-LOW (triggers when input = 0)
    run_motor=True  → DO LOW  → Relay ON  → Motor ON ✅
    run_motor=False → DO HIGH → Relay OFF → Motor OFF ❌
    """
    signal = 0 if run_motor else 1  # Force reverse for active-LOW
    code = arm.set_cgpio_digital(DO_PIN, signal)
    if code == 0:
        print(f"[DO] CO{DO_PIN} set to {'LOW (RUN)' if signal == 0 else 'HIGH (STOP)'}")
    else:
        print(f"[ERROR] Failed to set CO{DO_PIN}, Code={code}")




# === Fence check logic ===
def is_outside_fence(pos):
    x, y, z = pos[:3]
    return (
        x < x_min or x > x_max or
        y < y_min or y > y_max or
        z < z_min or z > z_max
    )

# === Start real-time monitoring ===
print("\n[INFO] Monitoring robot position...\n")
last_state = None

try:
    while True:
        code, pos = arm.get_position(is_radian=False)
        if code == 0 and isinstance(pos, list):
            x, y, z = pos[:3]
            outside = is_outside_fence(pos)
            print(f"[POS] X={x:.1f}, Y={y:.1f}, Z={z:.1f} → {'OUTSIDE' if outside else 'INSIDE'}")

            if not outside and last_state != 'inside':
                control_output(run_motor=True)
                last_state = 'inside'
            elif outside and last_state != 'outside':
                control_output(run_motor=False)
                last_state = 'outside'
        else:
            print("[ERROR] Could not read robot position.")
        time.sleep(0.3)

except KeyboardInterrupt:
    print("\n[INFO] Monitoring stopped by user.")

finally:
    control_output(run_motor=False)  # Stop motor safely on exit
    arm.disconnect()
    print("[INFO] Robot disconnected. Motor stopped.")
