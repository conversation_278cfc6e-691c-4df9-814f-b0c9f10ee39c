from fastapi import FastAPI
from general.camera.CaptureCameraImage import DualCameraOperation

# Global camera operation instance
camera_operation = None

def init_cameras(app: FastAPI):
    @app.on_event("startup")
    async def startup_event():
        global camera_operation
        try:
            camera_operation = DualCameraOperation()
            if not camera_operation.initialize_cameras():
                print("Failed to initialize cameras. Please check camera connections and permissions.")
                return
            camera_operation.start_grabbing()
            print("Cameras initialized successfully!")
        except Exception as e:
            print(f"Error during startup: {str(e)}")
            return

    @app.on_event("shutdown")
    async def shutdown_event():
        global camera_operation
        try:
            if camera_operation:
                camera_operation.stop_grabbing()
                print("Cameras stopped successfully!")
        except Exception as e:
            print(f"Error during shutdown: {str(e)}")

    return camera_operation 