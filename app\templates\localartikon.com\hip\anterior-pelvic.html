<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Artikon</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.1.0/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="../css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="../css/style.css">
</head>
<body>
    <div class="tbl">
        <div class="tbl-cell">
            <div class="content-area">
                <div class="head_wrap">
                    <div class="header">
                        <div class="first_head head_bg">Welcome To <a href="../index.html"><span class="bold">Artikon</span> <span class="font-color">AGS</span></a></div>
                        <div class="second_head head_bg">
                            <div class="inner_head one">
                                <span><img src="../images/calendar.png" /></span><span id="date">24/11/2022</span>
                            </div>
                            <div class="inner_head">
                                <span><img src="../images/time.png" /></span><span id="time">18:37:05</span>
                            </div>
                        </div>
                        <div class="third_head head_bg">
                            <span><img src="../images/settings.png" /></span>Settings
                        </div>
                    </div>
                </div>
                <div class="middle_section_wrap multiple_page anterior-pelvic-section">
                    <div class="middle_section bone_cut">
                        <div class="profile">
                            <ul class="position-relative">
                                <li>Anterior Pelvic and Functional Plane</li>
                                <li class="fs-16">
                                    <span class="mr-10"><img src="../images/profile.png" /></span>Johen Mark
                                </li>
                            </ul>
                        </div>
                        <div class="tbl">
                            <div class="tbl-cell">
                                <div class="main_block">
                                    <div class="row text-center justify-content-between">
                                        <div class="col-5">
                                            <div class="grid anterior-pelvic-grid ms-75">
                                                <div class="sub-grid p-0">
                                                    <div class="depth mr-13 raisin-black-bg ">
                                                        <div class="depth_bg">Anteversion</div>
                                                        <div class="depth_btn">
                                                            <ul>
                                                                <li id='li-1'>15</li>
                                                                <sup class="fs-14">0</sup>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="depth raisin-black-bg ">
                                                        <div class="depth_bg">Inclination</div>
                                                        <div class="depth_btn">
                                                            <ul>
                                                                <li id="li-2">40</li>
                                                                <sup class="fs-14">0</sup>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                                <img src="../images/hip-bone/anterior-pelvic-1.jpg" class="img-fluid" alt="">
                                            </div>
                                        </div>
                                        <div class="col-5">
                                            <div class="grid anterior-pelvic-grid me-75">
                                                <img src="../images/hip-bone/anterior-pelvic-2.jpg" class="img-fluid" alt="">
                                                <div class="sub-grid p-0">
                                                    <div class="depth raisin-black-bg  mr-13">
                                                        <div class="depth_bg">Anteversion</div>
                                                        <div class="depth_btn">
                                                            <ul>
                                                                <li id="li-3">15</li>
                                                                <sup class="fs-14">0</sup>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="depth raisin-black-bg ">
                                                        <div class="depth_bg">Inclination</div>
                                                        <div class="depth_btn">
                                                            <ul>
                                                                <li id="li-4">40</li>
                                                                <sup class="fs-14">0</sup>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                             
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="grid anterior-pelvic-img-3">
                                                <img src="../images/hip-bone/anterior-pelvic-3.jpg" class="img-fluid " alt="">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bottom_btn">
                                    <div class="btn border-0 pointer_bg">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider round"></span>
                                          </label>
                                        Auto center
                                    </div>
                                    <div class="blank"></div>
                                    <div class="btn">
                                        <a href="mid-axial-body-plane.html"><span class="mr-20"><img src="../images/left-arrow.png" /></span>Back</a>
                                    </div>
                                    <div class="btn">
                                        <a href="register-acetabulum.html"><span class="mr-20">Next</span><img src="../images/right-arrow.png" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_wrap">
                    <div class="footer">
                        <ul>
                            <li class="copy_right">
                                © <span id="year">2023</span> Artikon AGS
                                <span class="top_txt">Auto Guided Surgery</span>
                            </li>
                            <li class="footer_btn_one">
                                <a href="#">
                                    <div class="btn-group" role="group" aria-label="Basic example">
                                        <button type="button" class="btn first"><img src="../images/camera-video.png" /></button>
                                        <button type="button" class="btn second">F</button>
                                        <button type="button" class="btn third">T</button>
                                    </div>
                                </a>
                            </li>
                            <li class="footer_btn_two">
                                <a href="#">
                                    <span><img src="../images/home.png" /></span>Main Menu
                                </a>
                            </li>
                            <li class="footer_btn_three">
                                <a href="#">
                                    <span><img src="../images/union.png" /></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="../js/common.js"></script>
<script type="text/javascript">
    
    let socket;
    let delay = 1000; // Initial delay of 1 second
    let startFetching = false; // Flag to indicate when to start fetching data

    function startServer(index) {
        
        if (!socket || socket.readyState !== WebSocket.OPEN) {
        // socket = new WebSocket('ws://**************:8080/' + index);
        socket = new WebSocket('ws://**************:8080/' + index);

        socket.onopen = function(event) {
            console.log('Socket opened for ' + index);
        };

        socket.onmessage = function(event) {
            const values = event.data.split(',');
            const abbreviation = values[0];
            const data = values[1]; 
        
            if (startFetching && ['anterior-pelvic-1','anterior-pelvic-2',
                                  'anterior-pelvic-3','anterior-pelvic-4'].includes(abbreviation)) 
            {
                setTimeout(() => {

                    // Get the current URL
                    var currentUrl = window.location.href;
                    // Remove the page name from the URL
                    var projectUrl = currentUrl.substring(0, currentUrl.lastIndexOf('/') + 1);
                    if (abbreviation == 'anterior-pelvic-4') {
                        window.location.href = projectUrl + 'register-acetabulum.html';
                    }
                    // Original points assiging code
                    if (abbreviation === 'anterior-pelvic-1') {
                        $('#li-1').text(data);
                    }
                    else if (abbreviation === 'anterior-pelvic-2') {
                        $('#li-2').text(data);
                    }
                    else if (abbreviation === 'anterior-pelvic-3') {
                        $('#li-3').text(data);
                    }
                    else if (abbreviation === 'anterior-pelvic-4') {
                        $('#li-4').text(data);
                    }
                   
                }, delay);

                delay += 1000; // Increment the delay for the next message
            }

            if (abbreviation === 'RD') {
                startFetching = true; // Set flag to start fetching data
                console.log("start fetching");
            }
        };

        socket.onerror = function(error) {
            console.error('Socket error:', error);
        };
      }
    };

    //  Start the server automatically on page load
     window.onload = function() {
        startServer('Anterior_Pelvic_and_Functional_Plane');
    };

</script>
</html>