import numpy as np
import re

def compute_transformation_errors(initial_points, transformed_points):
    """
    Compute error metrics between initial and transformed point sets.

    Parameters:
    - initial_points: numpy array, shape (N, 3), initial LED points
    - transformed_points: numpy array, shape (N, 3), transformed LED points

    Returns:
    - metrics: dict with keys 'mean_error', 'rmse', 'max_error', 'min_error'
    """
    # Ensure shapes match
    assert initial_points.shape == transformed_points.shape, "Input arrays must have the same shape."

    print(initial_points)
    print(transformed_points)
    # Compute Euclidean distances between each pair of points
    errors = np.linalg.norm(transformed_points - initial_points, axis=1)

    # Compute metrics
    mean_error = np.mean(errors)
    rmse = np.sqrt(np.mean(errors**2))
    max_error = np.max(errors)
    min_error = np.min(errors)

    metrics = {
        'mean_error': mean_error,
        'rmse': rmse,
        'max_error': max_error,
        'min_error': min_error
    }
    return metrics

# Driver code
initial_leds = np.empty((0, 3))
with open('D:\\SpineSurgery\\robotic_calib_data\\robotic_init_data.txt', 'r') as file:
    for line in file:
        match = re.search(r'LED\s*\[([^\]]+)\]', line)
        numbers_str = match.group(1)
        row = np.fromstring(numbers_str, sep=' ').reshape(1, -1)
        initial_leds = np.append(initial_leds, row, axis=0)

transformed_leds = np.loadtxt('transformed_led_points_LHS_50_28_huber.txt', skiprows=1, delimiter=',')

# print(initial_leds)
# print(transformed_leds)

metrics = compute_transformation_errors(initial_leds, transformed_leds)
print('mean error = ', metrics['mean_error'])
print('rmse = ', metrics['rmse'])
print('max error = ', metrics['max_error'])
print('min error = ', metrics['min_error'])